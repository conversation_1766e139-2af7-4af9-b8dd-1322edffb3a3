import argparse
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
from typing import List, Tuple


class HashManager:
    """Handles SHA256 hash computations."""

    @staticmethod
    def compute_hash(file_path: pathlib.Path) -> str:
        """Compute SHA256 hash of a file."""
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(4096), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            print(f"Error reading {file_path}: {error}")
            return ""


class FileTree:
    """Represents the file structure in a tree format."""

    def __init__(self):
        self.root = {}

    def add_file(self, path_parts: List[str], hash_value: str):
        """Add a file to the tree structure."""
        current = self.root
        for part in path_parts[:-1]:
            current = current.setdefault(part, {})
        current[path_parts[-1]] = hash_value

    def to_markdown(self, current=None, indent=0) -> str:
        """Convert the tree structure to a markdown-formatted string."""
        if current is None:
            current = self.root
        md = ""
        for name, content in sorted(current.items()):
            if isinstance(content, dict):
                md += "    " * indent + f"- **{name}/**\n"
                md += self.to_markdown(content, indent + 1)
            else:
                hash_display = content[:8]  # Shorten the hash for display
                md += "    " * indent + f"- [{hash_display}] {name}\n"
        return md


class FileRenamer:
    """Manages the batch renaming process, including moving files."""

    def __init__(self, directory: pathlib.Path, include_subdirs: bool = True):
        self.directory = directory
        self.include_subdirs = include_subdirs

    def gather_file_hashes(self) -> List[Tuple[str, str]]:
        """Gather SHA256 hashes and relative file paths."""
        hashes = []
        for root, _, files in os.walk(self.directory):
            for filename in files:
                full_path = pathlib.Path(root) / filename
                if full_path.is_file() and os.access(full_path, os.R_OK):
                    relative_path = full_path.relative_to(self.directory).as_posix()
                    file_hash = HashManager.compute_hash(full_path)
                    if file_hash:
                        hashes.append((file_hash, relative_path))
                else:
                    print(f"Skipped: {full_path} (Not accessible or not a file)")
            if not self.include_subdirs:
                break
        return hashes

    def write_hashes_markdown(self, hashes: List[Tuple[str, str]], output_file: pathlib.Path):
        """Write hashes and filenames to a markdown file with tree structure."""
        file_tree = FileTree()
        for file_hash, filepath in hashes:
            path_parts = filepath.split('/')
            file_tree.add_file(path_parts, file_hash)

        markdown_content = file_tree.to_markdown()
        try:
            with output_file.open("w", encoding='utf-8') as file:
                file.write(markdown_content)
            print(f"File tree written to {output_file}")
        except IOError as error:
            print(f"Failed to write to {output_file}: {error}")

    def read_hashes_from_markdown(self, file_path: pathlib.Path) -> List[Tuple[str, str]]:
        """Read hashes and filenames from a markdown file."""
        hashes = []
        stack = []
        try:
            with file_path.open("r", encoding='utf-8') as file:
                for line in file:
                    stripped_line = line.lstrip(' ')
                    indent_level = (len(line) - len(stripped_line)) // 4
                    line_content = stripped_line.strip()
                    if line_content.startswith('- **') and line_content.endswith('/**'):
                        # Directory
                        dir_name = line_content[4:-3]
                        if indent_level < len(stack):
                            stack = stack[:indent_level]
                        stack.append(dir_name)
                    elif line_content.startswith('- [') and ']' in line_content:
                        # File with hash
                        hash_part, name_part = line_content[2:].split('] ', 1)
                        hash_value = hash_part.strip('[]')
                        filename = name_part.strip()
                        if indent_level < len(stack):
                            stack = stack[:indent_level]
                        filepath = '/'.join(stack + [filename])
                        hashes.append((hash_value, filepath))
            return hashes
        except IOError as error:
            print(f"Failed to read {file_path}: {error}")
            return []

    def execute_renaming(self, org_hashes: List[Tuple[str, str]], new_hashes: List[Tuple[str, str]]):
        """Execute renaming based on the new hash file, handling directory changes."""
        original_map = {hash_val: original_name for hash_val, original_name in org_hashes}
        new_map = {hash_val: new_name for hash_val, new_name in new_hashes}

        all_hashes = set(original_map.keys()) & set(new_map.keys())

        for hash_val in all_hashes:
            original_name = original_map.get(hash_val)
            new_name = new_map.get(hash_val)

            if not original_name or not new_name:
                continue  # Skip if either name is missing

            original_path = self.directory / original_name
            new_path = self.directory / new_name

            if not original_path.exists():
                print(f"Original file {original_path} does not exist. Skipping.")
                continue

            if original_path.resolve() == new_path.resolve():
                continue  # No change

            if new_path.exists():
                print(f"Target file {new_path} already exists. Conflict detected.")
                continue

            try:
                new_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.move(str(original_path), str(new_path))
                print(f"Renamed/Moved: {original_path} -> {new_path}")
            except OSError as error:
                print(f"Failed to rename/move {original_path} to {new_path}: {error}")

    def perform_renaming(self, org_file: pathlib.Path, new_file: pathlib.Path):
        """Coordinate the renaming process."""
        org_hashes = self.read_hashes_from_markdown(org_file)
        new_hashes = self.read_hashes_from_markdown(new_file)

        # Execute renaming
        self.execute_renaming(org_hashes, new_hashes)
        print("File renaming completed successfully.")


def open_in_editor(file_path: pathlib.Path):
    """Open a file in the default text editor."""
    try:
        if sys.platform.startswith('darwin'):
            subprocess.call(('open', str(file_path)))
        elif os.name == 'nt':
            os.startfile(str(file_path))
        elif os.name == 'posix':
            subprocess.call(('xdg-open', str(file_path)))
        else:
            print(f"Unsupported OS for opening files: {sys.platform}")
    except Exception as error:
        print(f"Failed to open {file_path}: {error}")


def parse_args() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Batch Rename Utility with SHA256 Verification")
    parser.add_argument("directory", type=str, help="Target directory for processing")
    parser.add_argument("--include-subdirectories", action="store_true", help="Include subdirectories in processing")
    return parser.parse_args()


def main():
    args = parse_args()

    target_dir = pathlib.Path(args.directory).resolve()
    org_file = target_dir / "FilesToText__filenames_ORG.md"
    new_file = target_dir / "FilesToText__filenames_NEW.md"

    if not target_dir.is_dir():
        print(f"Error: '{target_dir}' is not a valid directory.")
        sys.exit(1)

    renamer = FileRenamer(target_dir, include_subdirs=args.include_subdirectories)

    print(f"Generating file tree for '{target_dir}'...")

    # Generate initial hash mappings
    initial_hashes = renamer.gather_file_hashes()
    renamer.write_hashes_markdown(initial_hashes, org_file)
    renamer.write_hashes_markdown(initial_hashes, new_file)

    print("File trees generated successfully.")
    print(f"Opening '{new_file}' for editing...")
    open_in_editor(new_file)

    # Wait for user confirmation
    input("After editing the file, press Enter to proceed with renaming...")

    # Perform renaming
    renamer.perform_renaming(org_file, new_file)


if __name__ == "__main__":
    main()
