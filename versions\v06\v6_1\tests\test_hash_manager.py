import pathlib
import tempfile
import unittest
from unittest.mock import MagicMock

from src.core.hash_manager import HashManager
from src.utils.logging import Logger, LogConfig

class TestHashManager(unittest.TestCase):
    """Test cases for HashManager."""

    def setUp(self):
        self.logger = Logger(LogConfig())
        self.temp_dir = tempfile.TemporaryDirectory()
        self.test_dir = pathlib.Path(self.temp_dir.name)

    def tearDown(self):
        self.temp_dir.cleanup()

    def test_compute_sha256(self):
        """Tests SHA256 hash computation."""
        test_file = self.test_dir / "test.txt"
        test_content = "test content"
        expected_hash = "7b8df9b54c9f33c7b60a0951a9e11264"

        with test_file.open('w') as f:
            f.write(test_content)

        result = HashManager.compute_sha256(test_file, self.logger)
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 64)  # SHA256 is 64 characters

    def test_compute_sha256_nonexistent_file(self):
        """Tests hash computation for non-existent file."""
        result = HashManager.compute_sha256(
            self.test_dir / "nonexistent.txt",
            self.logger
        )
        self.assertIsNone(result)

    def test_verify_hash(self):
        """Tests hash verification."""
        test_file = self.test_dir / "test.txt"
        test_content = "test content"

        with test_file.open('w') as f:
            f.write(test_content)

        hash_value = HashManager.compute_sha256(test_file, self.logger)
        self.assertTrue(
            HashManager.verify_hash(test_file, hash_value, self.logger)
        )

    def test_verify_hash_mismatch(self):
        """Tests hash verification with mismatched hash."""
        test_file = self.test_dir / "test.txt"
        test_content = "test content"

        with test_file.open('w') as f:
            f.write(test_content)

        self.assertFalse(
            HashManager.verify_hash(
                test_file,
                "invalid_hash",
                self.logger
            )
        )

if __name__ == '__main__':
    unittest.main()
