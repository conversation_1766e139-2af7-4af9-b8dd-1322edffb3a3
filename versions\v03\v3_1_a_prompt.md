
please adress the issue that arise when e.g. changing the folder-names'


issue: if the user make changes to both the directoryname and the filename it will effectively corrupt the data. example:
FilesToText__filenames_ORG.txt:
```
42309a312e7c75dbfdd3ec9e681cecea6678dd4ba6aeea170307363292b22d98|v2_4_a.py
414cf5671c5c92c127b9283d9e24ff0d1095533d142d3473d31340ab8ccec022|TESTFOLDER\Q2_2024_ResizePages.webm
0806ce2a7f7030c3077ae24ef6d94680f9b2c3e9c7a115c9ea5616a947d13ada|TESTFOLDER\Q3_2022_PrepareAForm.webm
b88695c02c73aadb94790caf0e71cd4f5748aaf63a695f977e04f9dc66e8834b|TESTFOLDER\SelectEditMenu.webm
85fd58a59683e215f16a1d98aa871d3b68d723a927c3561a72e9d6ae27a9576f|TESTFOLDER\SelectFinish.webm
```
FilesToText__filenames_NEW.txt:
```
42309a312e7c75dbfdd3ec9e681cecea6678dd4ba6aeea170307363292b22d98|v2_4_a.py
414cf5671c5c92c127b9283d9e24ff0d1095533d142d3473d31340ab8ccec022|testdir\pfx_Q2_2024_ResizePages.webm
0806ce2a7f7030c3077ae24ef6d94680f9b2c3e9c7a115c9ea5616a947d13ada|testdir\pfx_Q3_2022_PrepareAForm.webm
b88695c02c73aadb94790caf0e71cd4f5748aaf63a695f977e04f9dc66e8834b|TESTFOLDER\pfx_SelectEditMenu.webm
85fd58a59683e215f16a1d98aa871d3b68d723a927c3561a72e9d6ae27a9576f|TESTFOLDER\pfx_SelectFinish.webm
```


one way to adress this issue is to regenerate the hashtable and refetch the filestructure before starting to apply the new filenames, this method also allow for visualizing the changes in dryruns
