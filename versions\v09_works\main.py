""" Utility for batch renaming files through an intermediary text editor (Class-based structure, Loguru-based logging). """

import argparse
import fnmatch
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
import re
import unicodedata
import time
import filetype
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

from dataclasses import dataclass
from enum import Enum

# Rich imports for console interactions
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.box import ROUNDED

# Loguru for logging
from loguru import logger


# =======================================================
# Configuration
# =======================================================
class Config:
    """
    Holds default settings for the Batch Rename Utility.
    """
    USE_DEFAULT_SETTINGS = True
    DEFAULT_CLEANUP_LOGS = True
    DEFAULT_INCLUDE_SUBDIRECTORIES = False
    DEFAULT_INCLUDE_TIMESTAMP = True
    DEFAULT_INCLUDE_DEPTH = True
    DEFAULT_INCLUDE_SIZE = True
    DEFAULT_INCLUDE_CONTENT = True
    DEFAULT_CONTENT_PREVIEW_LENGTH = 300

    EXCLUDED_DIRS = [
        ".backups", ".git", ".github", ".venv", ".versions",
        "__pycache__", "__tmp__", "node_modules", "venv",
    ]

    EXCLUDED_PATTERNS = [
        "*.pyc", "*.sublime-workspace",
    ]


# =======================================================
# Logger Setup
# =======================================================
class LoggerSetup:
    """Sets up Loguru logger to write YAML logs to a file."""

    @staticmethod
    def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
        """
        Configure Loguru to log messages in YAML format to `log_file`.
        """
        def yaml_sink(message):
            record = message.record

            time_str = record['time'].strftime('%Y-%m-%d %H:%M:%S')
            level_str = f"!{record['level'].name}"
            name_str = record['name']
            func_name_str = f"*{record['function']}"
            line_no = record["line"]
            msg = record["message"]

            # For multi-line messages, use a block scalar in YAML
            if "\n" in msg:
                lines = msg.split("\n")
                message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
            else:
                # Quote if it has special characters like ':'
                if ":" in msg:
                    message_str = f"'{msg}'"
                else:
                    message_str = msg

            yaml_lines = [
                f"- time: {time_str}",
                f"  level: {level_str}",
                f"  name: {name_str}",
                f"  funcName: {func_name_str}",
                f"  lineno: {line_no}",
                f"  message: {message_str}",
                ""
            ]

            with open(log_file, "a", encoding="utf-8") as f:
                f.write("\n".join(yaml_lines) + "\n")

        # Remove default handlers and add our YAML sink
        logger.remove()
        logger.add(yaml_sink, level=level, enqueue=True)

    @staticmethod
    def initialize_logging(verbosity: str = "INFO"):
        """
        Initialize YAML logging with a level mapped from the string `verbosity`.
        Maps:
          quiet -> ERROR
          normal -> INFO
          verbose -> DEBUG
          debug -> DEBUG
        """
        level_map = {
            "quiet": "ERROR",
            "normal": "INFO",
            "verbose": "DEBUG",
            "debug": "DEBUG"
        }
        selected_level = level_map.get(verbosity.lower(), "INFO")
        LoggerSetup.setup_yaml_logging(level=selected_level)


# =======================================================
# Argument Handler
# =======================================================
class ArgumentHandler:
    """Handles CLI argument parsing and optional prompting."""

    def __init__(self):
        self.parser = self.parse_arguments()

    @staticmethod
    def parse_arguments():
        logger.debug("Setting up argument parser.")
        parser = argparse.ArgumentParser(
            description="Batch Rename Utility with SHA256 Verification (using Loguru + Rich)."
        )

        parser.add_argument('-d', '--directory', type=str, help="Target directory for processing")

        parser.add_argument('--include_subdirs', action='store_true',
                            default=Config.DEFAULT_INCLUDE_SUBDIRECTORIES,
                            help="Include subdirectories in file processing")

        parser.add_argument('--include-depth', action='store_true',
                            default=Config.DEFAULT_INCLUDE_DEPTH,
                            help="Include a folder depth column (integer).")

        parser.add_argument('--include-size', action='store_true',
                            default=Config.DEFAULT_INCLUDE_SIZE,
                            help="Include a file size column in KB.")

        parser.add_argument('--include-time', action='store_true',
                            default=Config.DEFAULT_INCLUDE_TIMESTAMP,
                            help="Include 'HH:MM' in the date column (e.g. YYYY.MM.DD HH:MM).")

        parser.add_argument('--include-content', action='store_true',
                            default=Config.DEFAULT_INCLUDE_CONTENT,
                            help="Include a preview of text file content (not applied to binary files).")

        parser.add_argument('--content-length', type=int,
                           default=Config.DEFAULT_CONTENT_PREVIEW_LENGTH,
                           help=f"Maximum length of content preview (default: {Config.DEFAULT_CONTENT_PREVIEW_LENGTH} chars).")

        parser.add_argument('--prompt', action='store_true', help="Prompt for missing arguments")

        parser.add_argument('-v', '--verbosity',
                            choices=["quiet", "normal", "verbose", "debug"],
                            default="normal",
                            help="Set output verbosity level")

        # Log Cleanup arguments
        cleanup_logs_group = parser.add_mutually_exclusive_group()
        cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true',
                                        help="Clean up log files after successful execution")
        cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false',
                                        help="Do not clean up log files after successful execution")
        parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)

        logger.debug("Argument parser setup complete.")
        return parser

    def get_arguments(self):
        return self.parser.parse_args()

    def prompt_for_missing_arguments(self, args):
        logger.debug("Prompting for missing arguments.")
        console = Console()

        def print_section(title):
            console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)

        if args.prompt:
            print_section("Default Settings")
            use_defaults = Confirm.ask("Use default settings?", default=Config.USE_DEFAULT_SETTINGS)
            logger.debug(f"Use defaults: {use_defaults}")

            if not use_defaults:
                # Prompt for input directory
                print_section("Directory")
                current_dir = args.directory or ""
                args.directory = Prompt.ask("Target directory path?", default=current_dir).strip()

                # Prompt for including subdirectories
                print_section("Include Subdirectories?")
                args.include_subdirs = Confirm.ask("Include subdirectories?", default=True)

                # Prompt for including depth
                print_section("Include Folder Depth?")
                args.include_depth = Confirm.ask("Include folder depth column?", default=True)

                # Prompt for including size
                print_section("Include File Size?")
                args.include_size = Confirm.ask("Include file size column in KB?", default=True)

                # Prompt for including timestamps
                print_section("Include Time?")
                args.include_time = Confirm.ask("Include HH:MM in the date column?", default=True)

                # Prompt for including content
                print_section("Include Content Preview?")
                args.include_content = Confirm.ask("Include a preview of text file content?", default=True)

                # Prompt for content length
                print_section("Content Preview Length")
                args.content_length = Prompt.ask("Maximum length of content preview (chars)?", default=Config.DEFAULT_CONTENT_PREVIEW_LENGTH)

                # Prompt for logging verbosity
                print_section("Logging Verbosity")
                choices = ["quiet", "normal", "verbose", "debug"]
                console.print("Verbosity levels:\n  quiet\n  normal\n  verbose\n  debug\n")
                chosen_verbosity = Prompt.ask("Choose verbosity", default=args.verbosity, choices=choices)
                args.verbosity = chosen_verbosity

                # Prompt for cleanup logs
                print_section("Log Cleanup")
                cleanup_logs_default = False if chosen_verbosity in ["verbose", "debug"] else args.cleanup_logs
                args.cleanup_logs = Confirm.ask("Clean up log file after successful execution?", default=cleanup_logs_default)
            else:
                # Assign defaults if none provided
                args.directory = args.directory or ""
                args.include_subdirs = args.include_subdirs if args.include_subdirs is not None else Config.DEFAULT_INCLUDE_SUBDIRECTORIES
                args.include_size = args.include_size if args.include_size is not None else Config.DEFAULT_INCLUDE_SIZE
                args.include_content = args.include_content if args.include_content is not None else Config.DEFAULT_INCLUDE_CONTENT
                args.content_length = args.content_length if args.content_length is not None else Config.DEFAULT_CONTENT_PREVIEW_LENGTH
                # Let the existing defaults stand for verbosity, cleanup_logs, etc.

        # Validation
        if not args.directory:
            console.print("[red]Error:[/] The following argument is required: directory")
            sys.exit(1)

        dir_path = Path(args.directory)
        if not dir_path.exists() or not dir_path.is_dir():
            console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")
            sys.exit(1)

        logger.debug("Argument prompting complete.")
        return args


# =======================================================
# File Hasher
# =======================================================
class FileHasher:
    """Computes SHA256 hashes for files."""
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path) -> Optional[str]:
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.error(f"Error reading `{file_path}`: {error}")
            return None


# =======================================================
# File Processor
# =======================================================
class FileProcessor:
    """
    Processes files to collect their hashes, plus optional date/time + optional depth.
    Excludes any directories in Config.EXCLUDED_DIRS and any files matching Config.EXCLUDED_PATTERNS.
    """

    def __init__(
        self,
        root_dir: pathlib.Path,
        include_subdirs: bool,
        include_time: bool = False,
        include_depth: bool = False,
        include_size: bool = False,
        include_content: bool = False,
        content_length: int = 300
    ):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs
        self.include_time = include_time
        self.include_depth = include_depth
        self.include_size = include_size
        self.include_content = include_content
        self.content_length = content_length

        # Prepare sets/lists for exclusions
        self.excluded_dirs = set(Config.EXCLUDED_DIRS)
        self.excluded_patterns = Config.EXCLUDED_PATTERNS

    def collect_file_hashes(self) -> List[Tuple[str, str, str, str, str, str]]:
        """
        Collect a list of (file_hash, relative_filename, date_str, depth_str, size_str, content_str).

        - date_str: "YYYY.MM.DD" or "YYYY.MM.DD HH:MM"
        - depth_str: the folder depth with "lvl." prefix, or "" if not used
        - size_str: the file size in KB with ".kb" suffix, or "" if not used
        - content_str: minified preview of text file content, or "" if not used/applicable

        We skip directories in Config.EXCLUDED_DIRS and files matching any pattern in Config.EXCLUDED_PATTERNS.
        """
        # First pass: collect files and their sizes to determine max size (for padding calculation)
        file_info = []
        max_size_kb = 0

        # Use os.walk to gather files
        for root, dirs, files in os.walk(self.root_dir):
            # 1) remove excluded dirs from the walk
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]

            for filename in files:
                # 2) skip if matches any excluded pattern
                if any(fnmatch.fnmatch(filename, pat) for pat in self.excluded_patterns):
                    continue

                file_path = pathlib.Path(root) / filename
                if not self._is_accessible_file(file_path):
                    continue

                # Record file information and keep track of max size
                if self.include_size:
                    size_bytes = file_path.stat().st_size
                    size_kb = int(round(size_bytes / 1024))  # Integer KB size
                    max_size_kb = max(max_size_kb, size_kb)

                file_info.append((file_path, root))

            if not self.include_subdirs:
                # If user doesn't want subdirectories, stop after top-level
                break

        # Calculate padding digits needed based on max file size
        padding_digits = len(str(max_size_kb)) if max_size_kb > 0 else 1

        # Second pass: create the actual hash entries with proper formatting
        hash_entries: List[Tuple[str, str, str, str, str, str]] = []
        for file_path, root in file_info:
            filename = file_path.name
            relative_path = file_path.relative_to(self.root_dir).as_posix()
            file_hash = FileHasher.compute_sha256(file_path)

            if file_hash:
                # Build date/time
                mtime = file_path.stat().st_mtime
                if self.include_time:
                    date_str = time.strftime("%Y.%m.%d %H:%M", time.localtime(mtime))
                else:
                    date_str = time.strftime("%Y.%m.%d", time.localtime(mtime))

                # Build depth
                if self.include_depth:
                    depth_val = len(Path(relative_path).parts)
                    depth_str = f"lvl.{depth_val}"
                else:
                    depth_str = ""

                # Build size in KB
                if self.include_size:
                    size_bytes = file_path.stat().st_size
                    size_kb = int(round(size_bytes / 1024))  # Integer KB size
                    # Format with leading zeros based on the max file size
                    size_str = f"{size_kb:0{padding_digits}d}.kb"
                else:
                    size_str = ""

                # Get content preview if needed
                content_str = self._get_content_preview(file_path) if self.include_content else ""

                hash_entries.append((file_hash, relative_path, date_str, depth_str, size_str, content_str))
                logger.debug(f"Processed: {relative_path}")

        return hash_entries

    def _is_accessible_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            logger.warning(f"`{path}` is not accessible or not a file")
            return False
        return True

    def _is_text_file(self, file_path: pathlib.Path) -> bool:
        """
        Determine if a file is a text file (not a binary file).
        First uses filetype, then falls back to simple heuristic.
        """
        # Check file size first - skip very large files
        try:
            if file_path.stat().st_size > 1024 * 1024:  # Skip files larger than 1MB
                return False

            # Use filetype to detect kind
            kind = filetype.guess(str(file_path))
            if kind is not None:
                # If filetype can detect a type, it's likely a binary file
                return False

            # As a fallback, try to read the first few bytes and check for binary content
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                textchars = bytearray({7, 8, 9, 10, 12, 13, 27} | set(range(0x20, 0x7F)) | set(range(0x80, 0x100)))
                return bool(chunk) and not bool(chunk.translate(None, textchars))
        except (IOError, OSError, UnicodeDecodeError):
            # If any error occurs, consider it not a text file
            return False

    def _get_content_preview(self, file_path: pathlib.Path) -> str:
        """
        Gets a minified content preview of a text file.
        Returns empty string for binary files or if reading fails.
        """
        if not self.include_content or not self._is_text_file(file_path):
            return ""

        try:
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
                return TextMinifier.minify_text(content, self.content_length)
        except (IOError, OSError, UnicodeDecodeError) as error:
            logger.warning(f"Failed to read content from {file_path}: {error}")
            return ""


# =======================================================
# Hash File Manager
# =======================================================
class HashFileManager:
    """
    Manages reading and writing hash files (with optional time + optional depth + optional size).
    If 'include_time' -> we add date/time to lines.
    If 'include_depth' -> we add a DEPTH column.
    If 'include_size' -> we add a SIZE_KB column.
    """

    def __init__(self, file_path: pathlib.Path, include_time: bool = False, include_depth: bool = False, include_size: bool = False, include_content: bool = False):
        self.file_path = file_path
        self.include_time = include_time
        self.include_depth = include_depth
        self.include_size = include_size
        self.include_content = include_content

    def write(self, hash_entries: List[Tuple[str, str, str, str, str, str]]) -> None:
        """
        Writes lines in any of these formats for the header:
          #  YYYY.MM.DD  | FILENAME                       | FILEHASH
          #  YYYY.MM.DD  HH:MM | FILENAME                 | FILEHASH
          #  YYYY.MM.DD  | DEPTH | FILENAME               | FILEHASH
          #  YYYY.MM.DD  HH:MM | DEPTH | FILENAME         | FILEHASH
          #  YYYY.MM.DD  | DEPTH | SIZE(KB) | FILENAME     | FILEHASH
          #  YYYY.MM.DD  HH:MM | DEPTH | SIZE(KB) | FILENAME | FILEHASH
          #  YYYY.MM.DD  | DEPTH | SIZE(KB) | FILENAME     | FILEHASH | CONTENT
          #  YYYY.MM.DD  HH:MM | DEPTH | SIZE(KB) | FILENAME | FILEHASH | CONTENT

        Each data line:
          ''' 2025.03.07 | ''' - "filename.txt" # | 'abc123...'
          ''' 2025.03.07 21:13 | 3 | ''' - "filename.txt" # | 'abc123...'
          ''' 2025.03.07 21:13 | lvl.3 | 42.kb | ''' - "filename.txt" # | 'abc123...' | ''' content preview '''
        """
        try:
            with self.file_path.open("w", encoding='utf-8') as f:
                # Build the header line
                header = self._build_header_line()
                f.write(header + "\n")

                # Sort by filename (case-insensitive)
                sorted_entries = sorted(hash_entries, key=lambda x: x[1].lower())

                # Determine padding for the filename column
                max_length = 2 + max((len(filename) for _, filename, _, _, _, _ in sorted_entries), default=0)

                for file_hash, filename, date_str, depth_str, size_str, content_str in sorted_entries:
                    # Construct the date/depth portion in the line
                    line = self._build_data_line(file_hash, filename, date_str, depth_str, size_str, content_str, max_length)
                    f.write(line)

            logger.info(f"Hash file written: {self.file_path.name}")
        except IOError as error:
            logger.error(f"Failed to write hash file: {error}")

    def _build_header_line(self) -> str:
        """Return the appropriate header line based on include_time / include_depth / include_size / include_content."""
        if self.include_time and self.include_depth and self.include_size and self.include_content:
            # #  YYYY.MM.DD  HH:MM | DEPTH | SIZE(KB) | FILENAME           | FILEHASH | CONTENT
            return "#  YYYY.MM.DD  HH:MM | DEPTH    | SIZE(KB)  | FILENAME                | FILEHASH                                                           | CONTENT"
        elif self.include_time and self.include_depth and self.include_size and not self.include_content:
            # #  YYYY.MM.DD  HH:MM | DEPTH | SIZE(KB) | FILENAME           | FILEHASH
            return "#  YYYY.MM.DD  HH:MM | DEPTH    | SIZE(KB)  | FILENAME                | FILEHASH"
        elif self.include_time and self.include_depth and not self.include_size and self.include_content:
            # #  YYYY.MM.DD  HH:MM | DEPTH | FILENAME                   | FILEHASH | CONTENT
            return "#  YYYY.MM.DD  HH:MM | DEPTH    | FILENAME                        | FILEHASH                                                           | CONTENT"
        elif self.include_time and self.include_depth and not self.include_size and not self.include_content:
            # #  YYYY.MM.DD  HH:MM | DEPTH | FILENAME                   | FILEHASH
            return "#  YYYY.MM.DD  HH:MM | DEPTH    | FILENAME                        | FILEHASH"
        elif self.include_time and not self.include_depth and self.include_size and self.include_content:
            # #  YYYY.MM.DD  HH:MM | SIZE(KB) | FILENAME                | FILEHASH | CONTENT
            return "#  YYYY.MM.DD  HH:MM | SIZE(KB)  | FILENAME                     | FILEHASH                                                           | CONTENT"
        elif self.include_time and not self.include_depth and self.include_size and not self.include_content:
            # #  YYYY.MM.DD  HH:MM | SIZE(KB) | FILENAME                | FILEHASH
            return "#  YYYY.MM.DD  HH:MM | SIZE(KB)  | FILENAME                     | FILEHASH"
        elif self.include_time and not self.include_depth and not self.include_size and self.include_content:
            # #  YYYY.MM.DD  HH:MM | FILENAME                             | FILEHASH | CONTENT
            return "#  YYYY.MM.DD  HH:MM | FILENAME                        | FILEHASH                                                           | CONTENT"
        elif self.include_time and not self.include_depth and not self.include_size and not self.include_content:
            # #  YYYY.MM.DD  HH:MM | FILENAME                             | FILEHASH
            return "#  YYYY.MM.DD  HH:MM | FILENAME                        | FILEHASH"
        elif not self.include_time and self.include_depth and self.include_size and self.include_content:
            # #  YYYY.MM.DD  | DEPTH | SIZE(KB) | FILENAME                | FILEHASH | CONTENT
            return "#  YYYY.MM.DD  | DEPTH    | SIZE(KB)  | FILENAME                     | FILEHASH                                                           | CONTENT"
        elif not self.include_time and self.include_depth and self.include_size and not self.include_content:
            # #  YYYY.MM.DD  | DEPTH | SIZE(KB) | FILENAME                | FILEHASH
            return "#  YYYY.MM.DD  | DEPTH    | SIZE(KB)  | FILENAME                     | FILEHASH"
        elif not self.include_time and self.include_depth and not self.include_size and self.include_content:
            # #  YYYY.MM.DD  | DEPTH | FILENAME                           | FILEHASH | CONTENT
            return "#  YYYY.MM.DD  | DEPTH    | FILENAME                        | FILEHASH                                                           | CONTENT"
        elif not self.include_time and self.include_depth and not self.include_size and not self.include_content:
            # #  YYYY.MM.DD  | DEPTH | FILENAME                           | FILEHASH
            return "#  YYYY.MM.DD  | DEPTH    | FILENAME                        | FILEHASH"
        elif not self.include_time and not self.include_depth and self.include_size and self.include_content:
            # #  YYYY.MM.DD  | SIZE(KB) | FILENAME                        | FILEHASH | CONTENT
            return "#  YYYY.MM.DD  | SIZE(KB)  | FILENAME                     | FILEHASH                                                           | CONTENT"
        elif not self.include_time and not self.include_depth and self.include_size and not self.include_content:
            # #  YYYY.MM.DD  | SIZE(KB) | FILENAME                        | FILEHASH
            return "#  YYYY.MM.DD  | SIZE(KB)  | FILENAME                     | FILEHASH"
        elif not self.include_time and not self.include_depth and not self.include_size and self.include_content:
            # #  YYYY.MM.DD  | FILENAME                                  | FILEHASH | CONTENT
            return "#  YYYY.MM.DD  | FILENAME                              | FILEHASH                                                           | CONTENT"
        else:
            # #  YYYY.MM.DD  | FILENAME                                  | FILEHASH
            return "#  YYYY.MM.DD  | FILENAME                              | FILEHASH"

    def _build_data_line(self, file_hash: str, filename: str, date_str: str, depth_str: str, size_str: str, content_str: str, max_length: int) -> str:
        """Return a single data line with the right columns for date/depth/size/filename/hash/content."""
        # Always triple single-quotes for date portion
        # Possibly also "depth" and/or "size" plus the triple quotes for the next chunk
        # Then the double-quoted filename, single-quoted hash, etc.

        padded_filename = f"\"{filename}\"".ljust(max_length)

        # Base line without content
        if self.include_depth and depth_str and self.include_size and size_str:
            base_line = f"''' {date_str} | {depth_str} | {size_str} | ''' - {padded_filename} # | '{file_hash}'"
        elif self.include_depth and depth_str and not self.include_size:
            base_line = f"''' {date_str} | {depth_str} | ''' - {padded_filename} # | '{file_hash}'"
        elif not self.include_depth and self.include_size and size_str:
            base_line = f"''' {date_str} | {size_str} | ''' - {padded_filename} # | '{file_hash}'"
        else:
            base_line = f"''' {date_str} | ''' - {padded_filename} # | '{file_hash}'"

        # Add content if needed
        if self.include_content and content_str:
            return f"{base_line} | ''' {content_str} '''\n"
        else:
            return f"{base_line}\n"

    def read(self) -> List[Tuple[str, str, str, str, str, str]]:
        """
        Reads lines in all possible formats, returning (hash, filename, date_str, depth_str, size_str, content_str).
        If a line lacks date/time, depth, size, or content, we store those as empty strings.
        """
        hash_entries: List[Tuple[str, str, str, str, str, str]] = []

        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line_num, line in enumerate(f):
                    if not line.strip() or line.strip().startswith("#"):
                        continue
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            logger.error(f"Failed to read hash file: {error}")

        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str, str, str, str, str]]:
        """
        We have various possible states with or without content.
        Then we fallback to the old format (- 'filename' # | "hash").

        Returns (hash, filename, date_str, depth_str, size_str, content_str).
        """
        line = line.strip()
        if not line or line.startswith("#"):
            return None

        # First try to parse the line assuming the standard format without content
        content_str = ""

        # First check if this line has a content section at the end
        # The content section would be after the hash and marked with | ''' content '''
        hash_pattern = r"#\s*\|\s*'([a-fA-F0-9]{64})'"
        hash_match = re.search(hash_pattern, line)

        if hash_match:
            # We found the hash part, now check if there's content after it
            file_hash = hash_match.group(1)
            remainder = line[hash_match.end():].strip()

            # Look for content part (| ''' content ''')
            if remainder.startswith("|"):
                content_match = re.match(r"\|\s*'''\s*(.*?)\s*'''$", remainder)
                if content_match:
                    content_str = content_match.group(1)

                    # Remove content part from the line for standard parsing
                    line = line[:hash_match.end()].strip()

        # Now parse the line without the content part

        # 1) date/time + depth + size + hash (no content in this pattern)
        dt_depth_size_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})"
            r"\s*\|\s*(lvl\.\d+)\s*\|\s*(\d+\.kb)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(dt_depth_size_pat, line)
        if match:
            date_str = match.group(1)
            depth_str = match.group(2)
            size_str = match.group(3)
            filename = match.group(4)
            file_hash = match.group(5)
            return (file_hash, filename, date_str, depth_str, size_str, content_str)

        # 2) date/time + depth
        dt_depth_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})"
            r"\s*\|\s*(lvl\.\d+)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(dt_depth_pat, line)
        if match:
            date_str = match.group(1)
            depth_str = match.group(2)
            filename = match.group(3)
            file_hash = match.group(4)
            return (file_hash, filename, date_str, depth_str, "", content_str)

        # 3) date/time + size
        dt_size_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})"
            r"\s*\|\s*(\d+\.kb)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(dt_size_pat, line)
        if match:
            date_str = match.group(1)
            size_str = match.group(2)
            filename = match.group(3)
            file_hash = match.group(4)
            return (file_hash, filename, date_str, "", size_str, content_str)

        # 4) date/time only
        dt_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})"
            r"\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(dt_pat, line)
        if match:
            date_str = match.group(1)
            filename = match.group(2)
            file_hash = match.group(3)
            return (file_hash, filename, date_str, "", "", content_str)

        # 5) date + depth + size
        date_depth_size_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2})"
            r"\s*\|\s*(lvl\.\d+)\s*\|\s*(\d+\.kb)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(date_depth_size_pat, line)
        if match:
            date_str = match.group(1)
            depth_str = match.group(2)
            size_str = match.group(3)
            filename = match.group(4)
            file_hash = match.group(5)
            return (file_hash, filename, date_str, depth_str, size_str, content_str)

        # 6) date + depth
        date_depth_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2})"
            r"\s*\|\s*(lvl\.\d+)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(date_depth_pat, line)
        if match:
            date_str = match.group(1)
            depth_str = match.group(2)
            filename = match.group(3)
            file_hash = match.group(4)
            return (file_hash, filename, date_str, depth_str, "", content_str)

        # 7) date + size
        date_size_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2})"
            r"\s*\|\s*(\d+\.kb)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(date_size_pat, line)
        if match:
            date_str = match.group(1)
            size_str = match.group(2)
            filename = match.group(3)
            file_hash = match.group(4)
            return (file_hash, filename, date_str, "", size_str, content_str)

        # 8) date only
        date_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2})"
            r"\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(date_pat, line)
        if match:
            date_str = match.group(1)
            filename = match.group(2)
            file_hash = match.group(3)
            return (file_hash, filename, date_str, "", "", content_str)

        # Handle legacy formats
        # Legacy 1: date/time + depth + size (old format)
        old_dt_depth_size_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})"
            r"\s*\|\s*(\d+)\s*\|\s*(\d+)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(old_dt_depth_size_pat, line)
        if match:
            date_str = match.group(1)
            depth_val = match.group(2)
            size_val = match.group(3)
            filename = match.group(4)
            file_hash = match.group(5)
            depth_str = f"lvl.{depth_val}"
            size_str = f"{size_val}.kb"
            return (file_hash, filename, date_str, depth_str, size_str, content_str)

        # Legacy 2: date/time + depth (old format)
        old_dt_depth_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2}\s+\d{2}:\d{2})"
            r"\s*\|\s*(\d+)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(old_dt_depth_pat, line)
        if match:
            date_str = match.group(1)
            depth_val = match.group(2)
            filename = match.group(3)
            file_hash = match.group(4)
            depth_str = f"lvl.{depth_val}"
            return (file_hash, filename, date_str, depth_str, "", content_str)

        # Legacy 3: date + depth + size (old format)
        old_date_depth_size_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2})"
            r"\s*\|\s*(\d+)\s*\|\s*(\d+)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(old_date_depth_size_pat, line)
        if match:
            date_str = match.group(1)
            depth_val = match.group(2)
            size_val = match.group(3)
            filename = match.group(4)
            file_hash = match.group(5)
            depth_str = f"lvl.{depth_val}"
            size_str = f"{size_val}.kb"
            return (file_hash, filename, date_str, depth_str, size_str, content_str)

        # Legacy 4: date + depth (old format)
        old_date_depth_pat = (
            r"^'''\s*"
            r"(\d{4}\.\d{2}\.\d{2})"
            r"\s*\|\s*(\d+)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        )
        match = re.match(old_date_depth_pat, line)
        if match:
            date_str = match.group(1)
            depth_val = match.group(2)
            filename = match.group(3)
            file_hash = match.group(4)
            depth_str = f"lvl.{depth_val}"
            return (file_hash, filename, date_str, depth_str, "", content_str)

        # Old format fallback
        old_pat = r'^-\s*\'(.+?)\'\s*#\s*\|\s*"([a-fA-F0-9]{64})"$'
        match_old = re.match(old_pat, line)
        if match_old:
            filename = match_old.group(1)
            file_hash = match_old.group(2)
            return (file_hash, filename, "", "", "", content_str)

        logger.warning(f"Invalid hash file entry: {line}")
        return None


# =======================================================
# File Renamer
# =======================================================
class FileRenamer:
    """Handles the renaming of files based on hash comparisons."""

    def __init__(self, root_dir: pathlib.Path):
        self.root_dir = root_dir

    def execute(
        self,
        source_hashes: List[Tuple[str, str, str, str, str, str]],
        target_hashes: List[Tuple[str, str, str, str, str, str]],
        dry_run: bool = True
    ) -> bool:
        """
        We only need the (hash, filename) pair for matching and renaming.
        The date_str / depth_str / size_str / content_str are unused for the rename logic.
        """
        # Extract only the hash and filename for determining renames
        src_map_entries = [(h, f) for (h, f, _, _, _, _) in source_hashes]
        tgt_map_entries = [(h, f) for (h, f, _, _, _, _) in target_hashes]

        source_map = self._map_hash_to_paths(src_map_entries)
        target_map = self._map_hash_to_paths(tgt_map_entries)

        rename_pairs = self._determine_rename_pairs(source_map, target_map)
        conflicts = False

        if dry_run:
            self._preview_renames(rename_pairs)

        for src_rel, tgt_rel in rename_pairs:
            src_path = self.root_dir / src_rel
            tgt_path = self.root_dir / tgt_rel

            if src_rel == tgt_rel:
                logger.debug(f"Unchanged: {src_rel}")
                continue

            if not self._validate_paths(src_path, tgt_path):
                conflicts = True
                continue

            if dry_run:
                logger.info(f'Will rename: "{src_rel}" → "{tgt_rel}"')
            else:
                self._perform_rename(src_path, tgt_path, src_rel, tgt_rel)

        self._log_completion(dry_run, conflicts)
        return not conflicts

    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        hash_map: Dict[str, List[str]] = {}
        for file_hash, path in hash_entries:
            hash_map.setdefault(file_hash, []).append(path)
        return hash_map

    def _determine_rename_pairs(
        self,
        source_map: Dict[str, List[str]],
        target_map: Dict[str, List[str]],
    ) -> List[Tuple[str, str]]:
        pairs: List[Tuple[str, str]] = []
        processed_targets: Set[str] = set()

        for file_hash, src_paths in source_map.items():
            tgt_paths = target_map.get(file_hash, [])
            for src in src_paths:
                if any(src == pair[0] for pair in pairs):
                    # Already handled
                    continue

                available_tgts = [t for t in tgt_paths if t not in processed_targets]
                if not available_tgts:
                    logger.warning(f"No matching hash for: {src}")
                    continue

                best_match = self._select_best_match(src, available_tgts)
                if best_match:
                    pairs.append((src, best_match))
                    processed_targets.add(best_match)

        return pairs

    def _select_best_match(self, source: str, targets: List[str]) -> Optional[str]:
        source_clean = self._clean_name(source)
        best_similarity = -1.0
        best_target = None

        for tgt in targets:
            tgt_clean = self._clean_name(tgt)
            similarity = self._name_similarity(source_clean, tgt_clean)
            if similarity > best_similarity:
                best_similarity = similarity
                best_target = tgt

        if best_target:
            logger.debug(f"Best match for '{source}' is '{best_target}' with similarity {best_similarity:.2f}")
        else:
            logger.warning(f"No suitable match found for '{source}'")
        return best_target

    @staticmethod
    def _name_similarity(name1: str, name2: str) -> float:
        matches = sum(a == b for a, b in zip(name1, name2))
        max_len = max(len(name1), len(name2))
        return matches / max_len if max_len else 0

    @staticmethod
    def _clean_name(name: str) -> str:
        # Normalize to ASCII, remove special chars
        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')
        name = name.lower()
        name = pathlib.Path(name).stem
        name = re.sub(r'[^a-z0-9]', '', name)
        return name

    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:
        if not src.exists():
            logger.warning(f"Source missing: {src.relative_to(self.root_dir)}")
            return False
        if tgt.exists() and tgt != src:
            logger.error(f"Target exists: {tgt.relative_to(self.root_dir)}")
            return False
        return True

    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str) -> None:
        try:
            tgt.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src), str(tgt))
            logger.info(f'Renamed: "{src_rel}" → "{tgt_rel}"')
        except OSError as error:
            logger.error(f"Failed to rename {src_rel}: {error}")

    def _preview_renames(self, rename_pairs: List[Tuple[str, str]]) -> None:
        if not rename_pairs:
            logger.warning("No files require renaming")
            return

        table = Table(
            title="Pending Rename Operations",
            show_header=True,
            header_style="bold blue",
            box=ROUNDED
        )
        table.add_column("Operation", style="cyan", width=4)
        table.add_column("Source", style="white")
        table.add_column("Target", style="green")

        changes = 0
        for src, tgt in rename_pairs:
            if src != tgt:
                table.add_row("→", src, tgt)
                changes += 1

        if changes:
            console = Console()
            console.print("\n")
            console.print(Panel(table, border_style="blue"))
            console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")
        else:
            logger.warning("No files require renaming")

    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:
        operation = "Dry run" if dry_run else "File renaming"
        if has_conflicts:
            logger.warning(f"{operation}: Conflicts detected, some files skipped")
        else:
            logger.info(f"{operation} completed successfully")


# =======================================================
# File Editor
# =======================================================
class FileEditor:
    """Opens files using the default system editor."""

    @staticmethod
    def open(file_path: pathlib.Path) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', str(file_path)])
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(['xdg-open', str(file_path)])
            else:
                logger.warning(f"Unsupported platform: {sys.platform}")
        except Exception as error:
            logger.error(f"Failed to open editor: {error}")


# =======================================================
# Text Minifier
# =======================================================
class TextMinifier:
    """Handles minification of text content into single-line representation."""

    @staticmethod
    def minify_text(text, max_length=None):
        """
        Converts multi-line text to a single line with escaped newlines.
        Optionally truncates to max_length.
        """
        # First escape any existing backslashes to avoid issues
        text = text.replace('\\', '\\\\')

        # Special handling for tabs and other control characters
        text = text.replace('\t', '\\t')
        text = text.replace('\r', '\\r')

        # Split the text into lines and join with escaped newlines
        lines = text.splitlines()
        result = '\\n'.join(lines)

        # Truncate if necessary
        if max_length and len(result) > max_length:
            result = result[:max_length-3] + '...'

        return result

    @staticmethod
    def unminify_text(text):
        """
        Converts a single line with escaped sequences back to multi-line text.
        """
        # Handle all standard escape sequences
        escape_chars = {
            '\\n': '\n',   # newline
            '\\r': '\r',   # carriage return
            '\\t': '\t',   # tab
            '\\b': '\b',   # backspace
            '\\f': '\f',   # form feed
            '\\"': '"',    # double quote
            "\\'": "'",    # single quote
            '\\\\': '\\',  # backslash
            '\\/': '/'     # forward slash
        }

        # Process the string character by character
        result = ""
        i = 0
        while i < len(text):
            # Check for escape sequences (2-char sequences)
            if i + 1 < len(text) and text[i] == '\\':
                escape_seq = text[i:i+2]
                if escape_seq in escape_chars:
                    result += escape_chars[escape_seq]
                    i += 2
                    continue
                # Handle unicode escape sequences \uXXXX
                elif i + 5 < len(text) and text[i:i+2] == '\\u':
                    try:
                        hex_val = text[i+2:i+6]
                        result += chr(int(hex_val, 16))
                        i += 6
                        continue
                    except (ValueError, IndexError):
                        # If not a valid unicode escape, treat as normal characters
                        pass

            # Normal character
            result += text[i]
            i += 1

        return result


# =======================================================
# Main App Class
# =======================================================
class BatchRenameApp:
    """
    Encapsulates the CLI workflow for batch renaming:
      1) Parse & prompt for Arguments
      2) Initialize Logging
      3) Collect & write .original_hashes.py & .new_hashes.py
      4) Edit & confirm renaming
      5) Cleanup
    """

    def __init__(self):
        self.arg_handler = ArgumentHandler()
        self.args: Optional[argparse.Namespace] = None

    def run(self) -> None:
        # 1) Parse & prompt
        self.args = self.arg_handler.get_arguments()
        self.args = self.arg_handler.prompt_for_missing_arguments(self.args)

        # 2) Initialize Logging
        LoggerSetup.initialize_logging(self.args.verbosity)

        # 3) Handle the main process
        success = False
        try:
            self.handle_process_command()
            success = True
        except Exception as e:
            logger.error(f"Execution failed: {e}")

        # 4) Cleanup logs if needed and success
        if success and self.args.cleanup_logs:
            logger.remove()  # remove Loguru handler to release file
            log_file = Path("app.log.yml")
            if log_file.exists():
                console = Console()
                try:
                    log_file.unlink()
                    console.print(f"[bold green]Log file {log_file} has been cleaned up.[/bold green]\n")
                except Exception as e:
                    console.print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")

    def handle_process_command(self) -> None:
        console = Console()
        root_dir = pathlib.Path(self.args.directory).resolve()

        org_file = root_dir / ".original_hashes.py"
        new_file = root_dir / ".new_hashes.py"

        try:
            processor = FileProcessor(
                root_dir,
                self.args.include_subdirs,
                include_time=self.args.include_time,
                include_depth=self.args.include_depth,
                include_size=self.args.include_size,
                include_content=self.args.include_content,
                content_length=self.args.content_length
            )
            initial_hashes = processor.collect_file_hashes()

            # Write to both "original" and "new" for editing
            for file_path in (org_file, new_file):
                manager = HashFileManager(
                    file_path,
                    include_time=self.args.include_time,
                    include_depth=self.args.include_depth,
                    include_size=self.args.include_size,
                    include_content=self.args.include_content
                )
                manager.write(initial_hashes)

            logger.info("Opening new hash file for editing...")
            FileEditor.open(new_file)

            if not Confirm.ask("\nShow preview of filename changes? [y/n]: ", default="y"):
                logger.warning("Operation cancelled by user")
                return

            # Create HashFileManager instances without parameters for reading
            org_manager = HashFileManager(org_file)
            new_manager = HashFileManager(new_file)

            renamer = FileRenamer(root_dir)
            # Dry run first
            if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):
                if Confirm.ask("\nApply these changes? [y/n]: ", default="y"):
                    renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)

        finally:
            # Cleanup: Attempt to delete the hash files
            for file_path in (org_file, new_file):
                try:
                    if file_path.exists():
                        file_path.unlink()
                        logger.info(f"Cleaned up: {file_path.name}")
                except OSError as error:
                    logger.warning(f"Cleanup failed for {file_path.name}: {error}")


# =======================================================
# Execution entrypoint
# =======================================================
def main() -> None:
    """Main entry point of the utility."""
    app = BatchRenameApp()
    app.run()


if __name__ == "__main__":
    main()
