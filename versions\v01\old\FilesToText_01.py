import hashlib
import os
import time
import hashlib
import time
import os
import hashlib
import os
import time

def sha256_checksum(file_path):
    """Compute the SHA256 hash of a file."""
    sha256 = hashlib.sha256()
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b''):
            sha256.update(chunk)
    return sha256.hexdigest()

def get_file_hashes(input_directory, relative_path='', include_paths=True, search_subfolders=True):
    """Get the SHA256 hash, relative path (optional), and filename for each file in a directory and its subdirectories."""
    file_hashes = []
    print(f"Entering directory: {input_directory}")  # Debug print
    for entry in os.listdir(input_directory):
        entry_path = os.path.join(input_directory, entry)
        if os.path.isfile(entry_path) and os.access(entry_path, os.R_OK):
            print(f"Processing file: {entry_path}")  # Debug print
            file_hash = sha256_checksum(entry_path)
            if include_paths:
                file_hashes.append((file_hash, relative_path, entry))
                print(os.path.join(relative_path, entry))
            else:
                file_hashes.append((file_hash, entry))
                print(entry)
        elif os.path.isdir(entry_path) and search_subfolders and not os.path.islink(entry_path):
            new_relative_path = os.path.join(relative_path, entry)
            file_hashes.extend(get_file_hashes(entry_path, new_relative_path, include_paths, search_subfolders))
    return file_hashes



def write_file_hashes(input_directory, org_file, new_file, include_paths=True, search_subfolders=True):
    """Write the SHA256 hashes, relative paths (optional), and filenames to two text files."""
    file_hashes = get_file_hashes(input_directory, include_paths=include_paths, search_subfolders=search_subfolders)
    if include_paths:
        formatted_hashes = [f"{h[0]}|{h[1]}|{h[2]}" for h in file_hashes]
    else:
        formatted_hashes = [f"{h[0]}|{h[1]}" for h in file_hashes]
    with open(org_file, "w") as f:
        f.write("\n".join(formatted_hashes))
    with open(new_file, "w") as f:
        f.write("\n".join(formatted_hashes))






def rename_files(input_directory, out_file_org, out_file_new):
    with open(out_file_new, "r") as f:
        new_filenames = [line.strip() for line in f.readlines()]

    new_filenames_hash = []
    new_filenames_name = []
    for new in new_filenames:
        split_line = new.split('|')
        new_filenames_hash.append(split_line[0])
        new_filenames_name.append(split_line[1])

    filenames_with_hash = get_file_hashes(input_directory)

    for item in filenames_with_hash:
        matching_indices = []
        for index, new_filename_hash in enumerate(new_filenames_hash):
            if item[0] == new_filename_hash:
                matching_indices.append(index)

        if matching_indices:
            first_matching_index = matching_indices[0]
            print(first_matching_index)
            print('org: %s' % (item[1]))
            print('new: %s' % (new_filenames_name[first_matching_index]))
            print('\n')
            file_path_org = os.path.join(input_directory, item[1])
            file_path_new = os.path.join(input_directory, new_filenames_name[first_matching_index])
            os.rename(file_path_org, file_path_new)
    else:
        print("The number of filenames in the original and new lists do not match.")


initial_directory = os.getcwd()
# input_directory = os.path.join(initial_directory, '.VENV', 'VENV_Utils_Batch')
input_directory = os.path.join(initial_directory, '.VENV')

# Write the current filenames to the original_filenames file
out_textfile_org = os.path.join(initial_directory, 'FilesToText_01__filenames_ORG.txt')
out_textfile_new = os.path.join(initial_directory, 'FilesToText_01__filenames_NEW.txt')
write_file_hashes(input_directory, out_textfile_org, out_textfile_new, True, False)

# Rename files based on the new_filenames file
# rename_files(input_directory, out_textfile_org, out_textfile_new)