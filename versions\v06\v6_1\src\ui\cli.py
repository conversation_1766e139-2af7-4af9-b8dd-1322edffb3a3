import argparse
import pathlib
import sys
from typing import List, Optional

from rich.console import Console
from rich.panel import Panel
from rich.prompt import Confirm
from rich.table import Table

from src.utils.logging import Logger, LogLevel, LogConfig
from src.utils.config import Config
from src.core.directory_manager import DirectoryManager
from src.core.file_processor import FileProcessor
from src.ui.editor import EditorManager

class CLI:
    """Command-line interface for the file renaming utility."""

    def __init__(self):
        self.console = Console()
        self.config = Config()
        self.logger = Logger(self.config.log_config)

    def run(self, args: Optional[List[str]] = None) -> int:
        """Main entry point for the CLI."""
        parser = self._create_parser()
        parsed_args = parser.parse_args(args)

        try:
            return self._execute_command(parsed_args)
        except Exception as error:
            self.logger.log(f"Unexpected error: {error}", LogLevel.ERROR)
            return 1

    def _create_parser(self) -> argparse.ArgumentParser:
        """Creates the argument parser."""
        parser = argparse.ArgumentParser(
            description="File renaming utility with hash verification"
        )

        parser.add_argument(
            "-v", "--verbose",
            action="store_true",
            help="Enable verbose output"
        )

        subparsers = parser.add_subparsers(dest="command", required=True)

        # Rename command
        rename_parser = subparsers.add_parser("rename", help="Rename files")
        rename_parser.add_argument(
            "directory",
            type=pathlib.Path,
            help="Directory containing files to rename"
        )
        rename_parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be done without making changes"
        )

        # View command
        view_parser = subparsers.add_parser("view", help="View directory structure")
        view_parser.add_argument(
            "directory",
            type=pathlib.Path,
            help="Directory to view"
        )
        view_parser.add_argument(
            "--depth",
            type=int,
            help="Maximum depth to display"
        )

        return parser

    def _execute_command(self, args: argparse.Namespace) -> int:
        """Executes the specified command."""
        if args.verbose:
            self.config.log_config.verbosity = LogLevel.VERBOSE

        if not args.directory.exists():
            self.logger.log(f"Directory not found: {args.directory}", LogLevel.ERROR)
            return 1

        if args.command == "rename":
            return self._handle_rename(args)
        elif args.command == "view":
            return self._handle_view(args)
        
        return 1

    def _handle_rename(self, args: argparse.Namespace) -> int:
        """Handles the rename command."""
        processor = FileProcessor(args.directory, self.logger)
        editor = EditorManager(self.logger)

        files = processor.collect_files()
        if not files:
            self.logger.log("No files found to rename", LogLevel.WARNING)
            return 1

        if not editor.edit_rename_plan(files):
            self.logger.log("Failed to create rename plan", LogLevel.ERROR)
            return 1

        success = True
        for old_path in files:
            new_name = f"renamed_{old_path.name}"
            new_path = old_path.parent / new_name
            if not processor.rename_file(old_path, new_path, dry_run=args.dry_run):
                success = False

        if args.dry_run:
            self.logger.log("Dry run completed", LogLevel.SUMMARY)
            return 0 if success else 1

        if not Confirm.ask("Proceed with renaming?"):
            self.logger.log("Operation cancelled", LogLevel.WARNING)
            return 1

        return 0 if success else 1

    def _handle_view(self, args: argparse.Namespace) -> int:
        """Handles the view command."""
        manager = DirectoryManager(args.directory, self.logger)
        structure = manager.get_directory_structure(args.depth)
        
        if not structure:
            return 1

        table = Table(show_header=False, box=None)
        table.add_column("Structure")
        for line in structure:
            table.add_row(line)

        self.console.print("\n")
        self.console.print(Panel(table, title="Directory Structure"))
        return 0

def main() -> int:
    """Entry point for the CLI."""
    return CLI().run()

if __name__ == "__main__":
    sys.exit(main())
