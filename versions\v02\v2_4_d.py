# 'ChatGPT o1-preview'
# 'https://chatgpt.com/c/6742dfd4-4350-8008-a36b-87c1d886d5c6'

import argparse
import hashlib
import os
import sys
import subprocess
from pathlib import Path
from typing import List, Tuple

from rich import print
from rich.filesize import decimal
from rich.markup import escape
from rich.text import Text
from rich.tree import Tree


def compute_sha256(file_path: Path) -> str:
    """Compute the SHA256 hash of a file."""
    sha256 = hashlib.sha256()
    try:
        with file_path.open('rb') as f:
            for chunk in iter(lambda: f.read(4096), b''):
                sha256.update(chunk)
        return sha256.hexdigest()
    except IOError as e:
        print(f"[red]Error reading file {file_path}: {e}[/red]")
        return ""


def get_file_hashes(input_directory: Path, include_subdirectories: bool = True) -> List[Tuple[str, str]]:
    """Get the SHA256 hash and relative filename for each file in a directory."""
    file_hashes = []
    files = input_directory.rglob('*') if include_subdirectories else input_directory.iterdir()
    for file_path in files:
        if file_path.is_file():
            relative_path = file_path.relative_to(input_directory)
            file_hash = compute_sha256(file_path)
            if file_hash:
                file_hashes.append((file_hash, str(relative_path)))
                print(f"Processed: {relative_path}")
    return file_hashes


def write_hashes_to_file(file_hashes: List[Tuple[str, str]], output_file: Path) -> None:
    """Write the list of file hashes and filenames to a text file."""
    try:
        with output_file.open("w", encoding='utf-8') as f:
            for file_hash, filename in file_hashes:
                f.write(f"{file_hash}|{filename}\n")
        print(f"[green]Hashes written to {output_file}[/green]")
    except IOError as e:
        print(f"[red]Error writing to file {output_file}: {e}[/red]")


def read_hashes_from_file(input_file: Path) -> List[Tuple[str, str]]:
    """Read hashes and filenames from a text file."""
    hashes = []
    try:
        with input_file.open("r", encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split('|', 1)
                if len(parts) == 2:
                    hashes.append((parts[0], parts[1]))
    except IOError as e:
        print(f"[red]Error reading file {input_file}: {e}[/red]")
    return hashes


def open_file_in_editor(file_path: Path) -> None:
    """Open a file in the default text editor."""
    try:
        if sys.platform.startswith('darwin'):
            subprocess.call(('open', str(file_path)))
        elif os.name == 'nt':
            os.startfile(file_path)
        elif os.name == 'posix':
            subprocess.call(('xdg-open', str(file_path)))
        else:
            print(f"[yellow]Cannot determine the OS to open the file {file_path}.[/yellow]")
    except Exception as e:
        print(f"[red]Failed to open file {file_path} in editor: {e}[/red]")


def rename_files(
    input_directory: Path,
    original_hash_file: Path,
    new_hash_file: Path
) -> None:
    """Rename files based on hash matching between original and new hash files."""
    original_hashes = read_hashes_from_file(original_hash_file)
    new_hashes = read_hashes_from_file(new_hash_file)

    if len(original_hashes) != len(new_hashes):
        print("[yellow]Warning: The number of entries in original and new hash files do not match.[/yellow]")

    hash_to_new_filename = {hash_val: new_name for hash_val, new_name in new_hashes}

    for original_hash, original_filename in original_hashes:
        if original_hash in hash_to_new_filename:
            new_filename = hash_to_new_filename[original_hash]
            original_file_path = input_directory / original_filename
            new_file_path = input_directory / new_filename

            if original_file_path == new_file_path:
                print(f"[cyan]No change for file: {original_filename}[/cyan]")
                continue

            if original_file_path.exists():
                if not new_file_path.exists():
                    try:
                        new_file_path.parent.mkdir(parents=True, exist_ok=True)
                        original_file_path.rename(new_file_path)
                        print(f"[green]Renamed: {original_filename} -> {new_filename}[/green]")
                    except OSError as e:
                        print(f"[red]Error renaming {original_filename} to {new_filename}: {e}[/red]")
                else:
                    print(f"[yellow]Conflict: {new_filename} already exists. Skipping rename for {original_filename}.[/yellow]")
            else:
                print(f"[red]Original file does not exist: {original_filename}[/red]")
        else:
            print(f"[red]No matching new filename found for hash: {original_hash} (File: {original_filename})[/red]")


def create_file_text(path: Path) -> Text:
    """Create a formatted Text object for a file."""
    text_filename = Text(path.name, "green")
    text_filename.highlight_regex(r"\..*$", "bold red")
    text_filename.stylize(f"link file://{path}")
    try:
        file_size = path.stat().st_size
    except OSError:
        file_size = 0
    text_filename.append(f" ({decimal(file_size)})", "blue")
    icon = "🐍 " if path.suffix == ".py" else "📄 "
    return Text(icon) + text_filename


def walk_directory(directory: Path, tree: Tree) -> None:
    """Recursively build a Tree with directory contents."""
    try:
        paths = sorted(
            directory.iterdir(),
            key=lambda path: (path.is_file(), path.name.lower()),
        )
    except PermissionError as e:
        print(f"[red]Permission denied: {e}[/red]")
        return

    for path in paths:
        if path.name.startswith("."):
            continue  # Skip hidden files and directories

        if path.is_dir():
            style = "dim" if path.name.startswith("__") else ""
            branch = tree.add(
                f"[bold magenta]:open_file_folder: [link file://{path}]{escape(path.name)}",
                style=style,
                guide_style=style,
            )
            walk_directory(path, branch)
        else:
            tree.add(create_file_text(path))


def display_directory_tree(directory: Path) -> None:
    """Display the directory tree using Rich."""
    if not directory.exists():
        print(f"[red]Error: The directory '{directory}' does not exist.[/red]")
        return

    tree = Tree(
        f":open_file_folder: [link file://{directory}]{directory}",
        guide_style="bold bright_blue",
    )
    walk_directory(directory, tree)
    print(tree)


def parse_arguments() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="py_RenameWithTextEditor: Hashing and Renaming Utility"
    )

    parser.add_argument(
        "operation",
        choices=["process", "visualize"],
        help="Operation to perform: 'process' to generate hashes and rename files, 'visualize' to display directory tree.",
    )
    parser.add_argument(
        "input_directory",
        type=str,
        nargs="?",
        default=".",
        help="Directory to process or visualize. Defaults to current directory.",
    )
    parser.add_argument(
        "--include-subdirectories",
        action="store_true",
        help="Include subdirectories in processing.",
    )
    parser.add_argument(
        "--original-output",
        type=str,
        default="FilesToText__filenames_ORG.txt",
        help="Output file for original filenames and hashes.",
    )
    parser.add_argument(
        "--new-output",
        type=str,
        default="FilesToText__filenames_NEW.txt",
        help="Output file for new filenames and hashes.",
    )

    return parser.parse_args()


def main():
    args = parse_arguments()

    if args.operation == "process":
        input_dir = Path(args.input_directory).resolve()
        original_output = Path(args.original_output).resolve()
        new_output = Path(args.new_output).resolve()

        if not input_dir.exists():
            print(f"[red]Error: The directory '{input_dir}' does not exist.[/red]")
            sys.exit(1)

        print(f"[cyan]Generating hashes for directory: {input_dir}[/cyan]")
        file_hashes = get_file_hashes(input_dir, include_subdirectories=args.include_subdirectories)
        write_hashes_to_file(file_hashes, original_output)
        write_hashes_to_file(file_hashes, new_output)
        print("[cyan]Hash files generated successfully.[/cyan]")

        print(f"[cyan]Opening '{new_output}' for editing...[/cyan]")
        open_file_in_editor(new_output)

        print(f"[yellow]Please edit '{new_output}' with the new filenames and save the file.[/yellow]")
        input("Press Enter to continue with renaming after you have saved the new filenames file.")

        print(f"[cyan]Renaming files based on '{new_output}'...[/cyan]")
        rename_files(input_dir, original_output, new_output)
        print("[green]File renaming process completed.[/green]")

    elif args.operation == "visualize":
        directory = Path(args.input_directory).resolve()
        display_directory_tree(directory)


if __name__ == "__main__":
    main()
