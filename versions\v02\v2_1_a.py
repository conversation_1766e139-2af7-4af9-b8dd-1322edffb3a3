# 'ChatGPT 4o'
# 'https://chatgpt.com/c/6742dd99-3c80-8008-90ec-b6f8f3d5dfda'

import os
import hashlib
from rich import print
from rich.progress import Progress
from rich.tree import Tree

def sha256_checksum(file_path):
    """Compute the SHA256 hash of a file."""
    sha256 = hashlib.sha256()
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b''):
            sha256.update(chunk)
    return sha256.hexdigest()

def get_file_hashes(input_directory, include_subfolders=True):
    """
    Get the SHA256 hash and relative path of each file in a directory (and subdirectories if enabled).
    """
    file_hashes = []
    for root, dirs, files in os.walk(input_directory):
        for file in files:
            file_path = os.path.join(root, file)
            if os.path.isfile(file_path) and os.access(file_path, os.R_OK):
                relative_path = os.path.relpath(file_path, input_directory)
                file_hash = sha256_checksum(file_path)
                file_hashes.append((file_hash, relative_path))
        if not include_subfolders:
            break
    return file_hashes

def write_file_hashes(input_directory, org_file, new_file):
    """
    Write SHA256 hashes and filenames to two text files (original and new).
    """
    print(f"[bold green]Scanning files in:[/] {input_directory}")
    file_hashes = get_file_hashes(input_directory)
    formatted_hashes = [f"{h[0]}|{h[1]}" for h in file_hashes]

    with open(org_file, "w") as f:
        f.write("\n".join(formatted_hashes))
    with open(new_file, "w") as f:
        f.write("\n".join(formatted_hashes))

    print(f"[bold blue]Hashes written to:[/]\n - {org_file}\n - {new_file}")

def rename_files(input_directory, new_file):
    """
    Rename files in the directory based on new filenames in the new_file.
    """
    try:
        with open(new_file, "r") as f:
            new_filenames = [line.strip() for line in f.readlines()]
    except FileNotFoundError:
        print(f"[bold red]Error:[/] {new_file} not found!")
        return

    # Parse new filenames and hashes
    new_filenames_hash = []
    new_filenames_name = []
    for new in new_filenames:
        split_line = new.split('|')
        if len(split_line) != 2:
            print(f"[bold red]Invalid format in line:[/] {new}")
            continue
        new_filenames_hash.append(split_line[0])
        new_filenames_name.append(split_line[1])

    filenames_with_hash = get_file_hashes(input_directory)
    renamed_count = 0

    # Rename files based on hash matching
    for file_hash, relative_path in filenames_with_hash:
        if file_hash in new_filenames_hash:
            index = new_filenames_hash.index(file_hash)
            new_name = new_filenames_name[index]
            old_path = os.path.join(input_directory, relative_path)
            new_path = os.path.join(input_directory, new_name)

            # Ensure no overwriting
            if os.path.exists(new_path):
                print(f"[bold yellow]Skipped:[/] {new_name} already exists.")
                continue

            os.rename(old_path, new_path)
            print(f"[bold green]Renamed:[/] {relative_path} → {new_name}")
            renamed_count += 1

    if renamed_count > 0:
        print(f"[bold green]Renaming complete:[/] {renamed_count} files renamed.")
    else:
        print("[bold yellow]No files renamed. Ensure hashes match or modify the new file list.")

def visualize_directory(input_directory):
    """
    Display a tree visualization of the directory structure using Rich.
    """
    tree = Tree(f"[bold magenta]:open_file_folder: {input_directory}", guide_style="bold bright_blue")
    for root, dirs, files in os.walk(input_directory):
        relative_root = os.path.relpath(root, input_directory)
        branch = tree.add(f":file_folder: {relative_root}" if relative_root != '.' else ":file_folder: .")
        for file in files:
            branch.add(f":page_facing_up: {file}")
    print(tree)

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Utility to rename files based on hashes and text files.")
    parser.add_argument("input_directory", help="Path to the directory containing files to process.")
    parser.add_argument("--org_file", default="FilesToText_01__filenames_ORG.txt", help="Path to save original filenames.")
    parser.add_argument("--new_file", default="FilesToText_01__filenames_NEW.txt", help="Path to save or read new filenames.")
    parser.add_argument("--action", choices=["write", "rename", "visualize"], required=True, help="Action to perform.")
    parser.add_argument("--no_subfolders", action="store_true", help="Do not include subfolders in processing.")
    args = parser.parse_args()

    input_directory = os.path.abspath(args.input_directory)
    org_file = os.path.abspath(args.org_file)
    new_file = os.path.abspath(args.new_file)
    include_subfolders = not args.no_subfolders

    if not os.path.exists(input_directory):
        print(f"[bold red]Error:[/] Input directory does not exist: {input_directory}")
        exit(1)

    if args.action == "write":
        write_file_hashes(input_directory, org_file, new_file)
    elif args.action == "rename":
        rename_files(input_directory, new_file)
    elif args.action == "visualize":
        visualize_directory(input_directory)
