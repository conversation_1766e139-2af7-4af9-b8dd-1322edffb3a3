import os
import pathlib
import tempfile
import unittest
from unittest.mock import MagicMock, patch

from src.core.file_processor import FileProcessor
from src.utils.logging import Logger, LogConfig

class TestFileProcessor(unittest.TestCase):
    """Test cases for FileProcessor."""

    def setUp(self):
        self.logger = Logger(LogConfig())
        self.temp_dir = tempfile.TemporaryDirectory()
        self.test_dir = pathlib.Path(self.temp_dir.name)
        self.processor = FileProcessor(self.test_dir, self.logger)

    def tearDown(self):
        self.temp_dir.cleanup()

    def test_collect_files(self):
        """Tests file collection."""
        # Create test files
        test_files = [
            "test1.txt",
            "test2.txt",
            "subdir/test3.txt"
        ]
        
        for file_path in test_files:
            full_path = self.test_dir / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.touch()

        # Test with subdirs
        files = self.processor.collect_files(include_subdirs=True)
        self.assertEqual(len(files), 3)

        # Test without subdirs
        files = self.processor.collect_files(include_subdirs=False)
        self.assertEqual(len(files), 2)

    def test_rename_file(self):
        """Tests file renaming."""
        source = self.test_dir / "source.txt"
        target = self.test_dir / "target.txt"
        
        source.write_text("test content")
        
        self.assertTrue(self.processor.rename_file(source, target))
        self.assertFalse(source.exists())
        self.assertTrue(target.exists())
        self.assertEqual(target.read_text(), "test content")

    def test_rename_file_nonexistent(self):
        """Tests renaming non-existent file."""
        source = self.test_dir / "nonexistent.txt"
        target = self.test_dir / "target.txt"
        
        self.assertFalse(self.processor.rename_file(source, target))

    def test_rename_file_target_exists(self):
        """Tests renaming when target already exists."""
        source = self.test_dir / "source.txt"
        target = self.test_dir / "target.txt"
        
        source.write_text("source content")
        target.write_text("target content")
        
        self.assertFalse(self.processor.rename_file(source, target))
        self.assertTrue(source.exists())
        self.assertTrue(target.exists())

    @patch('src.core.hash_manager.HashManager.compute_sha256')
    def test_rename_file_verify_hash(self, mock_compute):
        """Tests hash verification during rename."""
        mock_compute.side_effect = ["hash1", "hash1"]
        
        source = self.test_dir / "source.txt"
        target = self.test_dir / "target.txt"
        
        source.write_text("test content")
        
        self.assertTrue(self.processor.rename_file(source, target, verify=True))
        self.assertEqual(mock_compute.call_count, 2)

if __name__ == '__main__':
    unittest.main()
