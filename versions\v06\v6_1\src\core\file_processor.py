import os
import pathlib
import shutil
from typing import Dict, List, Optional, Set, Tuple

from src.utils.logging import Lo<PERSON>, LogLevel
from src.core.hash_manager import HashManager

class FileProcessor:
    """Handles file operations including hashing and renaming."""

    def __init__(self, root_dir: pathlib.Path, logger: Logger):
        self.root_dir = root_dir
        self.logger = logger
        self.hash_manager = HashManager()

    def collect_files(self, include_subdirs: bool = True) -> List[pathlib.Path]:
        """Collects all files in the directory."""
        files = []
        for root, _, filenames in os.walk(self.root_dir):
            for filename in filenames:
                file_path = pathlib.Path(root) / filename
                if self._is_valid_file(file_path):
                    files.append(file_path)
            if not include_subdirs:
                break
        return files

    def _is_valid_file(self, path: pathlib.Path) -> bool:
        """Checks if a file is valid and accessible."""
        if not path.is_file() or not os.access(path, os.R_OK):
            self.logger.log(f"`{path}` is not accessible", LogLevel.WARNING)
            return False
        return True

    def rename_file(self, source: pathlib.Path, target: pathlib.Path, dry_run: bool = False, verify: bool = True) -> bool:
        """Renames a file with optional hash verification."""
        if not source.exists():
            self.logger.log(f"Source missing: {source}", LogLevel.ERROR)
            return False

        if target.exists() and target != source:
            self.logger.log(f"Target exists: {target}", LogLevel.ERROR)
            return False

        try:
            if verify:
                original_hash = HashManager.compute_sha256(source, self.logger)
                if not original_hash:
                    return False

            target.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(source), str(target))

            if verify:
                new_hash = HashManager.compute_sha256(target, self.logger)
                if new_hash != original_hash:
                    self.logger.log(
                        f"Hash verification failed after move: {target}",
                        LogLevel.ERROR
                    )
                    return False

            self.logger.log(
                f'Renamed: "{source.relative_to(self.root_dir)}" → '
                f'"{target.relative_to(self.root_dir)}"',
                LogLevel.CHANGE
            )
            return True

        except OSError as error:
            self.logger.log(f"Failed to rename {source}: {error}", LogLevel.ERROR)
            return False
