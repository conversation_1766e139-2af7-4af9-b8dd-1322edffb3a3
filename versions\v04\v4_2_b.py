import argparse
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
from dataclasses import dataclass
from enum import Enum, auto
from typing import Dict, List, Optional, Set, Tuple

from rich.prompt import Confirm


class LogLevel(Enum):
    PROCESSED = auto()
    WARNING = auto()
    ERROR = auto()
    ACTION = auto()
    SUMMARY = auto()


@dataclass
class LogMessage:
    prefix: str
    style: str


class Logger:
    def __init__(self, enabled_levels: Set[LogLevel]):
        self.enabled_levels = enabled_levels
        self._message_formats = {
            LogLevel.PROCESSED: LogMessage("- Processed:", ""),
            LogLevel.WARNING: LogMessage("- Warning:", "yellow"),
            LogLevel.ERROR: LogMessage("- Error:", "red"),
            LogLevel.ACTION: LogMessage("- Action:", "blue"),
            LogLevel.SUMMARY: LogMessage("\n**Summary:**", "bold")
        }

    def log(self, message: str, level: LogLevel) -> None:
        if level not in self.enabled_levels:
            return
        fmt = self._message_formats[level]
        print(f"{fmt.prefix} {message}")


class FileHasher:
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path) -> Optional[str]:
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            global_logger.log(f"Error reading `{file_path}`: {error}", LogLevel.ERROR)
            return None


class FileProcessor:
    def __init__(self, root_dir: pathlib.Path, include_subdirs: bool):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs

    def collect_file_hashes(self) -> List[Tuple[str, str]]:
        hash_entries = []
        for root, _, files in os.walk(self.root_dir):
            for filename in files:
                file_path = pathlib.Path(root) / filename
                if not self._is_valid_file(file_path):
                    continue

                relative_path = file_path.relative_to(self.root_dir).as_posix()
                file_hash = FileHasher.compute_sha256(file_path)

                if file_hash:
                    hash_entries.append((file_hash, relative_path))
                    global_logger.log(relative_path, LogLevel.PROCESSED)

            if not self.include_subdirs:
                break
        return hash_entries

    def _is_valid_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            global_logger.log(f"`{path}` is not accessible or not a file", LogLevel.WARNING)
            return False
        return True


class HashFileManager:
    def __init__(self, file_path: pathlib.Path):
        self.file_path = file_path

    def write(self, hash_entries: List[Tuple[str, str]]) -> None:
        try:
            # Find the length of the longest filename for padding
            max_filename_length = max(len(filename) for _, filename in hash_entries) + 2  # +2 for quotes

            with self.file_path.open("w", encoding='utf-8') as f:
                f.write("# Hash to Filename Mapping\n")
                for file_hash, filename in sorted(hash_entries, key=lambda x: x[1].lower()):
                    padded_filename = f"'{filename}'".ljust(max_filename_length)
                    f.write(f"- {padded_filename} # | \"{file_hash}\"\n")
            global_logger.log(f"Hashes written to `{self.file_path}`", LogLevel.ACTION)
        except IOError as error:
            global_logger.log(f"Failed to write to `{self.file_path}`: {error}", LogLevel.ERROR)

    def read(self) -> List[Tuple[str, str]]:
        hash_entries = []
        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line in f:
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            global_logger.log(f"Failed to read `{self.file_path}`: {error}", LogLevel.ERROR)
        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str]]:
        line = line.strip()
        if not (line.startswith("- '") and ' # | "' in line and line.endswith('"')):
            return None

        try:
            # Split on the hash separator and handle the padding
            filename_part, hash_part = line.split(" # | \"")
            filename = filename_part.strip("- '").rstrip()  # Remove the leading "- '" and trailing spaces
            file_hash = hash_part[:-1]  # Remove trailing quote
            return (file_hash, filename)
        except (IndexError, ValueError):
            global_logger.log(f"Malformed line in `{self.file_path}`: {line}", LogLevel.WARNING)
            return None


class FileRenamer:
    def __init__(self, root_dir: pathlib.Path):
        self.root_dir = root_dir

    def execute(self, source_hashes: List[Tuple[str, str]], target_hashes: List[Tuple[str, str]],
               dry_run: bool = True) -> bool:
        source_map = dict(source_hashes)
        target_map = dict(target_hashes)
        common_hashes = set(source_map) & set(target_map)

        has_conflicts = False
        for file_hash in common_hashes:
            source_path = self.root_dir / source_map[file_hash]
            target_path = self.root_dir / target_map[file_hash]

            if source_path == target_path:
                global_logger.log(f"No change for `{source_path}`. Skipping.", LogLevel.PROCESSED)
                continue

            if not self._validate_paths(source_path, target_path):
                has_conflicts = True
                continue

            if dry_run:
                global_logger.log(f'Will rename: "{source_path}" -> "{target_path}"', LogLevel.ACTION)
            else:
                self._perform_rename(source_path, target_path)

        self._log_completion(dry_run, has_conflicts)
        return not has_conflicts

    def _validate_paths(self, source: pathlib.Path, target: pathlib.Path) -> bool:
        if not source.exists():
            global_logger.log(f"Source file `{source}` does not exist", LogLevel.WARNING)
            return False
        if target.exists():
            global_logger.log(f"Target file `{target}` already exists", LogLevel.ERROR)
            return False
        return True

    def _perform_rename(self, source: pathlib.Path, target: pathlib.Path) -> None:
        try:
            target.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(source), str(target))
            global_logger.log(f'Renamed: "{source}" -> "{target}"', LogLevel.ACTION)
        except OSError as error:
            global_logger.log(f"Failed to rename `{source}` to `{target}`: {error}", LogLevel.ERROR)

    def _log_completion(self, is_dry_run: bool, has_conflicts: bool) -> None:
        operation = "Dry run" if is_dry_run else "File renaming"
        if has_conflicts:
            global_logger.log(
                f"{operation}: Conflicts detected. Some files may not be processed.",
                LogLevel.WARNING
            )
        else:
            global_logger.log(f"{operation} completed successfully.", LogLevel.SUMMARY)


class DirectoryVisualizer:
    def __init__(self, root_dir: pathlib.Path):
        self.root_dir = root_dir

    def display(self) -> None:
        if not self.root_dir.exists():
            global_logger.log(f"Directory '{self.root_dir}' does not exist", LogLevel.ERROR)
            return

        global_logger.log(f"Directory Structure for `{self.root_dir}`", LogLevel.SUMMARY)
        tree = []
        self._build_tree(self.root_dir, tree)
        for line in tree:
            print(line)

    def _build_tree(self, directory: pathlib.Path, tree: List[str], indent: str = "") -> None:
        try:
            entries = sorted(directory.iterdir(), key=lambda p: (p.is_file(), p.name.lower()))
            for entry in entries:
                if entry.name.startswith("."):
                    continue

                if entry.is_dir():
                    tree.append(f"{indent}- 📁 {entry.name}/")
                    self._build_tree(entry, tree, indent + "  ")
                else:
                    tree.append(f"{indent}- 📄 {entry.name}")
        except PermissionError as error:
            global_logger.log(f"Permission denied: {error}", LogLevel.ERROR)


class FileEditor:
    @staticmethod
    def open(file_path: pathlib.Path) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(('open', str(file_path)))
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(('xdg-open', str(file_path)))
            else:
                global_logger.log(f"Unsupported OS: {sys.platform}", LogLevel.WARNING)
        except Exception as error:
            global_logger.log(f"Failed to open `{file_path}`: {error}", LogLevel.ERROR)


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Batch Rename Utility with SHA256 Verification")
    subparsers = parser.add_subparsers(dest="command", required=True)

    process_parser = subparsers.add_parser("process")
    process_parser.add_argument("directory", type=str)
    process_parser.add_argument("--include-subdirectories", action="store_true")
    process_parser.add_argument("--org-output", type=str, default="FilesToText__filenames_ORG.py")
    process_parser.add_argument("--new-output", type=str, default="FilesToText__filenames_NEW.py")
    process_parser.add_argument("--visualize", action="store_true")

    log_group = process_parser.add_argument_group("logging options")
    for level in LogLevel:
        log_group.add_argument(
            f"--show-{level.name.lower()}",
            action="store_true",
            help=f"Show {level.name.lower()} messages"
        )

    visualize_parser = subparsers.add_parser("visualize")
    visualize_parser.add_argument("directory", type=str)

    return parser.parse_args()


def get_enabled_log_levels(args: argparse.Namespace) -> Set[LogLevel]:
    if args.command != "process":
        return set(LogLevel)

    enabled = set()
    for level in LogLevel:
        if getattr(args, f"show_{level.name.lower()}", False):
            enabled.add(level)

    return enabled or {LogLevel.PROCESSED, LogLevel.ACTION, LogLevel.SUMMARY}


def process_command(args: argparse.Namespace) -> None:
    root_dir = pathlib.Path(args.directory).resolve()
    if not root_dir.is_dir():
        global_logger.log(f"'{root_dir}' is not a valid directory", LogLevel.ERROR)
        sys.exit(1)

    org_file = pathlib.Path(args.org_output).resolve()
    new_file = pathlib.Path(args.new_output).resolve()

    processor = FileProcessor(root_dir, args.include_subdirectories)
    initial_hashes = processor.collect_file_hashes()

    for file_path in (org_file, new_file):
        manager = HashFileManager(file_path)
        manager.write(initial_hashes)

    global_logger.log("Opening new hash file for editing...", LogLevel.ACTION)
    FileEditor.open(new_file)

    if not Confirm.ask("Proceed with renaming? [y/n]: "):
        global_logger.log("Operation aborted by user", LogLevel.WARNING)
        return

    org_manager = HashFileManager(org_file)
    new_manager = HashFileManager(new_file)

    renamer = FileRenamer(root_dir)
    if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):
        if Confirm.ask("Apply these changes? [y/n]: "):
            renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)

            if args.visualize:
                DirectoryVisualizer(root_dir).display()

            for file_path in (org_file, new_file):
                try:
                    file_path.unlink()
                    global_logger.log(f"Deleted `{file_path}`", LogLevel.ACTION)
                except OSError as error:
                    global_logger.log(f"Failed to delete `{file_path}`: {error}", LogLevel.WARNING)


def main() -> None:
    args = parse_args()

    global global_logger
    global_logger = Logger(get_enabled_log_levels(args))

    if args.command == "process":
        process_command(args)
    elif args.command == "visualize":
        DirectoryVisualizer(pathlib.Path(args.directory).resolve()).display()
    else:
        global_logger.log("Unknown command", LogLevel.ERROR)


if __name__ == "__main__":
    main()
