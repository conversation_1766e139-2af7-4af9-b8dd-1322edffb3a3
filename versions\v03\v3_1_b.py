# 'ChatGPT o1-mini'
# 'https://chatgpt.com/c/6742eec4-e1e0-8008-838c-c83a6b6c95b2'

import argparse
import hashlib
import json
import os
import pathlib
import subprocess
import sys
from typing import List, Tuple, Dict

from rich import print
from rich.filesize import decimal
from rich.markup import escape
from rich.prompt import Confirm
from rich.text import Text
from rich.tree import Tree


class HashManager:
    """Handles SHA256 hash computations and validations."""

    @staticmethod
    def compute_hash(file_path: pathlib.Path) -> str:
        """Compute SHA256 hash of a file."""
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(4096), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            print(f"[red]Error reading {file_path}: {error}[/red]")
            return ""


class FileRenamer:
    """Manages the batch renaming process."""

    def __init__(self, directory: pathlib.Path, include_subdirs: bool = True):
        self.directory = directory
        self.include_subdirs = include_subdirs

    def gather_file_hashes(self) -> List[Tuple[str, str]]:
        """Gather SHA256 hashes and relative file paths."""
        hashes = []
        for root, _, files in os.walk(self.directory):
            for filename in files:
                full_path = pathlib.Path(root) / filename
                if full_path.is_file() and full_path.suffix != '.hash' and os.access(full_path, os.R_OK):
                    relative_path = full_path.relative_to(self.directory).as_posix()
                    file_hash = HashManager.compute_hash(full_path)
                    if file_hash:
                        hashes.append((file_hash, relative_path))
                        print(f"Processed: {relative_path}")
            if not self.include_subdirs:
                break
        return hashes

    def write_hashes(self, hashes: List[Tuple[str, str]], output_file: pathlib.Path) -> None:
        """Write hashes and filenames to a text file."""
        try:
            with output_file.open("w", encoding='utf-8') as file:
                for file_hash, filename in hashes:
                    file.write(f"{file_hash}|{filename}\n")
            print(f"[green]Hashes written to {output_file}[/green]")
        except IOError as error:
            print(f"[red]Failed to write to {output_file}: {error}[/red]")

    def read_hashes(self, file_path: pathlib.Path) -> List[Tuple[str, str]]:
        """Read hashes and filenames from a text file."""
        hashes = []
        try:
            with file_path.open("r", encoding='utf-8') as file:
                for line in file:
                    parts = line.strip().split('|')
                    if len(parts) == 2:
                        hashes.append((parts[0], parts[1]))
        except IOError as error:
            print(f"[red]Failed to read {file_path}: {error}[/red]")
        return hashes

    def validate_mappings(self, original_hashes: List[Tuple[str, str]], new_hashes: List[Tuple[str, str]]) -> bool:
        """Validate that hashes remain consistent after renaming."""
        original_dict = {hash_val: original_name for hash_val, original_name in original_hashes}
        new_dict = {hash_val: new_name for hash_val, new_name in new_hashes}

        # Check if all hashes in original are present in new
        for hash_val in original_dict:
            if hash_val not in new_dict:
                print(f"[red]Hash {hash_val} missing in new mappings.[/red]")
                return False
        print("[green]All hashes are consistent in the new mappings.[/green]")
        return True

    def execute_renaming(self, original_hashes: List[Tuple[str, str]], new_hashes: List[Tuple[str, str]], dry_run: bool = False) -> None:
        """Execute renaming based on the edited new hash file."""
        new_dict = {hash_val: new_name for hash_val, new_name in new_hashes}
        changes = []
        conflicts = []

        for hash_val, original_name in original_hashes:
            new_name = new_dict.get(hash_val)
            if new_name:
                original_path = self.directory / original_name
                new_path = self.directory / new_name
                changes.append((original_path, new_path))
                if new_path.exists() and original_path != new_path:
                    conflicts.append(new_path)
            else:
                print(f"[red]No new filename found for hash: {hash_val} (File: {original_name})[/red]")

        if conflicts:
            print(f"[yellow]Conflicts detected: {len(conflicts)} files already exist with the new names.[/yellow]")
            if not Confirm.ask("Do you want to proceed and skip conflicting files?"):
                print("[red]Operation aborted due to conflicts.[/red]")
                return

        if dry_run:
            print("[blue]Dry Run Mode: No changes will be made. The following operations would occur:[/blue]")
            for original, new in changes:
                if original != new:
                    print(f"[cyan]Renamed: {original.relative_to(self.directory)} -> {new.relative_to(self.directory)}[/cyan]")
            return

        for original, new in changes:
            if original != new:
                if new.exists():
                    print(f"[yellow]Conflict: {new.relative_to(self.directory)} already exists. Skipping.{new}[/yellow]")
                    continue
                try:
                    original.rename(new)
                    print(f"[green]Renamed: {original.relative_to(self.directory)} -> {new.relative_to(self.directory)}[/green]")
                except OSError as error:
                    print(f"[red]Failed to rename {original.relative_to(self.directory)} to {new.relative_to(self.directory)}: {error}[/red]")

        print("[green]File renaming completed successfully.[/green]")


class DirectoryVisualizer:
    """Generates a visual representation of the directory structure."""

    @staticmethod
    def create_visual_text(path: pathlib.Path) -> Text:
        """Create formatted text for directory visualization."""
        filename = path.name
        text = Text(filename, "green").highlight_regex(r"\..*$", "bold red")
        text.stylize(f"link file://{path.resolve()}")
        try:
            size = path.stat().st_size
        except OSError:
            size = 0
        text.append(f" ({decimal(size)})", "blue")
        icon = "📁 " if path.is_dir() else "📄 "
        return Text(icon) + text

    @staticmethod
    def build_directory_tree(directory: pathlib.Path, tree: Tree) -> None:
        """Recursively build a tree structure of the directory."""
        try:
            entries = sorted(
                directory.iterdir(),
                key=lambda p: (p.is_file(), p.name.lower()),
            )
        except PermissionError as error:
            print(f"[red]Permission denied: {error}[/red]")
            return

        for entry in entries:
            if entry.name.startswith("."):
                continue
            if entry.is_dir():
                branch = tree.add(f"[bold magenta]{escape(entry.name)}")
                DirectoryVisualizer.build_directory_tree(entry, branch)
            else:
                tree.add(DirectoryVisualizer.create_visual_text(entry))

    @staticmethod
    def visualize(directory: pathlib.Path) -> None:
        """Display the directory tree using Rich."""
        if not directory.exists():
            print(f"[red]Error: Directory '{directory}' does not exist.[/red]")
            return

        tree = Tree(f"[bold blue]{directory}[/bold blue]", guide_style="bold bright_blue")
        DirectoryVisualizer.build_directory_tree(directory, tree)
        print(tree)


def open_in_editor(file_path: pathlib.Path) -> None:
    """Open a file in the default text editor."""
    try:
        if sys.platform.startswith('darwin'):
            subprocess.call(('open', str(file_path)))
        elif os.name == 'nt':
            os.startfile(str(file_path))
        elif os.name == 'posix':
            subprocess.call(('xdg-open', str(file_path)))
        else:
            print(f"[yellow]Unsupported OS for opening files: {sys.platform}[/yellow]")
    except Exception as error:
        print(f"[red]Failed to open {file_path}: {error}[/red]")


def add_context_menu(executable_path: str) -> None:
    """Add the utility to the Windows Explorer context menu."""
    reg_content = f"""
Windows Registry Editor Version 5.00

[HKEY_CLASSES_ROOT\\Directory\\Background\\shell\\BatchRenameUtility]
@="Batch Rename Utility"

[HKEY_CLASSES_ROOT\\Directory\\Background\\shell\\BatchRenameUtility\\command]
@="\"{executable_path}\" process \"%V\""
    """
    reg_file = pathlib.Path("add_batch_rename_utility.reg")
    try:
        with reg_file.open("w", encoding='utf-8') as f:
            f.write(reg_content.strip())
        print(f"[green]Registry file '{reg_file}' created successfully.[/green]")
        print("[cyan]To add the context menu, double-click the .reg file and confirm the prompts.[/cyan]")
    except IOError as error:
        print(f"[red]Failed to create registry file: {error}[/red]")


def parse_args() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Batch Rename Utility with SHA256 Verification")
    subparsers = parser.add_subparsers(dest="command", required=True, help="Available commands")

    # Process Command
    process_parser = subparsers.add_parser("process", help="Generate hash files and rename files")
    process_parser.add_argument("directory", type=str, help="Target directory for processing")
    process_parser.add_argument("--include-subdirectories", action="store_true", help="Include subdirectories in processing")
    process_parser.add_argument("--org-output", type=str, default="rename_map_org.txt", help="Output file for original filenames and hashes")
    process_parser.add_argument("--new-output", type=str, default="rename_map_new.txt", help="Output file for new filenames and hashes")
    process_parser.add_argument("--dry-run", action="store_true", help="Perform a dry run without making changes")

    # Visualize Command
    visualize_parser = subparsers.add_parser("visualize", help="Display directory tree")
    visualize_parser.add_argument("directory", type=str, help="Directory to visualize")

    # Add Context Menu Command
    context_menu_parser = subparsers.add_parser("add-context-menu", help="Add the utility to Windows Explorer context menu")
    context_menu_parser.add_argument("executable_path", type=str, help="Path to the compiled executable")

    return parser.parse_args()


def main() -> None:
    """Main function to execute based on user command."""
    args = parse_args()

    if args.command == "process":
        target_dir = pathlib.Path(args.directory).resolve()
        org_file = pathlib.Path(args.org_output).resolve()
        new_file = pathlib.Path(args.new_output).resolve()

        if not target_dir.is_dir():
            print(f"[red]Error: '{target_dir}' is not a valid directory.[/red]")
            sys.exit(1)

        renamer = FileRenamer(target_dir, include_subdirs=args.include_subdirectories)

        # Operation A: Generate hash files
        print(f"[cyan]Generating hash files for '{target_dir}'...[/cyan]")
        original_hashes = renamer.gather_file_hashes()
        renamer.write_hashes(original_hashes, org_file)
        renamer.write_hashes(original_hashes, new_file)
        print("[cyan]Hash files generated successfully.[/cyan]")

        # Open the NEW hash file in the default editor
        print(f"[cyan]Opening '{new_file}' for editing...[/cyan]")
        open_in_editor(new_file)

        # Wait for user confirmation after editing
        if not renamer.validate_mappings(original_hashes, renamer.read_hashes(new_file)):
            print("[red]Validation failed. Please ensure that hashes are consistent before proceeding.[/red]")
            sys.exit(1)

        if args.dry_run:
            renamer.execute_renaming(original_hashes, renamer.read_hashes(new_file), dry_run=True)
            sys.exit(0)

        # Confirmation before executing renaming
        if Confirm.ask("[yellow]Have you reviewed the changes? Proceed with renaming.[/yellow]"):
            # Operation B: Rename files
            print(f"[cyan]Renaming files based on '{new_file}'...[/cyan]")
            new_hashes = renamer.read_hashes(new_file)
            renamer.execute_renaming(original_hashes, new_hashes, dry_run=False)
            print("[green]File renaming process completed.[/green]")
        else:
            print("[red]Operation aborted by user.[/red]")

    elif args.command == "visualize":
        target_dir = pathlib.Path(args.directory).resolve()
        DirectoryVisualizer.visualize(target_dir)

    elif args.command == "add-context-menu":
        executable_path = args.executable_path
        if not pathlib.Path(executable_path).is_file():
            print(f"[red]Error: Executable '{executable_path}' does not exist.[/red]")
            sys.exit(1)
        add_context_menu(executable_path)

    else:
        print("[red]Unknown command.[/red]")


if __name__ == "__main__":
    main()
