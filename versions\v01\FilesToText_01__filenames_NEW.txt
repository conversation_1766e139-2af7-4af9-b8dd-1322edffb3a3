9707220740b3b3e70e6d95ba3012bfbbda052fd41103db950714146666ec1965|FilesToText_01 - Copy (2).py
d4388d5983f6c7a5221b4363022237b736ea61277a8ae448a81d02953e5cac09|FilesToText_01 - Copy (3).py
73314d55eaf82726c92815f609e662f6ec8752d8df8ef147245022bb305c4bef|FilesToText_01 - Copy.py
9707220740b3b3e70e6d95ba3012bfbbda052fd41103db950714146666ec1965|FilesToText_01.py
e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855|FilesToText_01__filenames_NEW.txt
e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855|FilesToText_01__filenames_ORG.txt
6659b444e24f5a5ee26c1287e5e87b54eaaa9627765780fe8c0e2cc1dd2b5a35|FilesToText_02.bat
9707220740b3b3e70e6d95ba3012bfbbda052fd41103db950714146666ec1965|FilesToText_02_2.py
d4388d5983f6c7a5221b4363022237b736ea61277a8ae448a81d02953e5cac09|FilesToText_03.py
c482837d8c4050cd6003c2cdb9c8164579c13e49f9dbbe2c4e70536d05cd5c43|Install or Remove Package.bat
37df2ec64ced3d091a5244bdb88272f63bcdb98005e9e3bed08b7cbcf9afb893|RenameFiles_With_TextEditor_Initialize.bat
5f8ad63ad980ffa0588d694cff7dc8bae935393f6890e5cad894c0dc4ce2d685|requirements.txt