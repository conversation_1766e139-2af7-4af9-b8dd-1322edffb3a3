import hashlib
import os
import time
import win32gui


def sha256_checksum(file_path):
    sha256 = hashlib.sha256()
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b''):
            sha256.update(chunk)
    return sha256.hexdigest()


def get_hashed_file_list(directory):
    # variable for the result
    filenames_with_hash = []
    # get hash and filename for each file in the directory
    for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)
        if os.path.isfile(file_path) and os.access(file_path, os.R_OK):
            file_hash = sha256_checksum(os.path.join(directory, filename))
            filenames_with_hash.append([file_hash, filename])
    # return lists
    return filenames_with_hash


def write_current_filenames(input_directory, out_file_org, out_file_new):
    filenames_with_hash = get_hashed_file_list(input_directory)
    formatted_filenames = []

    # get files/hashes
    for item in filenames_with_hash:
        formatted_filenames.append(f"{item[0]}|{item[1]}")
        print(f"{item[0]}|{item[1]}")
    # write text
    with open(out_file_org, "w") as f:
        for filename in formatted_filenames:
            f.write(f"{filename}\n")
    # write text
    with open(out_file_new, "w") as f:
        for filename in formatted_filenames:
            f.write(f"{filename}\n")


def rename_files(input_directory, out_file_org, out_file_new):
    with open(out_file_new, "r") as f:
        new_filenames = [line.strip() for line in f.readlines()]

    new_filenames_hash = []
    new_filenames_name = []
    for new in new_filenames:
        split_line = new.split('|')
        new_filenames_hash.append(split_line[0])
        new_filenames_name.append(split_line[1])

    filenames_with_hash = get_hashed_file_list(input_directory)

    for item in filenames_with_hash:
        matching_indices = []
        for index, new_filename_hash in enumerate(new_filenames_hash):
            if item[0] == new_filename_hash:
                matching_indices.append(index)

        if matching_indices:
            first_matching_index = matching_indices[0]
            print(first_matching_index)
            print('org: %s' % (item[1]))
            print('new: %s' % (new_filenames_name[first_matching_index]))
            print('\n')
            file_path_org = os.path.join(input_directory, item[1])
            file_path_new = os.path.join(input_directory, new_filenames_name[first_matching_index])
            os.rename(file_path_org, file_path_new)
    else:
        print("The number of filenames in the original and new lists do not match.")


initial_directory = os.getcwd()
input_directory = os.path.join(initial_directory, '.VENV', 'VENV_Utils_Batch')

# Write the current filenames to the original_filenames file
out_textfile_org = os.path.join(initial_directory, 'FilesToText_01__filenames_ORG.txt')
out_textfile_new = os.path.join(initial_directory, 'FilesToText_01__filenames_NEW.txt')
write_current_filenames(input_directory, out_textfile_org, out_textfile_new)

# Rename files based on the new_filenames file
rename_files(input_directory, out_textfile_org, out_textfile_new)