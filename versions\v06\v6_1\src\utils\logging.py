from dataclasses import dataclass
from enum import Enum, auto
from typing import Optional

from rich.console import Console
from rich.panel import Panel
from rich.table import Table

class LogLevel(Enum):
    ERROR = auto()
    WARNING = auto()
    INFO = auto()
    DEBUG = auto()
    CHANGE = auto()
    SKIP = auto()
    SUMMARY = auto()

@dataclass
class LogConfig:
    """Configuration for logging behavior."""
    verbosity: LogLevel = LogLevel.INFO
    show_skipped: bool = False
    show_unchanged: bool = False
    use_colors: bool = True

class Logger:
    """Enhanced logging with rich formatting."""

    def __init__(self, config: LogConfig):
        self.config = config
        self.console = Console(
            color_system="auto" if config.use_colors else None
        )
        self.error_count = 0
        self.warning_count = 0
        self.change_count = 0
        self.skip_count = 0

    def log(
        self,
        message: str,
        level: LogLevel,
        details: Optional[str] = None
    ) -> None:
        """Logs a message with the specified level."""
        if not self._should_log(level):
            return

        if level == LogLevel.SKIP and not self.config.show_skipped:
            self.skip_count += 1
            return

        self._update_counters(level)
        
        style = self._get_style(level)
        prefix = self._get_prefix(level)
        
        styled_message = f"[{style}]{prefix} {message}[/]"
        
        if details and self.config.verbosity == LogLevel.DEBUG:
            styled_message += f"\n  [{style}]{details}[/]"
        
        self.console.print(styled_message)

    def _should_log(self, level: LogLevel) -> bool:
        """Determines if a message should be logged based on verbosity."""
        level_values = {
            LogLevel.ERROR: 0,
            LogLevel.WARNING: 1,
            LogLevel.INFO: 2,
            LogLevel.DEBUG: 3,
            LogLevel.CHANGE: 2,
            LogLevel.SKIP: 2,
            LogLevel.SUMMARY: 1,
        }

        return level_values[level] <= level_values[self.config.verbosity]

    def _update_counters(self, level: LogLevel) -> None:
        """Updates internal counters based on log level."""
        if level == LogLevel.ERROR:
            self.error_count += 1
        elif level == LogLevel.WARNING:
            self.warning_count += 1
        elif level == LogLevel.CHANGE:
            self.change_count += 1

    def _get_style(self, level: LogLevel) -> str:
        """Gets the style for a log level."""
        return {
            LogLevel.ERROR: "bold red",
            LogLevel.WARNING: "yellow",
            LogLevel.INFO: "blue",
            LogLevel.DEBUG: "dim white",
            LogLevel.CHANGE: "bold green",
            LogLevel.SKIP: "dim white",
            LogLevel.SUMMARY: "bold blue",
        }[level]

    def _get_prefix(self, level: LogLevel) -> str:
        """Gets the prefix for a log level."""
        return {
            LogLevel.ERROR: "❌",
            LogLevel.WARNING: "⚠️ ",
            LogLevel.INFO: "ℹ️ ",
            LogLevel.DEBUG: "🔍",
            LogLevel.CHANGE: "✨",
            LogLevel.SKIP: "⏭️ ",
            LogLevel.SUMMARY: "📋",
        }[level]

    def print_summary(self) -> None:
        """Prints a summary of all operations."""
        if not self._should_log(LogLevel.SUMMARY):
            return

        table = Table(title="Operation Summary", show_header=False, box=None)
        table.add_column("Type", style="bold")
        table.add_column("Count", justify="right")

        table.add_row("Changes made", str(self.change_count))
        if self.config.show_skipped:
            table.add_row("Items skipped", str(self.skip_count))
        if self.warning_count > 0:
            table.add_row("Warnings", str(self.warning_count))
        if self.error_count > 0:
            table.add_row("Errors", f"[red]{self.error_count}[/]")

        self.console.print("\n")
        self.console.print(Panel(table, border_style="blue"))
