
The provided script named `RenameWithEditor.py` is a util for batch renaming files through an intermediary text editor (Class-based structure, Loguru-based logging). Here's the code:

```python
""" Utility for batch renaming files through an intermediary text editor (Class-based structure, Loguru-based logging). """

import argparse
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
import re
import unicodedata
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

from dataclasses import dataclass
from enum import Enum
# Rich imports for console interactions
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.box import ROUNDED

# Loguru for logging
from loguru import logger


# ----------------------------
#        Configuration
# ----------------------------

class Config:
    """
    Holds default settings for the Batch Rename Utility.
    """
    DEFAULT_CLEANUP_LOGS = True
    USE_DEFAULT_SETTINGS = True
    # You may introduce additional defaults as needed
    DEFAULT_INCLUDE_SUBDIRECTORIES = False

# ----------------------------
#       Logger Setup
# ----------------------------

class LoggerSetup:
    """Sets up Loguru logger to write YAML logs to a file."""

    @staticmethod
    def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
        """
        Configure Loguru to log messages in YAML format to `log_file`.
        """
        def yaml_sink(message):
            record = message.record

            time_str = record['time'].strftime('%Y-%m-%d %H:%M:%S')
            level_str = f"!{record['level'].name}"
            name_str = record['name']
            func_name_str = f"*{record['function']}"
            line_no = record["line"]
            msg = record["message"]

            # For multi-line messages, use a block scalar in YAML
            if "\n" in msg:
                lines = msg.split("\n")
                message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
            else:
                # Quote message if it has special characters like ':'
                if ":" in msg:
                    message_str = f"'{msg}'"
                else:
                    message_str = msg

            yaml_lines = [
                f"- time: {time_str}",
                f"  level: {level_str}",
                f"  name: {name_str}",
                f"  funcName: {func_name_str}",
                f"  lineno: {line_no}",
                f"  message: {message_str}",
                ""
            ]

            with open(log_file, "a", encoding="utf-8") as f:
                f.write("\n".join(yaml_lines) + "\n")

        # Remove default handlers and add our YAML sink
        logger.remove()
        logger.add(yaml_sink, level=level, enqueue=True)

    @staticmethod
    def initialize_logging(verbosity: str = "INFO"):
        """
        Initialize YAML logging with a level mapped from the string `verbosity`.
        Maps:
          quiet -> ERROR
          normal -> INFO
          verbose -> DEBUG
          debug -> DEBUG
        """
        level_map = {
            "quiet": "ERROR",
            "normal": "INFO",
            "verbose": "DEBUG",
            "debug": "DEBUG"
        }
        selected_level = level_map.get(verbosity.lower(), "INFO")
        LoggerSetup.setup_yaml_logging(level=selected_level)


# ----------------------------
#     Argument Handler
# ----------------------------

class ArgumentHandler:
    # -------------------- Argument Parsing and Prompting --------------------
    def __init__(self):
        self.parser = self.parse_arguments()

    @staticmethod
    def parse_arguments():
        logger.debug("Setting up argument parser.")
        parser = argparse.ArgumentParser(
            description="Batch Rename Utility with SHA256 Verification (using Loguru + Rich)."
        )
        # Adapted from your first script’s approach but tailored to this utility:
        parser.add_argument('-d', '--directory', type=str, help="Target directory for processing")
        parser.add_argument('--include_subdirs', action='store_true',
                            default=Config.DEFAULT_INCLUDE_SUBDIRECTORIES,
                            help="Include subdirectories in file processing")
        parser.add_argument('--prompt', action='store_true', help="Prompt for missing arguments")
        parser.add_argument('-v', '--verbosity',
                            choices=["quiet", "normal", "verbose", "debug"],
                            default="normal",
                            help="Set output verbosity level")

        # New Arguments for Log Cleanup (same approach as first utility)
        cleanup_logs_group = parser.add_mutually_exclusive_group()
        cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true',
                                        help="Clean up log files after successful execution")
        cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false',
                                        help="Do not clean up log files after successful execution")
        parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)

        logger.debug("Argument parser setup complete.")
        return parser

    def get_arguments(self):
        return self.parser.parse_args()

    def prompt_for_missing_arguments(self, args):
        logger.debug("Prompting for missing arguments.")
        console = Console()

        def print_section(title):
            console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)

        if args.prompt:
            print_section("Default Settings")
            use_defaults = Confirm.ask("Use default settings?", default=Config.DEFAULT_CLEANUP_LOGS)
            logger.debug(f"Use defaults: {use_defaults}")

            if not use_defaults:
                # Ask for directory
                print_section("Directory")
                current_dir = args.directory or ""
                args.directory = Prompt.ask("Target directory path?", default=current_dir).strip()

                print_section("Include Subdirectories?")
                args.include_subdirs = Confirm.ask("Include subdirectories?", default=args.include_subdirs)

                print_section("Logging Verbosity")
                choices = ["quiet", "normal", "verbose", "debug"]
                console.print("Verbosity levels:\n  quiet\n  normal\n  verbose\n  debug\n")
                chosen_verbosity = Prompt.ask("Choose verbosity", default=args.verbosity, choices=choices)
                args.verbosity = chosen_verbosity

                # Prompt for cleanup logs
                print_section("Log Cleanup")
                args.cleanup_logs = Confirm.ask("Clean up log file after successful execution?", default=args.cleanup_logs)
            else:
                # Assign defaults if none provided
                args.directory = args.directory or ""
                args.include_subdirs = args.include_subdirs if args.include_subdirs is not None else Config.DEFAULT_INCLUDE_SUBDIRECTORIES
                # Let the existing defaults stand for verbosity and cleanup_logs

        # Validation
        if not args.directory:
            console.print("[red]Error:[/] The following argument is required: directory")
            sys.exit(1)

        dir_path = Path(args.directory)
        if not dir_path.exists() or not dir_path.is_dir():
            console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")
            sys.exit(1)

        logger.debug("Argument prompting complete.")
        return args


# ------------------
#    File Hasher
# ------------------

class FileHasher:
    """Computes SHA256 hashes for files."""
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path) -> Optional[str]:
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.error(f"Error reading `{file_path}`: {error}")
            return None


# ------------------
#  File Processor
# ------------------

class FileProcessor:
    """Processes files to collect their hashes."""

    def __init__(self, root_dir: pathlib.Path, include_subdirs: bool):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs

    def collect_file_hashes(self) -> List[Tuple[str, str]]:
        hash_entries = []
        for root, _, files in os.walk(self.root_dir):
            for filename in files:
                file_path = pathlib.Path(root) / filename
                if not self._is_accessible_file(file_path):
                    continue

                relative_path = file_path.relative_to(self.root_dir).as_posix()
                file_hash = FileHasher.compute_sha256(file_path)
                if file_hash:
                    hash_entries.append((file_hash, relative_path))
                    logger.debug(f"Processed: {relative_path}")

            if not self.include_subdirs:
                break
        return hash_entries

    def _is_accessible_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            logger.warning(f"`{path}` is not accessible or not a file")
            return False
        return True


# ------------------
#  Hash File Manager
# ------------------

class HashFileManager:
    """Manages reading and writing hash files."""

    def __init__(self, file_path: pathlib.Path):
        self.file_path = file_path

    def write(self, hash_entries: List[Tuple[str, str]]) -> None:
        try:
            max_length = max((len(filename) for _, filename in hash_entries), default=0) + 2
            with self.file_path.open("w", encoding='utf-8') as f:
                f.write("# Hash to Filename Mapping\n")
                for file_hash, filename in sorted(hash_entries, key=lambda x: x[1].lower()):
                    padded_filename = f"'{filename}'".ljust(max_length)
                    f.write(f"- {padded_filename} # | \"{file_hash}\"\n")
            logger.info(f"Hash file written: {self.file_path.name}")
        except IOError as error:
            logger.error(f"Failed to write hash file: {error}")

    def read(self) -> List[Tuple[str, str]]:
        hash_entries = []
        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line in f:
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            logger.error(f"Failed to read hash file: {error}")
        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str]]:
        line = line.strip()
        if not (line.startswith("- '") and ' # | "' in line and line.endswith('"')):
            return None

        try:
            filename_part, hash_part = line.split(" # | \"")
            filename = filename_part.strip("- '").rstrip()
            file_hash = hash_part[:-1]
            return (file_hash, filename)
        except (IndexError, ValueError):
            logger.warning(f"Invalid hash file entry: {line}")
            return None


# ------------------
#   File Renamer
# ------------------

class FileRenamer:
    """Handles the renaming of files based on hash comparisons."""

    def __init__(self, root_dir: pathlib.Path):
        self.root_dir = root_dir

    def execute(
        self,
        source_hashes: List[Tuple[str, str]],
        target_hashes: List[Tuple[str, str]],
        dry_run: bool = True
    ) -> bool:
        source_map = self._map_hash_to_paths(source_hashes)
        target_map = self._map_hash_to_paths(target_hashes)

        rename_pairs = self._determine_rename_pairs(source_map, target_map)
        conflicts = False

        if dry_run:
            self._preview_renames(rename_pairs)

        for src_rel, tgt_rel in rename_pairs:
            src_path = self.root_dir / src_rel
            tgt_path = self.root_dir / tgt_rel

            if src_rel == tgt_rel:
                logger.debug(f"Unchanged: {src_rel}")
                continue

            if not self._validate_paths(src_path, tgt_path):
                conflicts = True
                continue

            if dry_run:
                logger.info(f'Will rename: "{src_rel}" → "{tgt_rel}"')
            else:
                self._perform_rename(src_path, tgt_path, src_rel, tgt_rel)

        self._log_completion(dry_run, conflicts)
        return not conflicts

    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        hash_map: Dict[str, List[str]] = {}
        for file_hash, path in hash_entries:
            hash_map.setdefault(file_hash, []).append(path)
        return hash_map

    def _determine_rename_pairs(
        self,
        source_map: Dict[str, List[str]],
        target_map: Dict[str, List[str]],
    ) -> List[Tuple[str, str]]:
        pairs: List[Tuple[str, str]] = []
        processed_targets: Set[str] = set()

        for file_hash, src_paths in source_map.items():
            tgt_paths = target_map.get(file_hash, [])
            for src in src_paths:
                if any(src == pair[0] for pair in pairs):
                    # Already paired
                    continue

                available_tgts = [t for t in tgt_paths if t not in processed_targets]
                if not available_tgts:
                    logger.warning(f"No matching hash for: {src}")
                    continue

                best_match = self._select_best_match(src, available_tgts)
                if best_match:
                    pairs.append((src, best_match))
                    processed_targets.add(best_match)

        return pairs

    def _select_best_match(self, source: str, targets: List[str]) -> Optional[str]:
        source_clean = self._clean_name(source)
        best_similarity = -1.0
        best_target = None

        for tgt in targets:
            tgt_clean = self._clean_name(tgt)
            similarity = self._name_similarity(source_clean, tgt_clean)
            if similarity > best_similarity:
                best_similarity = similarity
                best_target = tgt

        if best_target:
            logger.debug(
                f"Best match for '{source}' is '{best_target}' with similarity {best_similarity:.2f}"
            )
        else:
            logger.warning(f"No suitable match found for '{source}'")

        return best_target

    @staticmethod
    def _name_similarity(name1: str, name2: str) -> float:
        matches = sum(a == b for a, b in zip(name1, name2))
        max_len = max(len(name1), len(name2))
        return matches / max_len if max_len else 0

    @staticmethod
    def _clean_name(name: str) -> str:
        # Normalize to ASCII, remove special chars
        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')
        name = name.lower()
        name = pathlib.Path(name).stem
        name = re.sub(r'[^a-z0-9]', '', name)
        return name

    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:
        if not src.exists():
            logger.warning(f"Source missing: {src.relative_to(self.root_dir)}")
            return False
        if tgt.exists() and tgt != src:
            logger.error(f"Target exists: {tgt.relative_to(self.root_dir)}")
            return False
        return True

    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str) -> None:
        try:
            tgt.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src), str(tgt))
            logger.info(f'Renamed: "{src_rel}" → "{tgt_rel}"')
        except OSError as error:
            logger.error(f"Failed to rename {src_rel}: {error}")

    def _preview_renames(self, rename_pairs: List[Tuple[str, str]]) -> None:
        if not rename_pairs:
            logger.warning("No files require renaming")
            return

        table = Table(
            title="Pending Rename Operations",
            show_header=True,
            header_style="bold blue",
            box=ROUNDED
        )
        table.add_column("Operation", style="cyan", width=4)
        table.add_column("Source", style="white")
        table.add_column("Target", style="green")

        changes = 0
        for src, tgt in rename_pairs:
            if src != tgt:
                table.add_row("→", src, tgt)
                changes += 1

        if changes:
            console = Console()
            console.print("\n")
            console.print(Panel(table, border_style="blue"))
            console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")
        else:
            logger.warning("No files require renaming")

    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:
        operation = "Dry run" if dry_run else "File renaming"
        if has_conflicts:
            logger.warning(f"{operation}: Conflicts detected, some files skipped")
        else:
            logger.info(f"{operation} completed successfully")


# ------------------
#    File Editor
# ------------------

class FileEditor:
    """Opens files using the default system editor."""

    @staticmethod
    def open(file_path: pathlib.Path) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', str(file_path)])
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(['xdg-open', str(file_path)])
            else:
                logger.warning(f"Unsupported platform: {sys.platform}")
        except Exception as error:
            logger.error(f"Failed to open editor: {error}")


# ----------------------------
#     Main App Class
# ----------------------------

class BatchRenameApp:
    """
    Encapsulates the CLI workflow for batch renaming:
      1) Parse & prompt for Arguments
      2) Initialize Logging
      3) Collect & write .original_hashes.py & .new_hashes.py
      4) Edit & confirm renaming
      5) Cleanup
    """

    def __init__(self):
        self.arg_handler = ArgumentHandler()
        self.args: Optional[argparse.Namespace] = None

    def run(self) -> None:
        # 1) Parse & prompt
        self.args = self.arg_handler.get_arguments()
        self.args = self.arg_handler.prompt_for_missing_arguments(self.args)

        # 2) Initialize Logging with Loguru
        LoggerSetup.initialize_logging(self.args.verbosity)

        # 3) Handle the main process
        success = False
        try:
            self.handle_process_command()
            success = True
        except Exception as e:
            logger.error(f"Execution failed: {e}")

        # 4) Cleanup logs if needed and success
        if success and self.args.cleanup_logs:
            logger.remove()  # remove Loguru handler to release file
            log_file = Path("app.log.yml")
            if log_file.exists():
                console = Console()
                try:
                    log_file.unlink()
                    console.print(f"[bold green]Log file {log_file} has been cleaned up.[/bold green]\n")
                except Exception as e:
                    console.print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")

    def handle_process_command(self) -> None:
        """Handles the main processing and renaming of files."""
        console = Console()
        root_dir = pathlib.Path(self.args.directory).resolve()

        org_file = root_dir / ".original_hashes.py"
        new_file = root_dir / ".new_hashes.py"

        try:
            processor = FileProcessor(root_dir, self.args.include_subdirs)
            initial_hashes = processor.collect_file_hashes()

            for file_path in (org_file, new_file):
                manager = HashFileManager(file_path)
                manager.write(initial_hashes)

            logger.info("Opening new hash file for editing...")
            FileEditor.open(new_file)

            if not Confirm.ask("\nProceed with renaming? [y/n]: ", default="y"):
                logger.warning("Operation cancelled by user")
                return

            org_manager = HashFileManager(org_file)
            new_manager = HashFileManager(new_file)

            renamer = FileRenamer(root_dir)
            # Dry run first
            if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):
                if Confirm.ask("\nApply these changes? [y/n]: ", default="y"):
                    renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)

        finally:
            # Cleanup: Attempt to delete the hash files
            for file_path in (org_file, new_file):
                try:
                    if file_path.exists():
                        file_path.unlink()
                        logger.info(f"Cleaned up: {file_path.name}")
                except OSError as error:
                    logger.warning(f"Cleanup failed for {file_path.name}: {error}")


# ------------------
#      Entry
# ------------------

def main() -> None:
    """Main entry point of the utility."""
    app = BatchRenameApp()
    app.run()


if __name__ == "__main__":
    main()
```

---

The way it currently works is that it generates a textfile looking like this:

```
# Hash to Filename Mapping
- 'filename1.txt' # | "a8280169b91551799750bce7540f5eb1a12e700cf5846e4593de32ebed8ad208"
- 'filename2.txt' # | "5742b246b010a2f1633abff74856ae95410adfac8f574b4247db9d280f5db93d"
- 'filename3.txt' # | "30f415e1fbdc8ec8c5739d9ca5eb982b13e2612273538f87ecef70df9d60444d"
- 'filename4.txt' # | "44136fa355b3678a1146ad16f7e8649e94fb4fc21fe77e8310c060f61caaff8a"
- 'filename5.txt' # | "5beeefd47276a5481fecbd9cd12d9bf16e07ccafcc494519aa7f501e7f051b0f"
- 'filename6.txt' # | "b61bc81cd94101b9d7b16aab5fa2277b9f51f6d82912755d576694244c122d4f"
- 'filename7.txt' # | "d8054cc6afa1f7caf4163756e98164b8e2c9524bc70e7984f6477b2a77342031"
```

---


Your goal is to modify it such that it changes the header to represent three columns, then for each file it will prefix the line with each file's datemodified (formatted like this `""" {yyyy.mm.dd} """`). Example:

```
#  YYYY.MM.DD  | FILENAME                | FILEHASH
""" 2025.03.07 | """ - 'filename1.txt' # | "a8280169b91551799750bce7540f5eb1a12e700cf5846e4593de32ebed8ad208"
""" 2025.03.07 | """ - 'filename2.txt' # | "5742b246b010a2f1633abff74856ae95410adfac8f574b4247db9d280f5db93d"
""" 2025.03.07 | """ - 'filename3.txt' # | "30f415e1fbdc8ec8c5739d9ca5eb982b13e2612273538f87ecef70df9d60444d"
""" 2025.03.07 | """ - 'filename4.txt' # | "44136fa355b3678a1146ad16f7e8649e94fb4fc21fe77e8310c060f61caaff8a"
""" 2025.03.07 | """ - 'filename5.txt' # | "5beeefd47276a5481fecbd9cd12d9bf16e07ccafcc494519aa7f501e7f051b0f"
""" 2025.03.07 | """ - 'filename6.txt' # | "b61bc81cd94101b9d7b16aab5fa2277b9f51f6d82912755d576694244c122d4f"
""" 2025.03.07 | """ - 'filename7.txt' # | "d8054cc6afa1f7caf4163756e98164b8e2c9524bc70e7984f6477b2a77342031"
```
