[{"role": "user", "content": [{"type": "text", "text": "<task>\nEmploy a phased approach, moving from high-level orientation to detailed analysis and strategic planning:\n\n**Phase 1: Quick Orientation (Rapid Reconnaissance)**\n\n1.  **Technology Stack Identification:** Determine primary languages, frameworks, libraries, databases, message queues, build systems, and major external dependencies.\n2.  **Purpose & Entry Points:** Identify the core purpose of the codebase (via READMEs, project descriptions) and locate the main application entry points and execution triggers (e.g., `main`, server start scripts, event handlers).\n3.  **Structural Overview:** Analyze the top-level directory structure. Identify likely locations for source code, tests, configurations, documentation, build artifacts, UI components, API definitions, etc.\n4.  **Build & Runtime:** Understand the basic steps to build, configure, run, and deploy the application. Identify key configuration files and environment variables.\n5.  **Version Control Scan:** Briefly review version control history (e.g., `git log`) for activity patterns, major refactors, key contributors, and branching strategies. Note commit message quality.\n6.  **Initial Documentation Review:** Scan available documentation (READMEs, CONTRIBUTING guides, wikis, inline comments) for stated goals, architecture overviews, setup instructions, and known issues. Note discrepancies or areas needing verification.\n7.  **Formulate Initial Questions:** Document immediate ambiguities, areas of uncertainty, or hypotheses to investigate further.\n\n**Phase 2: Abstract Mapping (Architecture & Flow Analysis)**\n\n1.  **Component Identification:** Decompose the system into its major logical components (modules, services, layers, classes). Define their responsibilities and boundaries.\n2.  **Architecture Visualization:** Create high-level diagrams (e.g., C4 Context/Container, block diagrams, component diagrams using Mermaid) illustrating the static structure, key components, and their relationships.\n3.  **Data Flow Mapping:** Trace the flow of key data entities through the system. Diagram data sources, transformations, storage mechanisms (databases, caches), and outputs/APIs. Visualize data models.\n4.  **Execution Flow Tracing:** Map critical user journeys or system processes across component boundaries. Use sequence diagrams or activity diagrams (Mermaid) to illustrate runtime interactions and control flow for key scenarios.\n5.  **API & Integration Analysis:** Document internal and external API contracts (e.g., REST endpoints, GraphQL schemas, RPCs). Identify and map integration points with other systems.\n6.  **State Management:** Analyze how application state is managed, especially in complex UI applications or distributed systems.\n\n**Phase 3: Specific Analysis & Strategic Intervention**\n\n1.  **Pattern & Practice Recognition:**\n    * Identify prevalent design patterns (or anti-patterns) and architectural styles (monolith, microservices, event-driven, etc.).\n    * Analyze coding conventions, abstraction levels, and consistency.\n    * Assess implementation of cross-cutting concerns (logging, error handling, security, configuration, caching, monitoring).\n2.  **Quality & Testing Assessment:**\n    * Evaluate the testing strategy (unit, integration, e2e tests).\n    * Assess test coverage, quality of tests, and CI/CD integration.\n    * Identify areas with insufficient testing or poor test practices.\n3.  **Vulnerability, Debt & Opportunity Assessment:**\n    * Pinpoint potential performance bottlenecks, scalability limitations, and security vulnerabilities.\n    * Identify areas of significant technical debt, design flaws, architectural inconsistencies, or code smells.\n    * Detect code duplication, overly complex logic, or areas ripe for refactoring and abstraction.\n    * Assess documentation gaps, inconsistencies between code and docs, and onboarding difficulties.\n4.  **Synthesize Findings & Plan Action:**\n    * Consolidate all findings into a structured report.\n    * Develop a prioritized list of actionable recommendations (e.g., refactoring targets, tech debt reduction, testing improvements, documentation updates).\n    * Suggest a phased approach for implementing changes, considering risk, effort, and impact.\n    * Clearly define boundaries and scope for proposed interventions.\n\n## Guidelines\n\n- **Systematic Approach:** Follow the phases sequentially, but allow for iterative refinement as understanding deepens.\n- **Visualize Actively:** Use diagrams (Mermaid preferred) as tools for thinking and communication, not just final outputs. Keep them synchronized with findings.\n- **Code is Truth:** Validate all assumptions, documentation, and comments against the actual code behavior and commit history.\n- **Prioritize Clarity:** Ensure all generated documentation, diagrams, and recommendations are clear, concise, and unambiguous.\n- **Context is Key:** Consider the project's history, team structure, and business domain when interpreting findings and making recommendations.\n- **Maintainability Focus:** Frame the analysis around improving the long-term health, understandability, and maintainability of the codebase.\n- **Document Rigorously:** Treat the generated analysis and documentation as critical artifacts. Ensure they are accurate, well-organized, and kept up-to-date if changes are made based on the analysis.\n\n## Requirements (for final output)\n\n- **Comprehensiveness:** The analysis must cover the technology stack, architecture, data/control flows, key algorithms, patterns, quality aspects (testing, debt), and actionable recommendations.\n- **Accuracy:** Findings must be fact-based and traceable to evidence in the codebase or its execution.\n- **Clarity & Structure:** The final report must be well-organized, easy to navigate, and use clear language. Diagrams should be appropriately labeled and integrated.\n- **Visualization:** Must include relevant diagrams (e.g., architecture, sequence, data flow using Mermaid) to illustrate complex aspects.\n- **Actionability:** Recommendations must be specific, prioritized, and provide clear steps or areas for further investigation/intervention.\n- **Consistency:** Terminology and level of detail should be consistent throughout the report.\n</task>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n(No open tabs)\n\n# Current Time\n4/3/2025, 6:35:22 PM (Europe/Oslo, UTC+2:00)\n\n# Current Working Directory (c:/Users/<USER>/Desktop/SCRATCH/app_everything2/user/_notes) Files\ndebug-grp-search-ex.txt.py\ndiscarded_snippets.py\nEverything.Unicode.Tables.txt\nevr_scratchpad.py\ngpt_interaction_notes.MD\nNotes_ALL.txt.py\nnotes_column_snippets.py\nnotes_exclusions.py.csv\nnotes_ini.ini\nnotes_regex_snippets.py\nnotes_search_syntax.cpp\nnotes_unsorted_snippets.py\nplay.py\nsearch_ux.png\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<plan_mode_respond>\n<response>\nI see we're looking at what appears to be a collection of notes and code snippets, likely related to search functionality based on the filenames. To properly analyze this codebase following the phased approach you outlined, I'll need to examine the files to understand their purpose and relationships.\n\nLet me start by examining some key files to get a better understanding of what we're working with. Would you like me to begin by:\n\n1. Reading the main documentation files first (like gpt_interaction_notes.MD) to understand the project's purpose\n2. Examining the Python files to understand the code structure and functionality\n3. Looking at configuration files like notes_ini.ini to understand how the system is configured\n4. Checking the search_ux.png to understand the user interface/experience\n5. Starting with a different approach you'd prefer\n\nThis will help me establish a solid foundation for the comprehensive analysis you're looking for.\n</response>\n<options>\n[\"Read the documentation files first\", \"Examine the Python code files\", \"Look at configuration files\", \"Check the search_ux.png image\", \"Take a broader approach by sampling multiple file types\"]\n</options>\n</plan_mode_respond>"}]}, {"role": "user", "content": [{"type": "text", "text": "[plan_mode_respond] Result:"}, {"type": "text", "text": "<user_message>\nRead the documentation files first\n</user_message>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n(No open tabs)\n\n# Current Time\n4/3/2025, 6:35:48 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<read_file>\n<path>gpt_interaction_notes.MD</path>\n</read_file>\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}]}, {"role": "user", "content": [{"type": "text", "text": "[read_file for 'gpt_interaction_notes.MD'] Result:"}, {"type": "text", "text": "\r\n1. **File Extension and RAR Part Files Check**\r\n    - `!FILTER: REGEX:^.*\\.r(?!00)\\d{2}$ AND ext:avi;divx;...;zip`\r\n    - Matches file names that do not end with `.r` followed by two digits (excluding `r00`), combined with a list of specified file extensions.\r\n    \r\n2. **UUID Format**\r\n    - `!FILTER: REGEX:^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-...{12}$`\r\n    - Matches strings formatted as a universally unique identifier (UUID), with 8-4-4-4-12 hex characters.\r\n    \r\n3. **Hexadecimal Values with Underscore**\r\n    - `!FILTER: REGEX:^[A-F0-9]{8}_[A-F0-9]{8}$`\r\n    - Matches strings with two 8-character hexadecimal values separated by an underscore.\r\n    \r\n4. **Long Alphabetical Strings** \r\n    - `!FILTER: REGEX:^[a-z]{25,}|[A-Z]{25,}|[a-zA-Z]{25,}$`\r\n    - Matches strings of 25 or more lowercase letters, uppercase letters, or a mix of both.\r\n    \r\n5. **Specific Format with Hexadecimal and Numbers**\r\n    - `!FILTER: REGEX:^[a-z]{32}_[0-9]+\\.[a-f0-9]{64}$`\r\n    - Matches strings with 32 lowercase letters, followed by an underscore, then numbers, a dot, and 64 hexadecimal characters.\r\n    \r\n6. **Log File Naming**\r\n    - `!FILTER: REGEX:^\\d+\\.log$`\r\n    - Matches filenames that are numbers followed by the `.log` extension.\r\n    \r\n7. **Windows Security Identifier (SID) Format**\r\n    - `!FILTER: REGEX:^S-1-5-21-\\d+-\\d+-\\d+-\\d+$`\r\n    - Matches strings formatted as a specific Windows Security Identifier (SID).\r\n    \r\n8. **Extracting Digits**\r\n    - `!FILTER: REGEX:(\\d)`\r\n    - Matches and extracts individual digits.\r\n    \r\n9. **Extracting Path Segments**\r\n    - `addcolumn:a a:=regexextract($path:,\"(?:(?:^|\\\\)[^\\\\]*){4}\")`\r\n    - Extracts the fourth segment from a file path using regex.\r\n    \r\n10. **Distinct File Extensions with Digit Check**\r\n    - `distinct:ext !REGEX:(\\d)`\r\n    - Filters distinct file extensions that do not contain digits.\r\n    \r\n11. **Sorting by Extension and Date Modified**\r\n    - `distinct:ext !REGEX:(\\d) secondary-sort:dm secondary-sort-descending:ext`\r\n    - Filters distinct file extensions without digits, with secondary sorting by date modified and then by extension.\r\n    \r\n12. **MP3 Files Analysis**\r\n    - `ext:mp3 dm:1year.. column1:=len($REGEX:path:(\\d):)  add-column:column1`\r\n    - Focuses on MP3 files modified in the last year and adds a column counting the digits in their paths.\r\n    \r\n13. **Zip File Existence in Folder**\r\n    - `folder:REGEX:^(.*)$ fileexists:\\1.zip`\r\n    - Finds folders where a zip file exists with the same name.\r\n    \r\n14. **Path Excluding Specific RAR Part Files**\r\n    - `path:download|download AND !REGEX:^.*\\.r(?!00)\\d{2}$ AND ext:avi;divx;...;zip`\r\n    - Matches paths with 'download' and filters out files ending with `.r` followed by two digits (excluding `r00`), combined with a list of specified file extensions.\r\n    \r\n15. **IP Address Matching**\r\n    - `!FILTER: REGEX:\"(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\"`\r\n    - Matches IPv4 addresses.\r\n    \r\n16. **Email Address Format**\r\n    - `!FILTER: REGEX:\"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}\"`\r\n    - Matches email addresses.\r\n    \r\n17. **Time Format Matching**\r\n    - `!FILTER: REGEX:\"\\b(?:01\\d|2[0-3])\\b\" and \"REGEX:\"\\b[0-5]\\d\\b\"`\r\n    - Matches hour and minute components of time.\r\n    \r\n18. **GUID/UUID Format with Braces**\r\n    - `!FILTER: REGEX:\"\\{?[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-...{12}\\}?\"`\r\n    - Matches GUID/UUID strings, optionally enclosed in braces.\r\n    \r\n19. **Case-Insensitive File Extension Matching for '.down'**\r\n    - `!FILTER: REGEX:(?i).\\\\down`\r\n    - Matches file paths ending with '.down', case-insensitively.\r\n    \r\n20. **MP3 and Corresponding JPG File Check**\r\n    - `!FILTER: REGEX:^(.*)\\.mp3$ fileexists:\\1.jpg`\r\n    - Finds MP3 files where a JPG file with the same stem and path exists.\r\n    \r\n21. **File Existence in Backup Folder**\r\n    - `!FILTER: REGEX:^(.*)\\\\([^\\\\]*)$ fileexists:\\1\\\\backup\\\\\\2`\r\n    - Matches files where a file with the same name exists in a backup subfolder.\r\n    \r\n22. **Matching 5 or More Underscores in a String**\r\n    - `!FILTER: REGEX:^(?:[^_]*_){5,}[^_]*$`\r\n    - Matches strings containing five or more underscores.\r\n    \r\n23. **Matching Only Underscores**\r\n    - `!FILTER: REGEX:^(\\_+)$`\r\n    - Matches strings composed entirely of one or more underscores.\r\n    \r\n24. **General File Extension Matching**\r\n    - `!FILTER: REGEX:extension:^mp and REGEX:extension:mp4`\r\n    - Matches file extensions starting with 'mp' and specifically 'mp4'.\r\n    \r\n25. **Path Length Check**\r\n    - `!FILTER: REGEX:path:^(?:[^\\\\]*\\\\){5,}[^\\\\]*$`\r\n    - Matches paths with at least five backslashes (i.e., at least five directory levels).\r\nThese are summaries of the regex patterns used in the provided expressions. The specific use of each regex depends on its context in the overall query or command."}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n(No open tabs)\n\n# Current Time\n4/3/2025, 6:35:54 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<read_file>\n<path>Notes_ALL.txt.py</path>\n</read_file>\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}]}, {"role": "user", "content": [{"type": "text", "text": "[read_file for 'Notes_ALL.txt.py'] Result:"}, {"type": "text", "text": "# =======================================================\r\n# [2025.03.02 12:09]\r\ntd: name:\r\n\r\nname:\".bat*\";\".bak*\";\".cmd*\";\".jinja-*\";\".sublime-*\";\".mcr*\";\".md*\";\".ms*\";\".nss*\";\".py*\";\".py*\";\".txt*\";\".txt*\";\".xml*\";|ext:3dm;3ds;3g2;3gp;3gp2;3gpp;3mf;7z;aac;abc;ac3;ai;aif;aifc;aiff;amr;amv;ape;asf;asx;au;avi;bat;bdmv;bik;blend;c4d;cda;cfg;cmd;cob;cpp;csv;curaproject;d2v;dae;divx;doc;docm;docx;drc;dsa;dsm;dss;dsv;dts;dwf;dwg;dxf;eps;epub;evo;exe;f4v;fbx;fla;flac;flc;fli;flic;flv;gdoc;git;gitignore;glb;gltf;gsheet;gslides;hdmov;hrc;htm;html;iam;ifo;indd;ini;iob;ipj;ipt;it;iv;ivf;js;json;keynote;log;lwo;m1a;m1v;m2a;m2p;m2t;m2ts;m2v;m3u;m4a;m4b;m4p;m4v;ma;max;mb;mcr;md;mid;midi;mka;mkv;mod;mov;mp2;mp2v;mp3;mp4;mp4v;mpa;mpc;mpe;mpeg;mpg;mpls;mpp;mpv2;mpv4;ms;mse;msg;msi;msp;msu;mtl;mts;mxp;mzp;nss;nwc;nwd;nwf;obj;ogg;ogm;ogv;opus;osg;pages;pc3;pdf;ply;pps;ppsx;ppt;pptx;ps1;psd;pss;pva;py;pyi;pyw;qt;raw;ram;rar;ratdvd;rm;rmi;rmm;rmvb;roq;rpm;rtf;scene;scn;scr;skp;smil;smk;snd;spc;sql;step;stl;stp;sublime;swf;tp;tpr;ts;txt;u3d;url;usd;usdc;usdz;vb;vbs;vdx;vob;voc;vp6;vrml;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;wav;weba;webm;wm;wma;wmp;wmv;wpd;wps;wrl;x3d;xlam;xls;xlsb;xlsm;xlsx;xltm;xltx;xm;xml;xsi;yml;zip;zpr;\r\n\r\n\r\nty: !<name:\".bat*\";\".bak*\";\".cmd*\";\".jinja-*\";\".sublime-*\";\".mcr*\";\".md*\";\".ms*\";\".nss*\";\".py*\";\".py*\";\".txt*\";\".txt*\";\".xml*\";|ext:3dm;3ds;3g2;3gp;3gp2;3gpp;3mf;7z;aac;abc;ac3;ai;aif;aifc;aiff;amr;amv;ape;asf;asx;au;avi;bat;bdmv;bik;blend;c4d;cda;cfg;conf;config;cmd;cob;cpp;csv;curaproject;d2v;dae;divx;doc;docm;docx;drc;dsa;dsm;dss;dsv;dts;dwf;dwg;dxf;eps;epub;evo;exe;f4v;fbx;fla;flac;flc;fli;flic;flv;gdoc;git;gitignore;glb;gltf;gsheet;gslides;hdmov;hrc;htm;html;iam;ifo;indd;ini;iob;ipj;ipt;it;iv;ivf;js;json;keynote;log;lnk;lwo;m1a;m1v;m2a;m2p;m2t;m2ts;m2v;m3u;m4a;m4b;m4p;m4v;ma;max;mb;mcr;md;mid;midi;mka;mkv;mod;mov;mp2;mp2v;mp3;mp4;mp4v;mpa;mpc;mpe;mpeg;mpg;mpls;mpp;mpv2;mpv4;ms;mse;msg;msi;msp;msu;mtl;mts;mxp;mzp;nss;nwc;nwd;nwf;obj;ogg;ogm;ogv;opus;osg;pages;pc3;pdf;ply;pps;ppsx;ppt;pptx;ps1;psd;pss;pva;py;pyi;pyw;qt;raw;ram;rar;ratdvd;rm;rmi;rmm;rmvb;roq;rpm;rtf;scene;scn;scr;skp;smil;smk;snd;spc;sql;step;stl;stp;sublime;swf;tp;tpr;ts;txt;u3d;url;usd;usdc;usdz;vb;vbs;vdx;vob;voc;vp6;vrml;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;wav;weba;webm;wm;wma;wmp;wmv;wpd;wps;wrl;x3d;xlam;xls;xlsb;xlsm;xlsx;xltm;xltx;xm;xml;xsi;yml;zip;zpr;|<dir: child-file:ext:ext:3dm;3ds;3g2;3gp;3gp2;3gpp;3mf;7z;aac;abc;ac3;ai;aif;aifc;aiff;amr;amv;ape;asf;asx;au;avi;bat;bdmv;bik;blend;c4d;cda;cfg;conf;config;cmd;cob;cpp;csv;curaproject;d2v;dae;divx;doc;docm;docx;drc;dsa;dsm;dss;dsv;dts;dwf;dwg;dxf;eps;epub;evo;exe;f4v;fbx;fla;flac;flc;fli;flic;flv;gdoc;git;gitignore;glb;gltf;gsheet;gslides;hdmov;hrc;htm;html;iam;ifo;indd;ini;iob;ipj;ipt;it;iv;ivf;js;json;keynote;log;lnk;lwo;m1a;m1v;m2a;m2p;m2t;m2ts;m2v;m3u;m4a;m4b;m4p;m4v;ma;max;mb;mcr;md;mid;midi;mka;mkv;mod;mov;mp2;mp2v;mp3;mp4;mp4v;mpa;mpc;mpe;mpeg;mpg;mpls;mpp;mpv2;mpv4;ms;mse;msg;msi;msp;msu;mtl;mts;mxp;mzp;nss;nwc;nwd;nwf;obj;ogg;ogm;ogv;opus;osg;pages;pc3;pdf;ply;pps;ppsx;ppt;pptx;ps1;psd;pss;pva;py;pyi;pyw;qt;raw;ram;rar;ratdvd;rm;rmi;rmm;rmvb;roq;rpm;rtf;scene;scn;scr;skp;smil;smk;snd;spc;sql;step;stl;stp;sublime;swf;tp;tpr;ts;txt;u3d;url;usd;usdc;usdz;vb;vbs;vdx;vob;voc;vp6;vrml;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;wav;weba;webm;wm;wma;wmp;wmv;wpd;wps;wrl;x3d;xlam;xls;xlsb;xlsm;xlsx;xltm;xltx;xm;xml;xsi;yml;zip;zpr;>>  (sort:dm;path; secondary-sort:!isfolder:) de:\r\n\r\n\r\n\r\n\r\n<name:\".bat*\";\".bak*\";\".cmd*\";\".jinja-*\";\".sublime-*\";\".mcr*\";\".md*\";\".ms*\";\".nss*\";\".py*\";\".py*\";\".txt*\";\".txt*\";\".xml*\";|ext:3dm;3ds;3g2;3gp;3gp2;3gpp;3mf;7z;aac;abc;ac3;ai;aif;aifc;aiff;amr;amv;ape;asf;asx;au;avi;bat;bdmv;bik;blend;c4d;cda;cfg;conf;config;cmd;cob;cpp;csv;curaproject;d2v;dae;divx;doc;docm;docx;drc;dsa;dsm;dss;dsv;dts;dwf;dwg;dxf;eps;epub;evo;exe;f4v;fbx;fla;flac;flc;fli;flic;flv;gdoc;git;gitignore;glb;gltf;gsheet;gslides;h264;hdmov;hrc;htm;html;iam;ifo;indd;ini;iob;ipj;ipt;it;iv;ivf;js;json;keynote;log;lnk;lwo;m1a;m1v;m2a;m2p;m2t;m2ts;m2v;m3u;m4a;m4b;m4p;m4v;ma;max;mb;mcr;md;mid;midi;mka;mkv;mod;mov;mp2;mp2v;mp3;mp4;mp4v;mpa;mpc;mpe;mpeg;mpg;mpls;mpp;mpv2;mpv4;ms;mse;msg;msi;msp;msu;mtl;mts;mxp;mzp;nss;nwc;nwd;nwf;obj;ogg;ogm;ogv;opus;osg;pages;pc3;pdf;ply;pps;ppsx;ppt;pptx;ps1;psd;pss;pva;py;pyi;pyw;qt;raw;ram;rar;ratdvd;rm;rmi;rmm;rmvb;roq;rpm;rtf;scene;scn;scr;skp;smil;smk;snd;spc;sql;step;stl;stp;sublime;swf;tp;tpr;ts;txt;u3d;url;usd;usdc;usdz;vb;vbs;vdx;vob;voc;vp6;vrml;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;wav;weba;webm;wm;wma;wmp;wmv;wpd;wps;wrl;x3d;xlam;xls;xlsb;xlsm;xlsx;xltm;xltx;xm;xml;xsi;yml;zip;zpr;|<dir: child-file:ext:ext:3dm;3ds;3g2;3gp;3gp2;3gpp;3mf;7z;aac;abc;ac3;ai;aif;aifc;aiff;amr;amv;ape;asf;asx;au;avi;bat;bdmv;bik;blend;c4d;cda;cfg;conf;config;cmd;cob;cpp;csv;curaproject;d2v;dae;divx;doc;docm;docx;drc;dsa;dsm;dss;dsv;dts;dwf;dwg;dxf;eps;epub;evo;exe;f4v;fbx;fla;flac;flc;fli;flic;flv;gdoc;git;gitignore;glb;gltf;gsheet;gslides;h264;hdmov;hrc;htm;html;iam;ifo;indd;ini;iob;ipj;ipt;it;iv;ivf;js;json;keynote;log;lnk;lwo;m1a;m1v;m2a;m2p;m2t;m2ts;m2v;m3u;m4a;m4b;m4p;m4v;ma;max;mb;mcr;md;mid;midi;mka;mkv;mod;mov;mp2;mp2v;mp3;mp4;mp4v;mpa;mpc;mpe;mpeg;mpg;mpls;mpp;mpv2;mpv4;ms;mse;msg;msi;msp;msu;mtl;mts;mxp;mzp;nss;nwc;nwd;nwf;obj;ogg;ogm;ogv;opus;osg;pages;pc3;pdf;ply;pps;ppsx;ppt;pptx;ps1;psd;pss;pva;py;pyi;pyw;qt;raw;ram;rar;ratdvd;rm;rmi;rmm;rmvb;roq;rpm;rtf;scene;scn;scr;skp;smil;smk;snd;spc;sql;step;stl;stp;sublime;swf;tp;tpr;ts;txt;u3d;url;usd;usdc;usdz;vb;vbs;vdx;vob;voc;vp6;vrml;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;wav;weba;webm;wm;wma;wmp;wmv;wpd;wps;wrl;x3d;xlam;xls;xlsb;xlsm;xlsx;xltm;xltx;xm;xml;xsi;yml;zip;zpr;>>\r\n\r\n\r\n\r\n!<name:\".bat*\";\".bak*\";\".cmd*\";\".jinja-*\";\".sublime-*\";\".mcr*\";\".md*\";\".ms*\";\".nss*\";\".py*\";\".py*\";\".txt*\";\".txt*\";\".xml*\";|ext:3dm;3ds;3g2;3gp;3gp2;3gpp;3mf;7z;aac;abc;ac3;ai;aif;aifc;aiff;amr;amv;ape;asf;asx;au;avi;bat;bdmv;bik;blend;c4d;cda;cfg;conf;config;cmd;cob;cpp;csv;curaproject;d2v;dae;divx;doc;docm;docx;drc;dsa;dsm;dss;dsv;dts;dwf;dwg;dxf;eps;epub;evo;exe;f4v;fbx;fla;flac;flc;fli;flic;flv;gdoc;git;gitignore;glb;gltf;gsheet;gslides;h264;hdmov;hrc;htm;html;iam;ifo;indd;ini;iob;ipj;ipt;it;iv;ivf;js;json;keynote;log;lnk;lwo;m1a;m1v;m2a;m2p;m2t;m2ts;m2v;m3u;m4a;m4b;m4p;m4v;ma;max;mb;mcr;md;mid;midi;mka;mkv;mod;mov;mp2;mp2v;mp3;mp4;mp4v;mpa;mpc;mpe;mpeg;mpg;mpls;mpp;mpv2;mpv4;ms;mse;msg;msi;msp;msu;mtl;mts;mxp;mzp;nss;nwc;nwd;nwf;obj;ogg;ogm;ogv;opus;osg;pages;pc3;pdf;ply;pps;ppsx;ppt;pptx;ps1;psd;pss;pva;py;pyi;pyw;qt;raw;ram;rar;ratdvd;rm;rmi;rmm;rmvb;roq;rpm;rtf;scene;scn;scr;skp;smil;smk;snd;spc;sql;step;stl;stp;sublime;swf;tp;tpr;ts;txt;u3d;url;usd;usdc;usdz;vb;vbs;vdx;vob;voc;vp6;vrml;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;wav;weba;webm;wm;wma;wmp;wmv;wpd;wps;wrl;x3d;xlam;xls;xlsb;xlsm;xlsx;xltm;xltx;xm;xml;xsi;yml;zip;zpr;|<dir: child-file:ext:ext:3dm;3ds;3g2;3gp;3gp2;3gpp;3mf;7z;aac;abc;ac3;ai;aif;aifc;aiff;amr;amv;ape;asf;asx;au;avi;bat;bdmv;bik;blend;c4d;cda;cfg;conf;config;cmd;cob;cpp;csv;curaproject;d2v;dae;divx;doc;docm;docx;drc;dsa;dsm;dss;dsv;dts;dwf;dwg;dxf;eps;epub;evo;exe;f4v;fbx;fla;flac;flc;fli;flic;flv;gdoc;git;gitignore;glb;gltf;gsheet;gslides;h264;hdmov;hrc;htm;html;iam;ifo;indd;ini;iob;ipj;ipt;it;iv;ivf;js;json;keynote;log;lnk;lwo;m1a;m1v;m2a;m2p;m2t;m2ts;m2v;m3u;m4a;m4b;m4p;m4v;ma;max;mb;mcr;md;mid;midi;mka;mkv;mod;mov;mp2;mp2v;mp3;mp4;mp4v;mpa;mpc;mpe;mpeg;mpg;mpls;mpp;mpv2;mpv4;ms;mse;msg;msi;msp;msu;mtl;mts;mxp;mzp;nss;nwc;nwd;nwf;obj;ogg;ogm;ogv;opus;osg;pages;pc3;pdf;ply;pps;ppsx;ppt;pptx;ps1;psd;pss;pva;py;pyi;pyw;qt;raw;ram;rar;ratdvd;rm;rmi;rmm;rmvb;roq;rpm;rtf;scene;scn;scr;skp;smil;smk;snd;spc;sql;step;stl;stp;sublime;swf;tp;tpr;ts;txt;u3d;url;usd;usdc;usdz;vb;vbs;vdx;vob;voc;vp6;vrml;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;wav;weba;webm;wm;wma;wmp;wmv;wpd;wps;wrl;x3d;xlam;xls;xlsb;xlsm;xlsx;xltm;xltx;xm;xml;xsi;yml;zip;zpr;>> de: sfda:\r\n\r\n\r\n<name:\".bat*\";\".bak*\";\".cmd*\";\".jinja-*\";\".sublime-*\";\".mcr*\";\".md*\";\".ms*\";\".nss*\";\".py*\";\".py*\";\".txt*\";\".txt*\";\".xml*\";|ext:3dm;3ds;3g2;3gp;3gp2;3gpp;3mf;7z;aac;abc;ac3;ai;aif;aifc;aiff;amr;amv;ani;ape;apng;asf;asx;au;avi;bat;bdmv;bik;blend;bmp;bpg;c4d;cda;cfg;cmd;cob;conf;config;cpp;csv;cur;curaproject;d2v;dae;divx;doc;docm;docx;drc;dsa;dsm;dss;dsv;dts;dwf;dwg;dxf;eps;epub;evo;exe;f4v;fbx;fla;flac;flc;fli;flic;flv;gdoc;gif;git;gitignore;glb;gltf;gsheet;gslides;h264;hdmov;heic;hrc;htm;html;iam;ico;ifo;indd;ini;iob;ipj;ipt;it;iv;ivf;jfi;jfif;jif;jpe;jpeg;jpg;js;json;keynote;lnk;log;lwo;m1a;m1v;m2a;m2p;m2t;m2ts;m2v;m3u;m4a;m4b;m4p;m4v;ma;max;mb;mcr;md;mid;midi;mka;mkv;mod;mov;mp2;mp2v;mp3;mp4;mp4v;mpa;mpc;mpe;mpeg;mpg;mpls;mpp;mpv2;mpv4;ms;mse;msg;msi;msp;msu;mtl;mts;mxp;mzp;nss;nwc;nwd;nwf;obj;ogg;ogm;ogv;opus;osg;pages;pc3;pcx;pdf;ply;png;pps;ppsx;ppt;pptx;ps1;psb;psd;pss;pva;py;pyi;pyw;qt;ra;ram;rar;ratdvd;raw;rle;rm;rmi;rmm;rmvb;roq;rpm;rtf;scene;scn;scr;skp;smil;smk;snd;spc;sql;step;stl;stp;sublime;svg;swf;tga;tif;tiff;tp;tpr;ts;txt;u3d;url;usd;usdc;usdz;vb;vbs;vdx;vob;voc;vp6;vrml;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;wav;weba;webm;webp;wm;wma;wmf;wmp;wmv;wpd;wps;wrl;x3d;xlam;xls;xlsb;xlsm;xlsx;xltm;xltx;xm;xml;xsi;yml;zip;zpr;|<dir: child-file:ext:ext:3dm;3ds;3g2;3gp;3gp2;3gpp;3mf;7z;aac;abc;ac3;ai;aif;aifc;aiff;amr;amv;ani;ape;apng;asf;asx;au;avi;bat;bdmv;bik;blend;bmp;bpg;c4d;cda;cfg;cmd;cob;conf;config;cpp;csv;cur;curaproject;d2v;dae;divx;doc;docm;docx;drc;dsa;dsm;dss;dsv;dts;dwf;dwg;dxf;eps;epub;evo;exe;f4v;fbx;fla;flac;flc;fli;flic;flv;gdoc;gif;git;gitignore;glb;gltf;gsheet;gslides;h264;hdmov;heic;hrc;htm;html;iam;ico;ifo;indd;ini;iob;ipj;ipt;it;iv;ivf;jfi;jfif;jif;jpe;jpeg;jpg;js;json;keynote;lnk;log;lwo;m1a;m1v;m2a;m2p;m2t;m2ts;m2v;m3u;m4a;m4b;m4p;m4v;ma;max;mb;mcr;md;mid;midi;mka;mkv;mod;mov;mp2;mp2v;mp3;mp4;mp4v;mpa;mpc;mpe;mpeg;mpg;mpls;mpp;mpv2;mpv4;ms;mse;msg;msi;msp;msu;mtl;mts;mxp;mzp;nss;nwc;nwd;nwf;obj;ogg;ogm;ogv;opus;osg;pages;pc3;pcx;pdf;ply;png;pps;ppsx;ppt;pptx;ps1;psb;psd;pss;pva;py;pyi;pyw;qt;ra;ram;rar;ratdvd;raw;rle;rm;rmi;rmm;rmvb;roq;rpm;rtf;scene;scn;scr;skp;smil;smk;snd;spc;sql;step;stl;stp;sublime;svg;swf;tga;tif;tiff;tp;tpr;ts;txt;u3d;url;usd;usdc;usdz;vb;vbs;vdx;vob;voc;vp6;vrml;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;wav;weba;webm;webp;wm;wma;wmf;wmp;wmv;wpd;wps;wrl;x3d;xlam;xls;xlsb;xlsm;xlsx;xltm;xltx;xm;xml;xsi;yml;zip;zpr;>>\r\n\r\n grp-ex-safe: (sort:dm;path; secondary-sort:!isfolder:)\r\n\r\nname:\".bat*\";\".cmd*\";\".mcr*\";\".md*\";\".ms*\";\".mse*\";\".nss*\";\".py*\";\".py*\";\".txt*\";\".txt*\";\".xml*\";|grp-ex: (sort:dm;path; secondary-sort:!isfolder:)\r\n\r\n<mytypes:|grp-ex:> sort:dm\r\n<mytypes:|grp-ex:> <sort:path; secondary-sort:!isfolder:> sort:dm\r\n\r\n!path:Git_Work_Jorn !path:Nas_Nuc <mytypes:|grp-ex: !webprofiles: hhash: > sort:dm\r\n\r\nnohighlight:!< (name:\".py*\"|!ext:\"ms|py|nss|bat|cmd|mcr|mse\" path:\"Cache\")|(!ext:\"py|json|config\" path:\"Atlassian/SourceTree\")|(!ext:\"py|json|config\" path:\"Sync.Config\")|path:\"Windows Defender\"|path:\"%WINDIR%\"|path:\".dropbox.cache\"|path:\"appcompat\"|path:\"Autodesk/ADPSDK\"|path:\"Autodesk/ODIS\"|path:\"C:/$WinREAgent\"|path:\"C:/Windows/Logs\"|path:\"Chrome/User Data/Safe Browsing\"|path:\"customDestinations-ms\"|path:\"GoogleUpdater\"|path:\"inetcache\"|path:\"leveldb\"|path:\"Local/Google/Chrome\"|path:\"Local/Microsoft/Windows/History\"|path:\"Logs/SystemIndex\"|path:\"loguploadersettings*.ini\" |path:\"Microsoft/Windows/WebCache\"|path:\"OneDrive/logs\"|path:\"Packages/Microsoft.Windows\"|path:\"programdata/microsoft/network\"|path:\"ProgramData/Oracle/Java\"|path:\"ProgramData/USOPrivate\"|path:\"Recent/AutomaticDestinations\"|path:\"sublimetext/exe/Data/Cache\"|path:\"sublimetext/exe/Data/Crash Reports\"|path:\"sublimetext/exe/Data/Index\"|path:\"sublimetext/exe/Data/Lib\"|path:\"sublimetext/exe/Data/Package Storage\"|path:\"sublimetext/exe/Lib\"|path:\"sync.cache\"|path:\"Sync.Logs\"|path:\"Windows/Prefetch\"|path:\"WindowsApps/MicrosoftTeams\"|path:\"$WINDOWS.~BT\"|path:\"Windows/AppRepository\"|path:\"WindowsApps\"|path:\"Acrobat/WebResources\" > nohighlight:!< (EXT:)|(EXT:\"crx*\")|EXT:\"accdb|adp|ani|aodl|aux|ax|backup|bak|bak2|bak3|bck|bck2|bin|bin64|bk|bkf|bkp|body|bup|cab|cache|cat|cc|ccx|cdmp|cdp|cdpresource|cdxml|changecount|chk|class|com|csproj.user|ctf|cubin|dat|db|db-shm|db-wal|db3|db3|dcl|dep|diskcopy42|diskcopy50|diz|dll|dmp|docbook|dof|ds_store|dsp|dspuser|dvc|edb|edb|err|err2|etl|evtx|fatbin|ftg|fts|gid|hds|hds|hxd|hxd|hyb|ibd|ico|idl|imagemanifest|inc|inf_loc|inline|ipch|ivt|jar|jcp|jcp|jfm|jfm|jtx|jtx|lck|ldb|leveldb|lock|log|log1|log2|log3|map|mdb|mdmp|mmap|mo|mum|ncb|nwl|nwl|od|odlgz|old|otc|parc|part|partial|pb|pck|pdb|pem|pf|pid|pl|pm|pma|pri|pri|prm|prt|ptx|pyc|pyf|qm|qmlc|rcache|res|res2|resx|sav|scc|sdb|sdf|sds|sid|sin|so|spi1d|spj|sqlite|srd-shm|store|sublime-package|pack|sublime_session|sublime-workspace|suo|suo2|swo|swp|sym|sys|tbres|tcl|tek|temp|thumb|thumbsdb|tjg|tlog|tmp|tmpfile|trc|vscdb|wae|lnk|wbk|wer|wlog|woff|woff2|xbf|xin|xlf|zsh|zfe|~debris|~lock|~temp\" > nohighlight:!< EXACT:ANCESTOR-NAME:\".git\"|EXACT:ANCESTOR-NAME:\"Crash Reports\"|EXACT:ANCESTOR-NAME:\".backups\"|EXACT:ANCESTOR-NAME:\"venv\"|EXACT:ANCESTOR-NAME:\"node_modules\" > nohighlight:!< file: CASE:EXACT:NAME:\"Exact Quick Find.sublime-settings\"|CASE:EXACT:NAME:\"globalStorage\"|CASE:EXACT:NAME:\"Local\"|CASE:EXACT:NAME:\"Network Persistent State\" > nohighlight:!< \"automaticDestinations-ms\"|\"Indexed*DB\"|\"microsoft*.xml\"|\"mpcache\"|\"PcaGeneralDb\"|\"Url*.store.*\" > nohighlight:!< (FILE: SIZE:<24bytes)|(DIR: CHILD-COUNT:0)|(empty: child-file-count-from-disk:0 child-folder-count-from-disk:0 !attrib:L) > nohighlight:!( REGEX:\"^\\d{1,30}$\" | REGEX:EXTENSION:\"^\\d{0,}$\" ) nohighlight:!< !ext:\"7z|bat|cmd|csv|doc|docx|exe|git|json|mcr|md|mp3|mp4|ms|nss|pdf|psd|py|rar|rar|scene|url|xls|xlsx|zip\" GUID:|HASH:|MD5:|SID: >\r\n\r\n\r\n# =======================================================\r\n# [2025.02.25 14:25]\r\n< dir: child-file:ext:\"svg\"  >|ext:svg (sort:path; secondary-sort:!isfolder:)\r\n< dir: child-file:ext:\"py\"  >|ext:py (sort:path; secondary-sort:!isfolder:)\r\n# [2025.02.25 14:33]\r\n<dir: child-file:ext:\"py\">|ext:py|pyw|pyi <sort:path; secondary-sort:!isfolder:>\r\n<dir: child-file:ext:\"py\">|ext:py <sort:path; secondary-sort:!isfolder:>\r\next:py|<dir: child-file:ext:\"py\"> <sort:path; secondary-sort:!isfolder:>\r\next:xxxxxx|<dir: child-file:ext:xxxxxx> <sort:path; secondary-sort:!isfolder:>\r\next:xxxxxx|ext:yyyyyy|<dir: child-file:ext:xxxxxx|child-file:ext:yyyyyy> <sort:path; secondary-sort:!isfolder:>\r\next:xxxxxx;yyyyyy;|<dir: child-file:ext:xxxxxx;yyyyyy;> <sort:path; secondary-sort:!isfolder:>\r\n\r\n\r\next:3g2;3gp;3gp2;3gpp;amv;asf;avchd;avi;bik;divx;drm;evo;f4v;flv;hdmov;m1v;m2p;m2t;m2ts;m2v;m4p;m4v;mkv;mov;mp2v;mp4;mp4v;mpe;mpeg;mpg;mpv;mpv2;mpv4;ogg;ogm;ogv;prores;qt;rm;rmvb;roq;smk;swf;tpr;vob;vp6;webm;wmv;|<dir: child-file:ext:3g2;3gp;3gp2;3gpp;amv;asf;avchd;avi;bik;divx;drm;evo;f4v;flv;hdmov;m1v;m2p;m2t;m2ts;m2v;m4p;m4v;mkv;mov;mp2v;mp4;mp4v;mpe;mpeg;mpg;mpv;mpv2;mpv4;ogg;ogm;ogv;prores;qt;rm;rmvb;roq;smk;swf;tpr;vob;vp6;webm;wmv;> <sort:path; secondary-sort:!isfolder:>\r\n\r\next:aac;ac3;aiff;alac;amr;ape;atrac;au;bwf;dts;flac;m4a;m4b;mid;midi;mp2;mp3;ogg;opus;pcm;ra;vqf;wav;wavpack;wma;|<dir: child-file:ext:aac;ac3;aiff;alac;amr;ape;atrac;au;bwf;dts;flac;m4a;m4b;mid;midi;mp2;mp3;ogg;opus;pcm;ra;vqf;wav;wavpack;wma;> <sort:path; secondary-sort:!isfolder:> size:>1kb\r\n\r\next:3dm;3ds;3mf;abc;blend;c4d;cob;curaproject;dae;dwf;dwg;dxf;fbx;glb;gltf;hrc;iam;iob;ipj;ipt;iv;lwo;ma;max;mb;mtl;nwc;nwd;nwf;obj;osg;ply;scn;skp;step;stl;stp;u3d;usd;usdc;usdz;vob;vrml;wrl;x3d;xsi;zpr;|<dir: child-file:ext:3dm;3ds;3mf;abc;blend;c4d;cob;curaproject;dae;dwf;dwg;dxf;fbx;glb;gltf;hrc;iam;iob;ipj;ipt;iv;lwo;ma;max;mb;mtl;nwc;nwd;nwf;obj;osg;ply;scn;skp;step;stl;stp;u3d;usd;usdc;usdz;vob;vrml;wrl;x3d;xsi;zpr;> <sort:path; secondary-sort:!isfolder:>\r\n\r\next:ms;mxp;mzp;mcr;|<dir: child-file:ext:ms;mxp;mzp;mcr;> <sort:path; secondary-sort:!isfolder:>\r\n\r\next:iam;ipt;ipj;idw;ipn;|<dir: child-file:ext:iam;ipt;ipj;idw;ipn;> <sort:path; secondary-sort:!isfolder:>\r\n\r\next:7z;ace;arj;bz2;cab;gz;gzip;jar;r00;r01;r02;r03;r04;r05;r06;r07;r08;r09;r10;r11;r12;r13;r14;r15;r16;r17;r18;r19;r20;r21;r22;r23;r24;r25;r26;r27;r28;r29;rar;tar;tgz;z;zip;|<dir: child-file:ext:7z;ace;arj;bz2;cab;gz;gzip;jar;r00;r01;r02;r03;r04;r05;r06;r07;r08;r09;r10;r11;r12;r13;r14;r15;r16;r17;r18;r19;r20;r21;r22;r23;r24;r25;r26;r27;r28;r29;rar;tar;tgz;z;zip;> <sort:path; secondary-sort:!isfolder:>\r\n\r\n\r\next:ani;apng;bmp;bpg;cur;gif;heic;ico;jfi;jfif;jif;jpe;jpeg;jpg;pcx;png;psb;psd;rle;svg;tga;tif;tiff;webp;wmf;|<dir: child-file:ext:ani;apng;bmp;bpg;cur;gif;heic;ico;jfi;jfif;jif;jpe;jpeg;jpg;pcx;png;psb;psd;rle;svg;tga;tif;tiff;webp;wmf;> <sort:path; secondary-sort:!isfolder:>\r\n\r\next:sublime-project;|<dir: child-file:ext:sublime-project;> <sort:path; secondary-sort:!isfolder:>\r\n\r\next:cmd;bat;vbs;ps1;vb;nss;py;ms;mzp;mse;|<dir: child-file:ext:cmd;bat;vbs;ps1;vb;nss;py;ms;mzp;mse;> <sort:path; secondary-sort:!isfolder:>\r\n\r\next:avi;trec;divx;flv;hdmov;m1v;m2p;m2t;m2ts;m2v;m4v;mkv;mov;mp2v;mp4;mp4v;mpe;mpeg;mpg;mpls;mpv2;mpv4;ogm;ogv;webm;wm;wmp;wmv;7z;ace;arj;bz2;cab;gz;gzip;jar;r00;r01;r02;r03;r04;r05;r06;r07;r08;r09;r10;r11;r12;r13;r14;r15;r16;r17;r18;r19;r20;r21;r22;r23;r24;r25;r26;r27;r28;r29;rar;tar;tgz;z;zip|<dir: child-file:ext:avi;trec;divx;flv;hdmov;m1v;m2p;m2t;m2ts;m2v;m4v;mkv;mov;mp2v;mp4;mp4v;mpe;mpeg;mpg;mpls;mpv2;mpv4;ogm;ogv;webm;wm;wmp;wmv;7z;ace;arj;bz2;cab;gz;gzip;jar;r00;r01;r02;r03;r04;r05;r06;r07;r08;r09;r10;r11;r12;r13;r14;r15;r16;r17;r18;r19;r20;r21;r22;r23;r24;r25;r26;r27;r28;r29;rar;tar;tgz;z;zip> <sort:path; secondary-sort:!isfolder:>\r\n\r\next:3g2;3gp;3gp2;3gpp;amv;asf;asx;avi;bdmv;bik;d2v;divx;drc;dsa;dsm;dss;dsv;evo;f4v;flc;fli;flic;flv;hdmov;ifo;ivf;m1v;m2p;m2t;m2ts;m2v;m4v;mkv;mov;mp2v;mp4;mp4v;mpe;mpeg;mpg;mpls;mpv2;mpv4;mts;ogm;ogv;pss;pva;qt;ram;ratdvd;rm;rmm;rmvb;roq;rpm;smil;smk;swf;tp;tpr;ts;vob;vp6;webm;wm;wmp;wmv;|<dir: child-file:ext:3g2;3gp;3gp2;3gpp;amv;asf;asx;avi;bdmv;bik;d2v;divx;drc;dsa;dsm;dss;dsv;evo;f4v;flc;fli;flic;flv;hdmov;ifo;ivf;m1v;m2p;m2t;m2ts;m2v;m4v;mkv;mov;mp2v;mp4;mp4v;mpe;mpeg;mpg;mpls;mpv2;mpv4;mts;ogm;ogv;pss;pva;qt;ram;ratdvd;rm;rmm;rmvb;roq;rpm;smil;smk;swf;tp;tpr;ts;vob;vp6;webm;wm;wmp;wmv;> <sort:path; secondary-sort:!isfolder:>\r\n\r\next:3g2;3gp;3gp2;3gpp;amv;asf;avi;divx;f4v;flv;mkv;mov;mp4;m4v;mpeg;mpg;m2ts;ogv;rmvb;vob;webm;wmv;|<dir: child-file:ext:3g2;3gp;3gp2;3gpp;amv;asf;avi;divx;f4v;flv;mkv;mov;mp4;m4v;mpeg;mpg;m2ts;ogv;rmvb;vob;webm;wmv;> <sort:path; secondary-sort:!isfolder:>\r\n\r\next:ai;doc;docx;dwg;dxf;msg;eps;epub;gdoc;gsheet;gslides;indd;keynote;md;mpp;pages;pdf;ppt;pptx;psd;rtf;wpd;wps;xls;docm;xlam;xlsb;xlsm;xlsx;xltm;xltx;pps;ppsx;vdx;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;pc3;|<dir: child-file:ext:ai;doc;docx;dwg;dxf;msg;eps;epub;gdoc;gsheet;gslides;indd;keynote;md;mpp;pages;pdf;ppt;pptx;psd;rtf;wpd;wps;xls;docm;xlam;xlsb;xlsm;xlsx;xltm;xltx;pps;ppsx;vdx;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;pc3;> <sort:path; secondary-sort:!isfolder:>\r\n\r\nSIZE:>300kb PATH:CHARS:<190 !path:%TEMP% EXT:trec;3gp;3gp2;3gpp;amv;asf;asx;avi;bdmv;bik;d2v;divx;drc;dsa;dsm;dss;dsv;evo;f4v;flc;fli;flic;flv;hdmov;ifo;ivf;m1v;m2p;m2t;m2ts;m2v;m4v;mkv;mp2v;mp4;mp4v;mpe;mpeg;mpg;mpls;mpv2;mpv4;mov;mts;ogm;ogv;pss;pva;qt;ram;ratdvd;rm;rmm;rmvb;roq;rpm;smil;smk;swf;tp;tpr;ts;vob;vp6;webm;wm;wmp;wmv\r\n\r\n\r\n\r\n<path:\"sublime text\" ext:sublime*;>|<dir: child-file:ext:sublime*;> <sort:path; secondary-sort:!isfolder:>\r\n\r\n\r\n\r\n\r\n((NOHIGHLIGHT:PATH:\"%APPDATA%\" AND PATH:\"Sublime Text\" NOHIGHLIGHT:PATH:\"Sublime Text\" OR REGEX:EXTENSION:\"SUBLIME.*\" OR REGEX:PATH:\"Sublime Text\" OR NOHIGHLIGHT:REGEX:NAME:\"SUBLIME.*\" ) OR (NOHIGHLIGHT:STP: OR NOHIGHLIGHT:STW: OR NOHIGHLIGHT:STT: OR NOHIGHLIGHT:STC: OR NOHIGHLIGHT:STS: OR NOHIGHLIGHT:STM: OR NOHIGHLIGHT:STB: ) AND (rel: ) )\r\n\r\n\r\n\r\n\r\next:conf;eot;otf;pfb;sfd;ttf;|<dir: child-file:ext:conf;eot;otf;pfb;sfd;ttf;> <sort:path; secondary-sort:!isfolder:>\r\n\r\n\r\nSIZE:>24BYTES EXT:cmd;bat;vbs;ps1;vb;nss;py;ms;mzp;mse; SORT:DM;PATH;EXT;NAME;SIZE\r\n\r\n\r\n\r\next:sublime-project rel:\r\n\r\nEXT:ani;apng;bmp;bpg;cur;gif;heic;ico;jfi;jfif;jif;jpe;jpeg;jpg;pcx;png;psb;psd;rle;svg;tga;tif;tiff;webp;wmf;\r\nEXT:7z;ace;arj;bz2;cab;gz;gzip;jar;r00;r01;r02;r03;r04;r05;r06;r07;r08;r09;r10;r11;r12;r13;r14;r15;r16;r17;r18;r19;r20;r21;r22;r23;r24;r25;r26;r27;r28;r29;rar;tar;tgz;z;zip;\r\n\r\n\r\nSIZE:>24BYTES PATH:CHARS:<190 EXT: SORT:DM;PATH;EXT;NAME;SIZE\r\nSIZE:>24BYTES PATH:CHARS:<190 EXT:ms;mxp;mzp;mcr; SORT:DM;PATH;EXT;NAME;SIZE\r\n\r\n((EXT:\"3dm\" OR EXT:\"3mf\" OR EXT:\"3ds\" OR EXT:\"abc\" OR EXT:\"blend\" OR EXT:\"c4d\" OR EXT:\"cob\" OR EXT:\"curaproject\" OR EXT:\"dae\" OR EXT:\"dwf\" OR EXT:\"dwg\" OR EXT:\"dxf\" OR EXT:\"fbx\" OR EXT:\"glb\" OR EXT:\"gltf\" OR EXT:\"hrc\" OR EXT:\"iam\" OR EXT:\"iob\" OR EXT:\"ipj\" OR EXT:\"ipt\" OR EXT:\"iv\" OR EXT:\"lwo\" OR EXT:\"ma\" OR EXT:\"max\" OR EXT:\"mb\" OR EXT:\"nwc\" OR EXT:\"nwd\" OR EXT:\"nwf\" OR EXT:\"obj\" OR EXT:\"osg\" OR EXT:\"ply\" OR EXT:\"scn\" OR EXT:\"skp\" OR EXT:\"step\" OR EXT:\"stl\" OR EXT:\"stp\" OR EXT:\"u3d\" OR EXT:\"usd\" OR EXT:\"usdc\" OR EXT:\"usdz\" OR EXT:\"vob\" OR EXT:\"vrml\" OR EXT:\"wrl\" OR EXT:\"x3d\" OR EXT:\"xsi\" OR EXT:\"zpr\" OR EXT:\"mtl\") AND (rel: SORT:DM;PATH;EXT;NAME;SIZE ) )\r\n\r\n\r\n\r\n\r\n\r\next:aac;ac3;aif;aifc;aiff;amr;ape;au;ogg;cda;dts;fla;flac;it;m1a;m2a;m3u;m4a;m4b;m4p;mid;midi;mka;mod;mp2;mp3;mpa;mpc;ogg;opus;ra;rmi;snd;spc;voc;wav;weba;wma;xm;|<dir: child-file:ext:size:>300kb ext:aac;ac3;aif;aifc;aiff;amr;ape;au;ogg;cda;dts;fla;flac;it;m1a;m2a;m3u;m4a;m4b;m4p;mid;midi;mka;mod;mp2;mp3;mpa;mpc;ogg;opus;ra;rmi;snd;spc;voc;wav;weba;wma;xm;> <sort:path; secondary-sort:!isfolder:> ext:size:>100kb\r\n\r\nEXT:3g2;3gp;3gp2;3gpp;amv;asf;asx;avi;bdmv;bik;d2v;divx;drc;dsa;dsm;dss;dsv;evo;f4v;flc;fli;flic;flv;hdmov;ifo;ivf;m1v;m2p;m2t;m2ts;m2v;m4v;mkv;mp2v;mp4;mp4v;mpe;mpeg;mpg;mpls;mpv2;mpv4;mov;mts;ogm;ogv;pss;pva;qt;ram;ratdvd;rm;rmm;rmvb;roq;rpm;smil;smk;swf;tp;tpr;ts;vob;vp6;webm;wm;wmp;wmv\r\n\r\nEXT:\r\n\r\n# =============================================================================\r\n# [10:15] -> 23.07.2024:\r\ngrp-paths-ex: grp-exts-ex: grp-parents-ex: grp-names-ex: grp-strings-ex: grp-meta-ex: grp-regex-ex: grp-patterns-ex:\r\n#\r\nnohighlight:!< (!ext:\"ms|py|nss|bat|cmd|mcr|mse\" path:\"Cache\")|(!ext:\"py|json|config\" path:\"Atlassian/SourceTree\")|(!ext:\"py|json|config\" path:\"Sync.Config\")|path:\"Windows Defender\"|path:\"%WINDIR%\"|path:\".dropbox.cache\"|path:\"appcompat\"|path:\"Autodesk/ADPSDK\"|path:\"Autodesk/ODIS\"|path:\"C:/$WinREAgent\"|path:\"C:/Windows/Logs\"|path:\"Chrome/User Data/Safe Browsing\"|path:\"customDestinations-ms\"|path:\"GoogleUpdater\"|path:\"inetcache\"|path:\"leveldb\"|path:\"Local/Google/Chrome\"|path:\"Local/Microsoft/Windows/History\"|path:\"Logs/SystemIndex\"|path:\"loguploadersettings*.ini\" |path:\"Microsoft/Windows/WebCache\"|path:\"OneDrive/logs\"|path:\"Packages/Microsoft.Windows\"|path:\"programdata/microsoft/network\"|path:\"ProgramData/Oracle/Java\"|path:\"ProgramData/USOPrivate\"|path:\"Recent/AutomaticDestinations\"|path:\"sublimetext/exe/Data/Cache\"|path:\"sublimetext/exe/Data/Crash Reports\"|path:\"sublimetext/exe/Data/Index\"|path:\"sublimetext/exe/Data/Lib\"|path:\"sublimetext/exe/Data/Package Storage\"|path:\"sublimetext/exe/Lib\"|path:\"sync.cache\"|path:\"Sync.Logs\"|path:\"Windows/Prefetch\"|path:\"WindowsApps/MicrosoftTeams\" > nohighlight:!< (EXT:)|(EXT:\"crx*\")|EXT:\"accdb|adp|ani|aodl|aux|ax|backup|bak|bak2|bak3|bck|bck2|bin|bin64|bk|bkf|bkp|body|bup|cab|cache|cat|cc|ccx|cdmp|cdp|cdpresource|cdxml|changecount|chk|class|com|csproj.user|ctf|cubin|dat|db|db-shm|db-wal|db3|db3|dcl|dep|diskcopy42|diskcopy50|diz|dll|dmp|docbook|dof|ds_store|dsp|dspuser|dvc|edb|edb|err|err2|etl|evtx|fatbin|ftg|fts|gid|hds|hds|hxd|hxd|hyb|ibd|ico|idl|imagemanifest|inc|inf_loc|inline|ipch|ivt|jar|jcp|jcp|jfm|jfm|jtx|jtx|lck|ldb|leveldb|lock|log|log1|log2|log3|map|mdb|mdmp|mmap|mo|mum|ncb|nwl|nwl|od|odlgz|old|otc|parc|part|partial|pb|pck|pdb|pem|pf|pid|pl|pm|pma|pri|pri|prm|prt|ptx|pyc|pyf|qm|qmlc|rcache|res|res2|resx|sav|scc|sdb|sdf|sds|sid|sin|so|spi1d|spj|sqlite|srd-shm|store|sublime-package|pack|sublime_session|sublime-workspace|suo|suo2|swo|swp|sym|sys|tbres|tcl|tek|temp|thumb|thumbsdb|tjg|tlog|tmp|tmpfile|trc|vscdb|wae|lnk|wbk|wer|wlog|woff|woff2|xbf|xin|xlf|zsh|zfe|~debris|~lock|~temp\" > nohighlight:!< EXACT:ANCESTOR-NAME:\".git\"|EXACT:ANCESTOR-NAME:\"Crash Reports\"|EXACT:ANCESTOR-NAME:\".backups\"|EXACT:ANCESTOR-NAME:\"venv\" > nohighlight:!< file: CASE:EXACT:NAME:\"Exact Quick Find.sublime-settings\"|CASE:EXACT:NAME:\"globalStorage\"|CASE:EXACT:NAME:\"Local\"|CASE:EXACT:NAME:\"Network Persistent State\" > nohighlight:!< \"automaticDestinations-ms\"|\"Indexed*DB\"|\"microsoft*.xml\"|\"mpcache\"|\"PcaGeneralDb\"|\"Url*.store.*\" > nohighlight:!< (FILE: SIZE:<24bytes)|(DIR: CHILD-COUNT:0)|(empty: child-file-count-from-disk:0 child-folder-count-from-disk:0 !attrib:L) > nohighlight:!( REGEX:\"^\\d{1,30}$\" | REGEX:EXTENSION:\"^\\d{0,}$\" ) nohighlight:!< !ext:\"7z|bat|cmd|csv|doc|docx|exe|git|json|mcr|md|mp3|mp4|ms|nss|pdf|psd|py|rar|rar|scene|url|xls|xlsx|zip\" GUID:|HASH:|MD5:|SID: >\r\n\r\n# =============================================================================\r\n# [11:48] -> 11.07.2024: grp-ex:\r\ngrp-paths-ex:\r\n    nohighlight:!< (!ext:\"ms|py|nss|bat|cmd|mcr|mse\" path:\"Cache\")|(!ext:\"py|json|config\" path:\"Atlassian/SourceTree\")|(!ext:\"py|json|config\" path:\"Sync.Config\")|path:\"Windows Defender\"|path:\"%WINDIR%\"|path:\".dropbox.cache\"|path:\"appcompat\"|path:\"Autodesk/ADPSDK\"|path:\"Autodesk/ODIS\"|path:\"C:/$WinREAgent\"|path:\"C:/Windows/Logs\"|path:\"Chrome/User Data/Safe Browsing\"|path:\"customDestinations-ms\"|path:\"GoogleUpdater\"|path:\"inetcache\"|path:\"leveldb\"|path:\"Local/Google/Chrome\"|path:\"Local/Microsoft/Windows/History\"|path:\"Logs/SystemIndex\"|path:\"loguploadersettings*.ini\" |path:\"Microsoft/Windows/WebCache\"|path:\"OneDrive/logs\"|path:\"Packages/Microsoft.Windows\"|path:\"programdata/microsoft/network\"|path:\"ProgramData/Oracle/Java\"|path:\"ProgramData/USOPrivate\"|path:\"Recent/AutomaticDestinations\"|path:\"sublimetext/exe/Data/Cache\"|path:\"sublimetext/exe/Data/Crash Reports\"|path:\"sublimetext/exe/Data/Index\"|path:\"sublimetext/exe/Data/Lib\"|path:\"sublimetext/exe/Data/Package Storage\"|path:\"sublimetext/exe/Lib\"|path:\"sync.cache\"|path:\"Sync.Logs\"|path:\"Windows/Prefetch\"|path:\"WindowsApps/MicrosoftTeams\" >\r\n\r\ngrp-exts-ex:\r\n    nohighlight:!< (EXT:)|(EXT:\"crx*\")|EXT:\"accdb|adp|ani|aodl|aux|ax|backup|bak|bak2|bak3|bck|bck2|bin|bin64|bk|bkf|bkp|body|bup|cab|cache|cat|cc|ccx|cdmp|cdp|cdpresource|cdxml|changecount|chk|class|com|csproj.user|ctf|cubin|dat|db|db-shm|db-wal|db3|db3|dcl|dep|diskcopy42|diskcopy50|diz|dll|dmp|docbook|dof|ds_store|dsp|dspuser|dvc|edb|edb|err|err2|etl|evtx|fatbin|ftg|fts|gid|hds|hds|hxd|hxd|hyb|ibd|ico|idl|imagemanifest|inc|inf_loc|inline|ipch|ivt|jar|jcp|jcp|jfm|jfm|jtx|jtx|lck|ldb|leveldb|lock|log|log1|log2|log3|map|mdb|mdmp|mmap|mo|mum|ncb|nwl|nwl|od|odlgz|old|otc|parc|part|partial|pb|pck|pdb|pem|pf|pid|pl|pm|pma|pri|pri|prm|prt|ptx|pyc|pyf|qm|qmlc|rcache|res|res2|resx|sav|scc|sdb|sdf|sds|sid|sin|so|spi1d|spj|sqlite|srd-shm|store|sublime-package|pack|sublime_session|sublime-workspace|suo|suo2|swo|swp|sym|sys|tbres|tcl|tek|temp|thumb|thumbsdb|tjg|tlog|tmp|tmpfile|trc|vscdb|wae|lnk|wbk|wer|wlog|woff|woff2|xbf|xin|xlf|zsh|zfe|~debris|~lock|~temp\" >\r\n\r\ngrp-parents-ex:\r\n    nohighlight:!< EXACT:ANCESTOR-NAME:\".git\"|EXACT:ANCESTOR-NAME:\"Crash Reports\"|EXACT:ANCESTOR-NAME:\".backups\"|EXACT:ANCESTOR-NAME:\"venv\" >\r\n\r\ngrp-names-ex:\r\n    nohighlight:!< file: CASE:EXACT:NAME:\"Exact Quick Find.sublime-settings\"|CASE:EXACT:NAME:\"globalStorage\"|CASE:EXACT:NAME:\"Local\"|CASE:EXACT:NAME:\"Network Persistent State\" >\r\n\r\ngrp-strings-ex:\r\n    nohighlight:!< \"automaticDestinations-ms\"|\"Indexed*DB\"|\"microsoft*.xml\"|\"mpcache\"|\"PcaGeneralDb\"|\"Url*.store.*\" >\r\n\r\ngrp-meta-ex:\r\n    nohighlight:!( !ext:\"gitignore|ms|py|nss|bat|cmd|mcr|mse\" (FILE: SIZE:<24bytes)|(DIR: CHILD-COUNT:0)|(empty: child-file-count-from-disk:0 child-folder-count-from-disk:0 !attrib:L) )\r\n\r\ngrp-regex-ex:\r\n    nohighlight:!( REGEX:\"^\\d{1,30}$\" | REGEX:EXTENSION:\"^\\d{0,}$\" )\r\n\r\ngrp-patterns-ex:\r\n    nohighlight:!< !ext:\"7z|bat|cmd|csv|doc|docx|exe|git|json|mcr|md|mp3|mp4|ms|nss|pdf|psd|py|rar|rar|scene|url|xls|xlsx|zip\" GUID:|HASH:|MD5:|SID: >\r\n# =============================================================================\r\n# [22:31] -> 27.05.2024:\r\nTEXT: REGEX:CONTENT:\"\\b((https?|ftp|file):\\/\\/[-A-Z0-9+&@#\\/%?=~_|!:,.;]*[-A-Z0-9+&@#\\/%=~_|])\" column1:=regmatch0: col-1-label:=\"match:url\" columns:dm;size;ext;name;column1;path;name-frequency;extension-frequency;run-count;  add-column:1:2\r\n\r\n\r\ncol-1:=regmatch0: col-1-label:=\"match:url\" columns:dm;size;ext;name;column1;path;name-frequency;extension-frequency;run-count;  add-column:1:2\r\n\r\naddcolumn:c:2;b:3;a:4; a-label:=\"match:2\" b-label:=\"match:1\" c-label:=\"match:0\"\r\na-label:=\"regmatch0\" b-label:=\"regmatch1\" c-label:=\"regmatch2\"\r\n\r\nc:2\r\nb:3\r\na:4\r\n\"cols1\"\r\naddcolumn:a:2;b:3; a-label:=\"match:1\" b-label:=\"match:2\" col-a:=regmatch0: col-b:=regmatch1:\r\n\r\naddcolumn:j:1;i:2;g:3;f:4;e:5;d:6;c:7;b:8;a:9;\r\n\r\n# 22.04.2024 - Kl.23:27:\r\n# 3dsmax:\r\n< nohighlight:path:\"%LOCALAPPDATA%\" nohighlight:path:\"3ds\" regex:parent-path:\"(202[1-9])\\b\" >\r\n\r\n!< path:AdskLicensingSDK|BackBurner|CachedThumbnails|OSLCache >\r\n!< childfoldercount:0 >\r\n!< size:<8bytes >\r\n!< ext:log|tmp|oso|dat|bak|xml >\r\n\r\n< sort:dm secondary-sort:!isfolder: >\r\n\r\n\r\n\r\n# 05.04.2024 - Kl.22:17:\r\ndm:last60seconds (columns:dm;column1;size;ext;name;path;name-frequency;extension-frequency;run-count;)\r\ncol-1-label:=\"Last Modified : [A]\"\r\ncol-1:=if(now()-$dm:>315576000000000,\"e.  : \"(now()-$dm:)/315576000000000..\" years\",\r\n          if(now()-$dm:>864000000000,\"d.  : \"(now()-$dm:)/864000000000..\" days [ D ]\",\r\n             if(now()-$dm:>36000000000,\"c.  : \"(now()-$dm:)/36000000000..\" hours [ + ]\",\r\n                if(now()-$dm:>600000000,\"b.  : \"(now()-$dm:)/600000000..\" minutes [ * ]\",\r\n                   \"a.  : \"(now()-$dm:)/10000000..\" seconds\"))))\r\nsort:dm\r\n\r\n\r\n\r\n\r\n# Sort by char-count\r\nSORT:CHARS\r\n\r\n\r\n\r\n> # colorize by depth             > sort last modified (mixed dirs/files) >\r\n> \"group-colors:depth|dir:|file:\" > \"sort:dm;path;folder-depth-ascending\" >\r\n\r\n#\r\n(\r\n    # Define a label for the column\r\n    a-label:=\"Last Modified\"\r\n    addcolumn:a\r\n\r\n    # Calculate time difference and assign a label based on duration\r\n    a:=if(now()-$dm:>315576000000000,\r\n        \"f.  : \" (now()-$dm:)/315576000000000 ..\" years\",\r\n        if(now()-$dm:>864000000000,\r\n            \"c.  : \" (now()-$dm:)/864000000000 ..\" days [ D ]\",\r\n            if(now()-$dm:>36000000000,\r\n                \"b.  : \" (now()-$dm:)/36000000000 ..\" hours [ + ]\",\r\n                \"a.  : \" (now()-$dm:)/600000000 ..\" minutes [ * ]\"\r\n            )\r\n        )\r\n    )\r\n\r\n    # Sort entries by last modified date and apply color grouping\r\n    group-colors:depth|dir|file\r\n)\r\ngroup-colors:depth|dir:|file: sort:dm secondary-sort:!isfolder:\r\n\r\ngroup-colors:depth|dir:|file: sort:dm\r\ngroup-colors:depth|dir:|file: sort:dm;path\r\ngroup-colors:depth|dir:|file: sort:parent-date-modified;dm;path\r\n\r\ngroup-colors:depth|dir:|file: sort:dm;path;folder-depth-ascending;path\r\n\r\n\r\n\r\n\r\n# 22.03.2024 - Kl.15:55: find duplicates\r\n# explore folder\r\n#\r\n# - all\r\n\"!dir\" : \"17688\"\r\n# - unique\r\n\"!dir: !dupe:size;name\" : \"14598\"\r\n# - dupes\r\n\"!dir: dupe:size;name\" : \"3090\"\r\n\r\n\r\n#\r\n\"!dir\" : \"15262\"\r\n\"!dupe:size !dir:\" : \"11945\"\r\n\"dupe:size !dir:\" : \"3317\"\r\n\r\n12377\r\n2885\r\n\r\n\r\n# 03.03.2024 - Kl.14:40:\r\n\"GITIGNORE size:<1MB !dupe:size sort:dm !img: \"\r\n\"GITIGNORE !dupe:size;sha256 \"\r\n\r\n\"size:<1mb !dupe:size;sha256 sort:dm gitignore\"\r\n# 03.03.2024 - Kl.12:30:\r\n\r\n# use when cleaning up space\r\n\"sort:parent-size;path;folder-depth-ascending;size\"\r\n#\r\n# \"sort:parent-date-modified;parent-size;path;folder-depth-ascending;size\"\r\n# sort:child-date-modified\r\n# sort:parent-size;path;folder-depth-ascending;size\r\n\r\n\r\n# 01.03.2024 - Kl.14:21: playing with sorts\r\n#\r\nsort:folder-name-ascending;size img: !path:\"world\" !ext:psd\r\n#\r\n< \"D:\\Dokumenter O.l\\YvonneDropbox\\Reaping For Dummies\" sort:folder-size;path;folder-depth-ascending;size >\r\n#\r\n\"sort:folder-size;path;folder-depth-ascending;size\"\r\n#\r\nd: path:midle  path:fromol parent-size:>1GB\r\nd: parent-size:>4GB parent-size:<30GB\r\nd: parent-size:>4GB parent-size:<30GB dp:\r\n#\r\ndm:2022.04.25-2024.04.30\r\nport 3d:|img: !ext:svg sort:folder-size;path;folder-depth-ascending;size dp: d:\r\n\r\n# 24.02.2024 - Kl.19:27:\r\n- burde kunne sortere basert på dir-with-immediate-children-size\r\n  - baserer seg på samme prinsipp som depth-searches\r\n- eksempel:\r\n  - \"%USERPROFILE%\\Downloads depth:4\"\r\n\r\n\r\n\r\n# 15.02.2024 - Kl.21:21:\r\n'''\r\n-------------------------\r\n[quote=void post_id=64942 time=1707907966 user_id=809]\r\n    Everything doesn't have the option to do a single pass search to exclude these folders.\r\n\r\n    I'll look into adding something as this is requested often enough.\r\n    ancestor-child: ? -not sure of the terminology..\r\n\r\n    For now, please consider [rurl=/forum/viewtopic.php?t=11375]omit results[/rurl]:\r\n    [list]\r\n    [*]From the Index menu, check Enable Omit Results.\r\n    [*]Search for:\r\n    regex:[b][/b]child:^\\.gitignore\r\n    [*]Select all these folders.\r\n    [*]From the File menu, click Omit.\r\n    [/list]\r\n[/quote]\r\n'''\r\n'''\r\n-------------------------\r\n[quote=jorn post_id=64939 time=1707872539 user_id=39916]\r\n    I did a terrible job at phrasing my question correctly  :oops:\r\n\r\n    I'm basically looking for a way to exclude all directories (including\r\n    sufolders/children) containing a specific file (e.g. \".gitignore\"), from a\r\n    single search-query (within the application).\r\n\r\n\r\n    E.g. given the filestructure below, it should exclude everything within 'other_dirname':\r\n    [code]└── some_dirname\r\n        ├── poetry.lock\r\n        ├── pyproject.toml\r\n        │\r\n        ├── other_dirname\r\n        │   ├── .exclude\r\n        │   ├── labels.yml\r\n        │   ├── release-drafter.yml\r\n        │   └── workflows\r\n        │       └── tests.yml\r\n        │\r\n        ├── src\r\n        │   └── hypermodern_python\r\n        │       ├── __init__.py\r\n        │       ├── __main__.py\r\n        │       └── py.typed\r\n        │\r\n        └── tests\r\n            ├── __init__.py\r\n            └── test_main.py[/code]\r\n'''\r\n\r\n// query #1: list recent files (tracked by Windows)\r\next:\"lnk\" distinct:name date-accessed:currentday sort:date-accessed columns:date-accessed;ext;name;path;type sort:date-accessed\r\npath:\"%appdata%\\Microsoft\\Windows\\Recent\" distinct:name date-accessed:currentday columns:da;ext;name;shortcut-target;type sort:da exists($shortcut-target:)\r\n\r\nregex:\"^[a-zA-Z0-9]+\\.(.*lnk)$\"\r\n\r\n\r\n\r\n\r\nregex:ads-ansi:\"sha512=(\\w+)\" !eval:#<:#stricmp:<#regex-match-1:,#sha512sum-sha512:>==0#>:\r\n\r\n\r\n# 13.02.2024 - Kl.14:48: wip\r\n\r\n# -----------------\r\n# WORKS4: get the path of all folders to exclude (by containing file)\r\nregex:\"^.exclude.*\" column1:=(path:) columns:column1;name;path;size sort:column1-descending\r\n\r\n# WORKS3: quick regmatch\r\nlen(REGEX_EXTRACT($name:,\"^.exclude.*\"))\r\n\r\n# WORKS2: extracts length of match value (.exclude.discarded -> 18)\r\ncolumn1:=LEN(REGEX_EXTRACT($name:,\"^.exclude.*\")) columns:column1;name;size sort:column1-descending\r\n\r\n# WORKS1: extracts match value (.exclude -> \".exclude.discarded\")\r\ncolumn1:=REGEX_EXTRACT($name:,\"^.exclude.*\") columns:column1;name;size sort:column1-descending\r\n\r\n# -----------------\r\n\r\n\r\n\r\n\r\n# 13.02.2024 - Kl.14:42:\r\n# UNSORTED WORKING EXAMPLES\r\n# - quick regex-match\r\n# -\r\nregex:ads-ansi:\"sha512=(\\w+)\" #regex-match-1:!=#sha512sum-sha512:\r\n\r\n\r\n# 13.02.2024 - Kl.14:06: Preprocessor examples\r\n# Using the preprocessor to find a filename with the current date as ISO8601:\r\n[year:now:]-[text:month:now:,00]-[text:day:now:,00]=>2023-01-28\r\n#\r\ndm:[year:now:] => dm:2023\r\n# One way to view the output is to use the preprocessor as a search command by prefixing a /\r\n/[year:now:]\r\n\r\n# https://www.voidtools.com/forum/viewtopic.php?f=12&t=10099\r\nclipboard:\r\n[len:abc]=> 3\r\n[len:\"[abc]\"]=> 5\r\n[eval:1<<17]=> 131072\r\n[rand:%10][rand:%10][rand:%10]=> (random 3 digits)\r\n[pathpart:\"c:\\windows\\system32\"]=> \"c:\\windows\"\r\n[day:now:]=> (the current day: 1-31)\r\n[formatfiletime:[edate:now:,-12],\"YYYYMMDD\"]=> (same date as now with the previous year)\r\n[define:list=\"Red,Green,Blue,Yellow\"][element:list:,\",\",randbetween:1,elementcount:list:,\",\"] (pick a random color name)\r\n#define<:f<x>=#<:#if<:\"#x:>1\",#eval<:#x:*#f<:#eval<:#x:-1#>:#>:#>:,1#>:#>:#>:#f:<5> (factorial 5)\r\n\r\n\r\n\r\n# 03.02.2024 - Kl.14:53: syncs/overridden constantly when logged in and open,\r\n# if youre gonna modify, don't work directly in the file while chrome is open.\r\n- \"Default\"\r\n  - user: '<EMAIL>'\r\n  - path: 'C:/Users/<USER>/AppData/Local/Google/Chrome/User Data/Default/Preferences'\r\n\r\n- \"Profile 1\"\r\n  - path: 'C:/Users/<USER>/AppData/Local/Google/Chrome/User Data/Profile 1/Preferences'\r\n\r\n- \"Profile 3\"\r\n  - user: '<EMAIL>'\r\n  - path: 'C:/Users/<USER>/AppData/Local/Google/Chrome/User Data/Profile 3/Preferences'\r\n\r\n- \"Profile 4\"\r\n  - user: '<EMAIL>'\r\n  - path: 'C:/Users/<USER>/AppData/Local/Google/Chrome/User Data/Profile 4/Preferences'\r\n\r\n- \"Profile 5\"\r\n  - user: '<EMAIL>'\r\n  - path: 'C:/Users/<USER>/AppData/Local/Google/Chrome/User Data/Profile 5/Preferences'\r\n\r\n\r\n- \"Profile 6\"\r\n  - user: '<EMAIL>'\r\n  - path: 'C:/Users/<USER>/AppData/Local/Google/Chrome/User Data/Profile 6/Preferences'\r\n# 03.02.2024 - Kl.16:05: everything -> chrome-profile-related:\r\nchrome_profile:\r\n    # PATH: \"%LOCALAPPDATA%/Google/Chrome/User Data\"\r\n    # REGEX:ANCESTOR-NAME:\"\\s[0-9]{1,2}(?!\\d)$\"\r\n    # !< CHILDFOLDERCOUNT:0 >\r\n    # !< SIZE:<24BYTES >\r\n    # !< REGEX:ANCESTOR-NAME:\".*cache.*\" >\r\n    # !< REGEX:PATH:\"\\{?[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}?\" >\r\n    # !< REGEX:PATH:\"[0-9A-Fa-f]{32}\" >\r\n\r\n\r\n\r\n\r\n# 29.01.2024 - Kl.14:11: Cleaning up and clearing space\r\n< !filter: !3d: !sc: sort:size > depth:3\r\n< PATH:\"%TEMP%\" PATH:\"After Effects\" PATH:\"Disk Cache\" >\r\n< path:\"C:\\Users\\<USER>\\AppData\\Local\\Temp\" dm:2000.01.01-2023.12.30 > depth:6\r\n\r\n\r\n# 19.01.2024 - Kl.18:00: quick way of including relevant stuff\r\n# -> ii: (include)\r\nii:\r\n    < EXT:\"3dm|3ds|3g2|3gp|3gp2|3gpp|aac|abc|ac3|ai|aif|aifc|aiff|amr|amv|ape|asf|asx|au|avi|bat|bdmv|bik|blend|c4d|cda|cfg|cmd|cob|cpp|csv|d2v|dae|divx|doc|docm|docx|drc|dsa|dsm|dss|dsv|dts|dwf|dwg|dxf|eps|epub|evo|exe|f4v|fbx|fla|flac|flc|fli|flic|flv|gdoc|git|gitignore|glb|gltf|gsheet|gslides|hdmov|hrc|htm|html|iam|ifo|indd|ini|iob|ipj|ipt|it|iv|ivf|js|json|keynote|log|lwo|m1a|m1v|m2a|m2p|m2t|m2ts|m2v|m3u|m4a|m4b|m4p|m4v|ma|max|mb|mcr|md|mid|midi|mka|mkv|mod|mov|mp2|mp2v|mp3|mp4|mp4v|mpa|mpc|mpe|mpeg|mpg|mpls|mpp|mpv2|mpv4|ms|mse|msg|msi|msp|msu|mts|mxp|mzp|nss|nwc|nwd|nwf|obj|ogg|ogm|ogv|opus|osg|pages|pc3|pdf|ply|pps|ppsx|ppt|pptx|ps1|psd|pss|pva|py|pyi|pyw|qt|ra|ram|rar|ratdvd|rm|rmi|rmm|rmvb|roq|rpm|rtf|scene|scn|scr|skp|smil|smk|snd|spc|sql|step|stl|stp|sublime|swf|tp|tpr|ts|txt|u3d|url|usd|usdc|usdz|vb|vbs|vdx|vob|voc|vp6|vrml|vsd|vsdm|vsdx|vss|vssm|vssx|vst|vstm|vstx|wav|weba|webm|wm|wma|wmp|wmv|wpd|wps|wrl|x3d|xlam|xls|xlsb|xlsm|xlsx|xltm|xltx|xm|xml|xsi|yml|zip|zpr\" OR REGEX:EXTENSION:\"^SUBLIME.*\" >\r\n\r\n\r\n# 19.01.2024 - Kl.17:00: quick way of excluding some extra\r\n# ! ADDITIVE: this is a standalone macro that is added when you want to exclude most of the \"noise\"\r\n# - Note: specifies what you DONT want to see\r\n# -> ee: (excludeextra)\r\nee:\r\n    !< size:<24bytes >\r\n    !< ext:\"vscdb|jcp|jtx|nwl|backup|edb|jfm|hxd|hds|db3|pri\" >\r\n    !< case:exact:name:\"Local\"|\"globalStorage\"|\"Auto Save Session.sublime_session\"|\"Exact Quick Find.sublime-settings\" >\r\n    !< path:\"%LOCALAPPDATA%\"|\"%TEMP%\" ext:\"tmp\"|\"aodl\"|\"OD\" >\r\n    !< path:\"%TEMP%\" regex:\"^[0-9a-z]{8}\\.[a-z0-9]{3}$\" >\r\n    !< path:\"*/.vscode/extensions/*/dist\"|\"%TEMP%/CreativeCloud\"|\"%TEMP%/*.tek/\"|\"CCX Welcome\" >\r\n    !< path:\"/Local/pip\"|\"/stdlib/\"|\"Google/Chrome/User Data\"|\"Code/User/Sync\"|\"Code/logs\"|\"Sublime Text/Index\"|\"FancyZones/Logs\" >\r\n    !< path:\"/Python*/Lib\"|\"/usr/lib\"|\"/local/Lib\"|\"/site-packages\"|\"/python/data\"|\"Autodesk/ODIS/\" >\r\n    !< path:\"microsoft.\"|\".microsoft\"|\"Microsoft/Windows\"|\"Microsoft/Edge\" >\r\n\r\n\r\n\r\n# 19.01.2024 - Kl.15:46:\r\n# burde lage egen macro som eksluderer uvanlige filtyper (e.g. '!ext:vscdb|jcp|jtx|nwl|etc')\r\n!ext:\"vscdb|jcp|jtx|nwl|backup\"\r\n#\r\n# 19.01.2024 - Kl.16:18: combining groups\r\n# example: !< dir:name:\"microsoft.\"|\".microsoft\" > !< file:name:\"microsoft.\"|\".microsoft\" >\r\n\"\"\"\r\nTEMP:\r\n    !< PATH:\"%TEMP%\" regex:\"^[0-9a-z]{8}\\.[a-z0-9]{3}$\" >\r\n    !< case:exact:name:\"Local\"|\"globalStorage\" >\r\n    !< path:\"*/.vscode/extensions/*/dist\"|\"%TEMP%/CreativeCloud\"|\"%TEMP%/*.tek/\" >\r\n    !< path:\"/Python*/Lib\"|\"/usr/lib\"|\"/local/Lib\"|\"/site-packages\"|\"/python/data\" >\r\n\"\"\"\r\n\r\n\r\n\r\n# 17.01.2024 - Kl.20:46:\r\n# regex notes\r\n\"\"\"\r\n(?:0[1-9]|[12][0-9]|3[01]) matches the day (01 to 31).\r\n(?:0[1-9]|1[0-2]) matches the month (01 to 12).\r\n\\d{4} matches a four-digit year.\r\n\"\"\"\r\n\r\n# 11.01.2024 - Kl.15:59:\r\n# -> Exclude files without extension\r\n!EXT:\r\n# 11.01.2024 - Kl.13:52:\r\n\r\n# REGEX PATTERNS:\r\n#\r\n# -> CLSID\r\n\"\"\" {FFE5B55B-7ED0-4E24-85C3-AB9BCD6881EE} \"\"\"\r\nREGEX:\"{[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}}\"\r\n#\r\n# -> GUID\r\n# \"\"\" e6b812f5-5fd9-4db5-bc88-3b3039a8db69 \"\"\"\r\nREGEX:\"{?[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}}?\"\r\n#\r\n# -> IPv4 Pattern\r\n# \"\"\" ************ \"\"\"\r\nREGEX:\"(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\"\r\nREGEX:\"\\b(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\\b\"\r\nREGEX:\"^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$\"\r\n#\r\n# -> EMAIL\r\n# \"\"\" <EMAIL> \"\"\"\r\nREGEX:\"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}\"\r\n#\r\n# -> DATE\r\n# \"\"\" DD.MM.YYYY (e.g. 24.12.2023) \"\"\"\r\nREGEX:\"(?:0[1-9]|[12][0-9]|3[01])\\.(?:0[1-9]|1[0-2])\\.\\d{4}\"\r\n# \"\"\" MM.DD.YYYY (e.g. 12.24.2023) \"\"\"\r\nREGEX:\"(?:0[1-9]|1[0-2])\\.(?:0[1-9]|[12][0-9]|3[01])\\.\\d{4}\"\r\n# \"\"\" YYYY.MM.DD (e.g. 12.24.2023) \"\"\"\r\nREGEX:\"\\d{4}\\.(?:0[1-9]|1[0-2])\\.(?:0[1-9]|[12][0-9]|3[01])\"\r\n#\r\n# -> HEX\r\n# \"\"\" 4605f46abeb61a17fe093713e4e99543 \"\"\"\r\nREGEX:\"[0-9A-Fa-f]{32}\"\r\nREGEX:\"\\b[0-9A-Fa-f]{32}\\b\"\r\n#\r\n# -> RAR Parts\r\n# \"\"\" office2021-en-x64.r36 \"\"\"\r\nREGEX:\"^.*\\.r(?!00)\\d{2}$\"\r\n#\r\n# -> Foldername ends with digits (range: 0:99), e.g: 'Profile 2}'\r\n\"\"\" ../Google/Chrome/User Data/Profile 2/WebStorage/.. \"\"\"\r\nREGEX:ANCESTOR-NAME:\"\\s[0-9]{1,2}(?!\\d)$\" # IMPROVED: 79305\r\n# REGEX:PATH:\"\\s[0-9]{1,2}(?!\\d)\"       # EASY: 158635 | 7519\r\n# REGEX:PATH:\"\\s(?:[1-9]?[0-9])(?!\\d)\"  # HARD: 150115 | 7144\r\n\r\n\r\n\r\n# -> Files with numberic extensions\r\n# \"\"\" Diagtrack-Listener.etl.001 \"\"\"\r\nREGEX:EXTENSION:\"^\\d+$\"\r\n\r\n# -> Files with numberic extensions (3 digits or more)\r\n# \"\"\" aria_log.00000001 \"\"\"\r\nREGEX:EXTENSION:\"^\\d{3,}$\"\r\n\r\nREGEX:EXTENSION:\"\\s[0-9]{1,2}\\\\$\"\r\n\r\n# -> Path contains \"Ferdig\" and Extension is \".ms\"\r\nregex:path:^(?=.*Ferdig).*\\.ms$\r\nregex:path:^[0-9a-z]{8}\\.[a-z0-9]{3}$\r\n\r\n# -> SEARCH\r\nfile:dm:thisweek\r\nfolder:dm:thisweek\r\nDate Range: dm:2017.01.01..2017.04.30 *.jpg\r\n\r\n\r\n# COLUMNS:\r\n# Time conversion\r\naddcolumn:a;b;c a:=FORMATFILETIMEUTC($date-created:) b:=FORMATFILETIMEUTC($date-modified:) c:=formatduration($date-created:-$date-modified:,\"[d]:hh:mm:ss.SSSSSSS\") a-label:=\"Date Created UTC\" b-label:=\"Date Modified UTC\" c-label:=\"Time difference in 100-nano-seconds\"\r\n# Show four first parts of a path\r\naddcolumn:a a:=regexextract($path:,\"(?:(?:^|\\\\)[^\\\\]*){4}\")\r\n# Show time since last modified in weeks\r\nadd-column:a a-label:=\"Weeks\" a:=(now()-$dm:)/2629746000000 file:\r\n# Specify the index to add the new column to\r\nadd-column:a:0 a:=$size:\r\n# | Extract Number of Digits in Filename\r\ncolumn1:=LEN(REGEX_EXTRACT($name:,\"[0-9]+\")) columns:column1;name;size sort:column1-descending\r\n# | Extract Number of Underscores in Filename\r\ncolumn1:=LEN(REGEX_REPLACE($name:,\"[^_]\")) columns:column1;name;size sort:column1-descending\r\n\r\n\r\n# 27.11.2023 - Kl.17:56:\r\n#\r\n# -> TODAY (Modified/Created)\r\n(\r\n    date-modified:currentday OR\r\n    date-created:currentday\r\n)\r\n# -> THIS HOUR (Modified/Created)\r\n(\r\n    date-modified:currenthour OR\r\n    date-modified:lasthour OR\r\n    date-created:currenthour\r\n)\r\n(\r\n)\r\n# -> THIS MONTH (Modified/Created)\r\n(\r\n    date-modified:currentmonth OR\r\n    date-created:currentmonth\r\n)\r\n# -> THIS WEEK (Modified/Created)\r\n(\r\n    date-modified:currentweek OR\r\n    date-created:currentweek\r\n)\r\n# -> THIS YEAR (Modified/Created)\r\n(\r\n    date-modified:currentyear OR\r\n    date-created:currentyear\r\n)\r\n\r\n\r\n\r\n# 15.01.2024 - Kl.17:57:\r\n\r\nExamples:\r\n\r\n$date-modified:==$date-created: (date-modified is equal to date-created)\r\nUPPER($name:)>=\"N\" UPPER($name:)<=\"S\" (Name starts with N to S)\r\n$name:[1]=='o' (Second letter of name is o)\r\nlen($stem:)%3==0 (Stem length is a multiple of 3)\r\n*.lnk !exists($shortcut-target:) (find lnk files where the shortcut target doesn't exist)\r\n\r\n# 12.01.2024 - Kl.18:59:\r\n\r\n# 21.11.2023 - Kl.19:37:\r\n# count:max-items-to-display må kun brukes når man har et sterkt filter!\r\nfr: ext:txt count:41\r\n\r\n# 21.11.2023 - Kl.00:48:\r\n# -> SUBLIME\r\n(\r\n    (\r\n        NOHIGHLIGHT:PATH:\"%APPDATA%\" OR\r\n        NOHIGHLIGHT:PATH:\"%USERPROFILE%\\Desktop\" OR\r\n        NOHIGHLIGHT:PATH:\"PRJ\\\" OR\r\n        NOHIGHLIGHT:PATH:\"Project\\\" OR\r\n        NOHIGHLIGHT:PATH:\"Person*\" OR\r\n        NOHIGHLIGHT:PATH:\"Sublime Text\" OR\r\n        NOHIGHLIGHT:EXT:SUBLIME* OR\r\n        REGEX:EXTENSION:SUBLIME.* OR\r\n        REGEX:PATH:SUBLIME.* OR\r\n        REGEX:NAME:SUBLIME.*\r\n    ) AND\r\n    (\r\n        !PATH:\"%WINDIR%\" AND\r\n        !PATH:\"%PROGRAMFILES%\" AND\r\n        !PATH:\"%PROGRAMFILES(X86)%\"\r\n    )\r\n(\r\n    (\r\n        !PATH:\".GIT\" AND\r\n        !PATH:\"SourceTree.exe_Url_*\" AND\r\n        !PATH:\"*\\Microsoft.*\\TempState\\BgTask_*\" AND\r\n        !PATH:\"*\\Chrome\\**\\Default\\Network\" AND\r\n        !PATH:\"*\\Chrome\\**\\Code Cache\" AND\r\n        !PATH:\"*Chrome\\**IndexedDB\" AND\r\n        !PATH:\"*Chrome\\**File System\" AND\r\n        !PATH:\"Microsoft\\**Edge\" AND\r\n        !PATH:\".dropbox.cache\" AND\r\n        !PATH:\"Google\\DriveFS\" AND\r\n        !PATH:\"Packages\\Microsoft.WindowsFeedbackHub_*\" AND\r\n        !PATH:\"qBittorrent\\BT_backup\" AND\r\n        !PATH:\"qBittorrent\\qBittorrent-data.ini\" AND\r\n        !PATH:\"AppData\\Local\\Dropbox\" AND\r\n        !PATH:\"ProgramData\\Microsoft\\Network\" AND\r\n        !PATH:\"INetCache\" AND\r\n        !PATH:\"__pycache__\" AND\r\n        !PATH:\"Dropbox\\Update\"\r\n    (\r\n        !NAME:\"FETCH_HEAD\" AND\r\n        !NAME:\"HxStore.hxd\" AND\r\n        !NAME:\"__init__\" AND\r\n        !NAME:\"AdobeARM.log\" AND\r\n        !NAME:\"DropboxUpdate.log*\"\r\n    (\r\n        !EXT:\"\" AND\r\n        !EXT:\"PYC\" AND\r\n        !EXT:\"LNK\" AND\r\n        !EXT:\"BIN\" AND\r\n        !EXT:\"STORE\" AND\r\n        !EXT:\"DB\" AND\r\n        !EXT:\"ETL\" AND\r\n        !EXT:\"TOK\" AND\r\n        !EXT:\"CHK\" AND\r\n        !EXT:\"TBRES\" AND\r\n        !EXT:\"SQL*\" AND\r\n        !EXT:\"fastresume\" AND\r\n        !EXT:\".automaticDestinations*\"\r\n    )\r\n(\r\n    (\r\n        !SIZE:<24BYTES AND\r\n        !REGEX:^\\d+\\.log$ AND\r\n        !REGEX:^[a-z]{25,}|[A-Z]{25,}|[a-zA-Z]{25,}$ AND\r\n        !REGEX:^[a-z]{32}_[0-9]+\\.[a-f0-9]{64}$ AND\r\n        !REGEX:^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$ AND\r\n        !REGEX:^[A-F0-9]{8}_[A-F0-9]{8}$ AND\r\n        !REGEX:^S-1-5-21-\\d+-\\d+-\\d+-\\d+$\r\n    )\r\n)\r\n\r\n\r\n\r\n\r\n# 20.11.2023 - Kl.23:53:\r\nregex:\\.(exe)$ | nohighlight:regex:.* sort:regmatch1-descending;name-ascending\r\n\r\n\r\n#\r\n# 20.11.2023 - Kl.23:49: adding column count\r\ncolumn1:=(stem-length:) add-column:column1 column2:=len(sibling-file-count:) add-column:column2\r\n\r\n# 20.11.2023 - Kl.22:00: (TD) files or folders modified (today)\r\n(\r\n    PARENT-DATE-MODIFIED:TODAY OR\r\n    DATE-MODIFIED:TODAY\r\n)\r\n# 20.11.2023 - Kl.17:40: (R) filter out all un(relevant) files\r\n(\r\n    (\r\n    (\r\n    (\r\n    (\r\n    )\r\n(\r\n    (\r\n    )\r\n)\r\n\r\n\r\n\r\n\r\n# 20.11.2023 - Kl.15:05: group search using parenthesis\r\n# allow_round_bracket_parenthesis2=1\r\n# expand_environment_variables2=1\r\n(path:\"sublime text\") !path:\"Sublime Text\\Lib\" !path:\"\" !path:\"__pycache__\" !path:\"%programfiles%\"  distinct:path sort:dm\r\n\r\n\r\n\r\n# 20.11.2023 - Kl.15:34: GLOBAL SEARCHES\r\n# -> WORK IN PROGRESS (W:) (exlusion-based -filter)\r\n(\r\n    (\r\n        !EXT:\"BIN\"\r\n    )\r\n    (\r\n        !PATH:\"%WINDIR%\"\r\n        !PATH:\"%PROGRAMFILES%\"\r\n        !PATH:\".GIT\"\r\n    )\r\n    (\r\n        PATH:\"%APPDATA%\" OR\r\n        PATH:\"%USERPROFILE%\\Desktop\" OR\r\n        PATH:\"PRJ\\\"\r\n        PATH:\"Project\\\"\r\n    )\r\n    (\r\n        SIZE:>24BYTES\r\n    )\r\n) SORT:dm;path;ext;name;size;\r\n\r\n# 20.11.2023 - Kl.15:59: kombinerer exlusion og inclusion, fordi det jeg inkluderer etterpå representerer et\r\n# ekstra nivå. det som matcher inclusion vil highlightes i view'en.\r\n# - exclusion filters (ext, path, etc)\r\n(\r\n    (\r\n    (\r\n        !PATH:\"%PROGRAMFILES(X86)%\" AND\r\n    )\r\n)\r\nOR\r\n(\r\n    (\r\n        PATH:\"PRJ\\\" OR\r\n    )\r\n    (\r\n    )\r\n AND (!regex:^[a-z]{25,}|[A-Z]{25,}|[a-zA-Z]{25,}$)\r\n\r\n\r\n# 20.11.2023 - Kl.16:14:\r\n(\r\n    (\r\n        SIZE:>24BYTES AND\r\n        (!regex:^[a-z]{25,}|[A-Z]{25,}|[a-zA-Z]{25,}$)\r\n    ) AND (\r\n    )\r\n) OR\r\n(\r\n    (\r\n        PATH:\"Person*\"\r\n    )\r\n    SORT:DM;PATH;EXT;NAME;SIZE;\r\n)\r\n\r\n\r\nREGEX:^(?!(?:[a-z]{25,}|[A-Z]{25,}|[a-zA-Z]{25,})).*$\r\n\r\n\r\n\r\n\r\nC:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Sync Extension Settings\\ohcpnigalekghcmgcdcenkpelffpdolg\\000003.log\r\n\r\n((!EXT:\"\" AND !EXT:\"PYC\" AND !EXT:\"LNK\" AND !EXT:\"BIN\") AND (!PATH:\"%WINDIR%\" AND !PATH:\"%PROGRAMFILES%\" AND !PATH:\"%PROGRAMFILES(X86)%\" AND !PATH:\".GIT\") ) OR ((PATH:\"%APPDATA%\" OR PATH:\"%USERPROFILE%\\Desktop\" OR PATH:\"PRJ\\\" OR PATH:\"Project\\\") (SIZE:>24BYTES ) )\r\n\r\n\r\n\r\n\r\n\r\n\r\n EXT:ai;doc;docx;dwg;dxf;msg;eps;epub;gdoc;gsheet;gslides;indd;keynote;md;mpp;pages;pdf;ppt;pptx;psd;rtf;wpd;wps;xls;docm;xlam;xlsb;xlsm;xlsx;xltm;xltx;pps;ppsx;vdx;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;pc3;\r\n\r\n# -> DISTINCT DIRS (DI:)\r\nDIR: DISTINCT:PATH DISTINCT:NAME\r\n\r\n((!EXT:\"\" !EXT:\"PYC\" !EXT:\"LNK\" !EXT:\"BIN\") (!PATH:\"%WINDIR%\" !PATH:\"%PROGRAMFILES%\" !PATH:\"%PROGRAMFILES(X86)%\" ) (!PATH:\".GIT\" !PATH:\"%XXXXXXXXXXXX%\" ) ) SORT:DM\r\n\r\n\r\n((!EXT:\"\" !EXT:\"PYC\" !EXT:\"LNK\" !EXT:\"BIN\") AND (!PATH:\"%WINDIR%\" !PATH:\"%PROGRAMFILES%\" !PATH:\"%PROGRAMFILES(X86)%\" ) AND (!PATH:\".GIT\" !PATH:\"%XXXXXXXXXXXX%\" ) ) SORT:DM\r\n\r\n\r\n\r\n\r\n# SUBLIME TEXT (ST:)\r\n(\r\n    (!EXT:\"\" !EXT:\"PYC\" !EXT:\"LNK\")\r\n    AND (!PATH:\"%WINDIR%\" !PATH:\"%PROGRAMFILES%\" !PATH:\"%PROGRAMFILES(X86)%\" )\r\n    AND (!PATH:\".GIT\" !PATH:\"%XXXXXXXXXXXX%\" )\r\n)\r\n    OR (path:\"%APPDATA%\" OR \"%USERPROFILE%\\Desktop\")\r\n    AND (PATH:\"PRJ\\GIT\\JHP\" OR \"%USERPROFILE%\\Desktop\")\r\n    AND (PATH:\"sublime text\" OR \"xxxxx\")\r\n!path:\".git\" !ext:pyc !ext:lnk\r\n\r\ncommand:\r\n    AND (!path:\".git\" !ext:pyc !ext:lnk)\r\n    AND (path:\"sublime text\") !path:\"Sublime Text\\Lib\" !path:\"\" !path:\"__pycache__\" !path:\"%programfiles%\"  distinct:path sort:dm\r\n\r\n\r\n\r\n# 10.11.2023 - Kl.12:34:\r\nSort by folder, then by date modified:\r\nsort:parent-date-modified secondary-sort:dm\r\n# example:\r\n# path:\"src\\all_combined_2023.11.11\\all_from_myself_2023.11.11\" ext:url|dir: file: sort:parent-date-modified secondary-sort:dm\r\nSort by stem-length (Sort-Column: Filename/basename Characters):\r\ncolumn1:=(stem-length:) add-column:column1\r\n\r\n\r\n\r\n# 14.10.2023 - Kl.10:38:\r\n# - How to display only latest/most recent file in each subfolder?\r\n# - How to sort all files by parent-folder-date-modified then file-modified?\r\nfile: column1:=(parent-date-modified:) add-column:column1 sort:column1-descending;dm\r\nfile: column1:=(parent-date-modified:) add-column:column1 sort:column1-descending secondary-sort:dm\r\n\r\nfile: column1:=(parent-date-modified:) add-column:column1 sort:column1-descending distinct:column1 distinct:path\r\n<ext:subl* | ext:tmTheme | ext:tmLanguage | ext:tmPreferences | ext:tmSnippet | name:subl*.bat> <!ext:sublime-dependency !name:*(Linux)* !name:*(OSX)*>\r\ncolumn1:=(parent-date-modified:) add-column:column1 sort:column1-descending;dm;path;ext;name;size;\r\n\r\n// Returns count to a new sorting-column\r\ncolumn1:=len(sibling-file-count:*.txt) add-column:column1\r\nparent-date-modified:\r\ncolumn1:=(child-date-accessed:) add-column:column1\r\ncolumn1:=(parent-date-modified:) add-column:column1 sort:column1;dm\r\nfile: column1:=(parent-date-modified:) add-column:column1 sort:column1-ascending;dm\r\nfile: dm:year: depth:8 ext:py sort:parent-date-modified: column1:=(parent-date-modified:) add-column:column1\r\nfile: depth:8 ext:py sort:parent-date-modified: column1:=(parent-date-modified:) add-column:column1\r\nparent-date-modified:today\r\nchild-folder-date-created:\r\ncolumn:size;date-modified\r\n// Returns match to a new sorting-column\r\n# accidentally deleted this line\r\nregex:^(?!(?:[a-z]{25,}|[A-Z]{25,}|[a-zA-Z]{25,})).*$\r\nregex:^[a-z]{25,}|[A-Z]{25,}|[a-zA-Z]{25,}$\r\n\r\n\r\n\r\nsecondary-sort:dm secondary-sort-descending:ext\r\nsecondary-sort:size secondary-sort:\"date modified\"\r\nsecondary-sort:size-ascending secondary-sort:path-descending\r\nsecondary-sort:parent-date-modified secondary-sort:dm\r\nsecondary-sort:size secondary-sort-ascending:\r\n\r\n\r\n\r\n\r\n# 11.10.2023 - Kl.13:14: Desktop (root-level, no subdirectories)\r\n# - CURRENT USER\r\nPATH:\"%USERPROFILE%\\Desktop\" DEPTH:4\r\n# - ALL USERS\r\nPATH:\"C:\\Users\\<USER>\\Desktop\" DEPTH:4\r\n\r\n# 05.11.2023 - Kl.18:05:\r\n- 20221203_151922.mp4: Pappa\r\n- 20221203_152020.mp4: Pappa\r\n- 20221203_194452.mp4: Pappa\r\n- 20221209_163052.mp4: Mamma\r\n- 20221209_195030.mp4: Mamma\r\n- 20221210_114042.mp4: Mamma\r\n- 20221211_155142.mp4: Mamma\r\n- 20221211_165721.mp4: Mamma\r\n- 20230223_151924.mp4: Alva\r\n- 20230404_112415.mp4: Mamma\r\n- 20230404_121545.mp4: Ingvild\r\n- 20230404_135437.mp4: Ingvild\r\n- 20230406_174834.mp4: Anne Lise\r\n- 20230406_175233.mp4: Anne Lise\r\n- 20230406_175909.mp4: Ronny\r\n- 20230407_142009.mp4: Mor\r\n- 20230409_190106.mp4: Mor\r\n- 20230413_190944.mp4: Håvard\r\n- 20230414_121602.mp4: Ingrid og Pappa\r\n- 20230414_151954.mp4: Ingrid og Pappa\r\n- 20230512_095056.mp4: Alva\r\n- 20230707_161831.mp4: Mamma\r\n- 20230707_180312.mp4: Mamma\r\n- 20230717_164911.mp4: Ingrid\r\n- 20230721_230404.mp4: Pappa\r\n- 20231012_134456.mp4: Mamma\r\n- 20231019_154046.mp4: Mamma\r\n\r\nEXT:MP4 NAME: 20221203_151922|20221203_152020|20221203_194452|20221209_163052|20221209_195030|20221210_114042|20221211_155142|20221211_165721|20230223_151924|20230404_112415|20230404_121545|20230404_135437|20230406_174834|20230406_175233|20230406_175909|20230407_142009|20230409_190106|20230413_190944|20230414_121602|20230414_151954|20230512_095056|20230707_161831|20230707_180312|20230717_164911|20230721_230404|20231012_134456|20231019_154046\r\nVIDEOS:\r\n VID: DEPTH:9 DISTINCT:NAME !PATH:SNAPSHOT !PATH:DOWNLOAD !PATH:SNAPSHOT !PATH:DOWNLOAD PATH:MobilBackup\\2023.*|PATH:MobilBackup\\2022.* PATH:\"D:\\Dokumenter O.l\\Documens\\Privat\\Dokumenter\\Personlig\\MobilBackup\"\r\nIMAGES:\r\n IMG: DEPTH:9 DISTINCT:NAME !PATH:SNAPSHOT !PATH:DOWNLOAD !PATH:SNAPSHOT !PATH:DOWNLOAD PATH:MobilBackup\\2023.*|PATH:MobilBackup\\2022.* PATH:\"D:\\Dokumenter O.l\\Documens\\Privat\\Dokumenter\\Personlig\\MobilBackup\"\r\n IMG: DEPTH:9 DISTINCT:NAME !PATH:SNAPSHOT !PATH:DOWNLOAD !PATH:SNAPSHOT !PATH:DOWNLOAD PATH:_JORN PATH:\"D:\\Dokumenter O.l\\Documens\\Privat\\Dokumenter\\Personlig\\MobilBackup\"\r\n IMG: PATH:\"D:\\Dokumenter O.l\\Documens\\Privat\\Dokumenter\\Personlig\\MobilBackup\"\r\n# 03.11.2023 - Kl.22:11: Mobilbackup Bilder: Alva\r\nIMG: PATH:_JORN PATH:MobilBackup\\2023.*|PATH:MobilBackup\\2022.* DC: DISTINCT:NAME !NAME:SCREENSHOT !NAME:SNAPCHAT !NAME:SCREENRECORDER SORT:DM\r\n# DM:2022.21.10..2024.01.01 PATH:\"MobilBackup\\*_JORN\" path:\"_JORN\" DC: IMG: DISTINCT:NAME !NAME:SCREENSHOT !NAME:SNAPCHAT !NAME:SCREENRECORDER SORT:DM\r\n# 10.10.2023 - Kl.14:47: Mobilbackup Bilder\r\nDM:2023.01.01..2024.01.01 PATH:\"Personlig\\MobilBackup\\*_JORN\" path:\"_JORN\" DC: IMG: DISTINCT:NAME !NAME:SCREENSHOT !NAME:SNAPCHAT !NAME:SCREENRECORDER SORT:DM\r\n# 25.11.2023 - Kl.13:53:\r\n!PATH:YVONNE PATH:\"Privat\" PATH:\"Personlig\" DC: IMG: DISTINCT:NAME !NAME:SCREENSHOT !NAME:SNAPCHAT !NAME:SCREENRECORDER SORT:DM DM:2022.01.01..2022.12.01\r\n!PATH:YVONNE PATH:\"Privat\" PATH:\"Personlig\" DC: IMG: DISTINCT:NAME !NAME:SCREENSHOT !NAME:SNAPCHAT !NAME:SCREENRECORDER SORT:DM DM:2022.06.01..2022.08.01 PATH:OPPUSS\r\n# 10.10.2023 - Kl.14:55: Inspirerende musikk\r\nEXT:MP3 PATH:MUSI DISTINCT:NAME PATH:\"Inspirasjon\\Inspirerende Musikk\" SORT:DM\r\n\r\n\r\n\r\n// ============================================================================\r\n24.09.2023 - Kl.13:37: Søk siste filer/mapper\r\n// -------------\r\n// 14:12:15:\r\n\"%APPDATA%\\Microsoft\\Windows\\Recent\" !PATH:CustomDestinations !PATH:AutomaticDestinations DISTINCT:NAME DATE-ACCESSED:CURRENTDAY DEPTH:8 !EXT:DELETED SORT:DATE-ACCESSED\r\n\r\n\r\n// -------------\r\n// 14:06:01:\r\ndir: date-accessed:currentday sort:date-accessed\r\n// dm:currenthour\r\n// dm:currentday\r\n// dm:currentweek\r\n// dm:currentmonth\r\n\r\nSøk -> Finn all extensions relatert til sublime text:\r\n- !EXT:LNK PATH:Sublime DISTINCT:EXT FILE: PATH:%APPDATA% DISTINCT:NAME DISTINCT:EXT !PATH:(*)\r\n\r\n- PIC: PATH:\"SUBSTANCE\"|PATH:\"TEXTURES\"|PATH:\"3D\" !PATH:\"%PROGRAMFILES%\" !PATH:\"%PROGRAMFILES(X86)%\"\r\n\r\nSøk -> Textures for 3D scener:\r\n\r\nSøk -> Vault: Inventor\r\n\"%APPDATA%\\Autodesk\\Autodesk Vault Professional 2023\\Servers\" AND path:\"pdm-vltnor\"\r\n// PRIVAT -> BILDER -> MOBILBACKUP\r\n\"D:\\Dokumenter O.l\\Documens\\Privat\\Dokumenter\\Personlig\\MobilBackup\" dm:2023.04.10-2024.05.10 sort:dm-ascending !path:annet !path:snapsh\r\n\r\nOpen multiple instances of Everything:\r\n- C:\\Program Files\\Everything 1.5a\\Everything64.exe -instance \"InstanceName\"\r\n// FIND DUPLICATES\r\n// \"https://www.voidtools.com/forum/viewtopic.php?p=57440&hilit=duplicates&sid=e42dcf7e06c8dcf83fef7548a64d5ef3#p57440\"\r\n\r\n// To find files/folders with the same name and size, include the following in your search:\r\ndupe:name;size\r\n\r\n// To find files with duplicated content (this is extremely slow, don't use for large scopes):\r\ndupe:size;sha256\r\n\r\n// To find duplicated files between two folders:\r\n\"D:\\Dokumenter O.l\\Documens\\Privat\\Dokumenter\\Personlig\\MobilBackup\\2023.05.30_YVONNE\" | \"D:\\Dokumenter O.l\\Documens\\Privat\\Dokumenter\\Personlig\\MobilBackup\\2023.01.10_YVONNE - MOVED\" dupe:name;size;dm !dir:\r\n\r\n// For example, to find unique filenames, include the following in your search:\r\nunique:name;size;sha256\r\n\r\n\r\n// Date Range\r\ndm:2022.12.01-2022.12.24\r\ndm-month:11-12\r\ndm-week:51-52\r\ndm-day:365\r\ndm-hour:8-16\r\n// Previous Day\r\ndm:lastyear\r\ndm:lastmonth\r\ndm:lastweek\r\ndm:lastday\r\ndm:lasthour\r\n// Current Day\r\ndm:currentyear\r\ndm:currentmonth\r\ndm:currentweek\r\ndm:currentday\r\ndm:currenthour\r\ndistinct:full-path\r\n\r\n\r\n// Find files where the first word is duplicated:\r\nregex:^(.+)\\b dupe:1\r\nregex:^(.+)\\b\\d+$ dupe:1\r\n\r\n// List all simulators in: D:\\Simulator Media\r\n<dir: path:\"D:\\Simulator Media\"> depth:2 distinct:path distinct:full-path\r\n\r\n<dir: path:\"D:\\Simulator Media\"> depth:2 max-child-depth:1 distinct:path distinct:full-path column1:=(stem-length:) columns:dm;dc;size;path;name;child-size;column1\r\n\r\n\r\n\r\nname-frequency:>20\r\nsize:>500KB\r\nsize:<24bytes AND !folder: AND sort:size-ascending;dm;ext;name\r\nsibling-file-count:>=10\r\ndistinct:path\r\nexact:basename:hosts\r\n!child-count:0\r\n\r\n\r\ndm:today:\r\ndm:week:\r\ndm:month:\r\nchild-count:>300\r\nchild-file:ext:mp3\r\ndescendant-count:>10000\r\nchars:20-30\r\ngroup-colors:path no-alternate-row-color: sort:path;size\r\ndistinct:ext AND pic:\r\n\r\n\r\n\"dm:today: path:3ds AND path:2023 AND distinct:ext ext:mnux\"\r\n\"dm:today: path:3ds AND path:2023 AND ext:clrx\r\n\"file: AND !ext:.tar.gz AND !ext:xaml !ext:log AND !ext:json dm:lasthour path:3ds AND path:2023 AND ext:clrx\r\n\"file: AND !ext:.tar.gz AND !ext:xaml !ext:log !ext:bak !ext:json !ext:dat AND dm:lasthour path:3ds AND path:2023 AND ext:clrx\r\n\"dm:lasthour path:3ds AND path:2023 AND ext:clrx\r\n\"\"\"C:\\Users\\<USER>\\AppData\\Local\\Autodesk\\3dsMax\\*2021*\\ENU\"\" AND file: AND !bookmark:\"\"WIP: 3dsMax-Config-Recent 4\"\";\"\r\n\"dir: \"\"D:\\Simulator Media\"\" depth:2 distinct:path\"\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n// CMD install\r\n-enable-update-notification\r\n-install-run-on-system-startup\r\n-install-folder-context-menu\r\n-install-start-menu-shortcuts\r\n-install-desktop-shortcut\r\n-install-quick-launch-shortcut\r\n-install-efu-association\r\n-install-url-protocol\r\n-language 1033\r\n\r\n\r\n\r\n// -----------------------------------------------------\r\n// testing:\r\next:pdf sort:chars:\r\ncolumn1:=len($regex:path:(\\d):)  add-column:column1\r\ncolumn1:=len($stem-length) add-column:column1\r\nsort:filenamelength:\r\nsort:stem-length\r\n// -----------------------------------------------------\r\n// works\r\n\"D:\\DevTest\\RigOfficeDownload_Output\\R13387\" column1:=(stem-length:) add-column:column1\r\n// -----------------------------------------------------\r\n// notes\r\nstem = filename without extension\r\n// -----------------------------------------------------\r\n\r\n\r\nregex:(?i).\\\\download AND column1:=(sibling-file-count:)regex:(?i).download) add-column:column1\r\n\r\n// Search files which exists in a directory that contains (X) files:\r\n\r\n// Only one matching file-extension can exist in any given folder\r\ndistinct:ext\r\n\r\n// Number of times file has been opened/executed (interacted with)\r\ncolumn1:=(runcount:) add-column:column1\r\n\r\n// Disables highlight for match-phrase (basically hides the match-string)\r\n\r\n// Only display unique paths\r\n\r\n// Force update a file-attribute\r\nreparse-tag:0xA000000C\r\n\r\n// Search and use custom macros\r\nbookmark:\"My Bookmark\";\"My bookmark with a macro\":\"my bookmark parameters\";bookmark1\r\n\r\n// Max number of parent (until root)\r\n\"D:\\Data\\3D-Relatert\\3D Modeller\" parents:<8\r\n\r\n// Files 10 levels down\r\n\"D:\\Data\\3D-Relatert\\3D Modeller\" depth:>11\r\n\r\n// Clamp search by condition\r\ndepth:6 max-child-depth:>=2\r\n\r\n// Works: All unique file extensions\r\n// Works: All unique file extensions that does -not- contain digits\r\ndistinct:ext !regex:(\\d)\r\n\r\n(DATE-MODIFIED:CURRENTHOUR OR DATE-MODIFIED:LASTHOUR OR DATE-CREATED:CURRENTHOUR )\r\n\r\n(PARENT-DATE-MODIFIED:CURRENTHOUR OR DATE-MODIFIED:LASTHOUR) AND EX1: AND EX2: AND EX3:\r\n\r\n\r\n// Only (X) file within any directory can match\r\n// char-range:A-B\r\n// char-range:0x1F600..0x1F64F\r\n// character-encoding:ANSI sort:size\r\n// distinct:ext ext:xlsx* character-count:>28 sort:dm\r\n// case-sensitive-dir:yes\r\n// ancestor:\"C:\\Program Files\"\r\n// reparse-target:C:\\User*\r\n// layout:menubar;preview;folders;status-bar;header\r\n// \"C:\\Users\\<USER>\\AppData\\Local\\Temp*\" add-columns:width;height add-layout:preview;folders\r\n// secondary-sort:size secondary-sort:\"date modified\" secondary-sort:size-ascending secondary-sort:path-descending secondary-sort:size secondary-sort-ascending:\r\n// runcount:>1\r\n// regex:(?i).\\\\download AND column1:=(sibling-file-count:)regex:(?i).download) add-column:column1 column2:=det secondary-sort:path add-column:column2\r\n// column1:=d secondary-sort:path add-column:column1\r\n// sort:dm distinct:ext distinct:path !regex:(\\d) secondary-sort:dm secondary-sort-descending:ext\r\n// sort:dm;size; distinct:ext distinct:path !regex:(\\d) secondary-sort:dm secondary-sort-descending:ext\r\n// \"D:\\Dokumenter O.l\\Documens\\Dropbox\\Everything - Customization Project\\Playground\\Everything-SDK\" sort:dm secondary-sort:size\r\n:: ------------------------------------------\r\n\r\nBilder/Video fra mobil:\r\ndm:2022.12.01..2023.02.01 AND !name:snapcha AND !name:Screenshot AND !name:screenrecorder AND dc:  AND \"D:\\Dokumenter O.l\\Documens\\Privat\\Dokumenter\\Personlig\\MobilBackup\\2023.01.10_YVONNE\" | \"D:\\Dokumenter O.l\\Documens\\Privat\\Dokumenter\\Personlig\\MobilBackup\\2022.12.31_JORN\" | \"D:\\Dokumenter O.l\\Documens\\Privat\\Dokumenter\\Personlig\\MobilBackup\\2023.03.24_JORN\"\r\n\r\n\r\nregex:(?i)down\r\npath:C:\\Progs\r\n\r\n:: Works: All unique file extensions\r\n:: Works: All unique file extensions that does -not- contain digits\r\n\r\n\r\n:: Works: Checking multiple extensions within one macro (using semicolon as separator)\r\next:avi;divx;flv;hdmov;ifo;ivf;m1v;m2p;m2t;m2ts;m2v;m4v;mkv;mp2v;mp4;mp4v;mpe;mpeg;mpg;mpls;mpv2;mpv4;mov;mts;ogm;ogv;pss;pva;qt;ram;ratdvd;rm;rmm;rmvb;roq;rpm;smil;smk;swf;tp;tpr;ts;vob;vp6;webm;wm;wmp;wmv\r\n\r\n:: names that exists more than fifty places\r\nname-frequency:>50\r\n\r\n:: sort by multiple\r\nsort:dm;path;ext;name;size; file\r\n\r\n:: works\r\nDepth:>5\r\n\r\n\r\n\r\nregex:extension:mp4\r\n\r\ncolumn1:=sibling-file-count:D:\\Dokumenter O.l\\Documens\\Privat\\Dokumenter\\Personlig\\MobilBackup\\2023.03.24_JORN\\Diverse\\Downloads\\ add-column:column1\r\ncolumn1:=sibling-file-count:Privat\\Dokumenter\\Personlig\\MobilBackup\\2023.03.24_JORN add-column:column1\r\n\r\nregex:(?i).\\\\down\r\n\r\n$size:*8\r\n\r\n::  TRENGER BARE Å PLUKKE UT FØRSTE DIGIT AV RESULTATET FRA DENNE (\\d):\r\nregex:(?i).\\\\down AND column1:=(sibling-file-count:regex:(?i).\\\\down) add-column:column1\r\n\r\nregex:(?i).\\\\down AND column1:=sibling-file-count:regex:(?i).\\\\down) add-column:column1\r\n\r\nregex:(?i).\\\\down AND column1:=len(sibling-file-count:regex:(?i).\\\\down) add-column:column1\r\n\r\n\r\n\r\n\r\ncolumn1:=len(sibling-file-count:folder:regex:(?i).\\\\down) add-column:column1\r\ncolumn1:=len(sibling-file-count:regex:(?i).\\\\down)  add-column:column1\r\n\r\nregex:(?i).\\\\down column1:=len(sibling-file-count)  add-column:column1\r\ncolumn1:=len(sibling-file-count:*.txt)  add-column:column1\r\n\r\next:mp3 dm:1year.. column1:=len($regex:path:(\\d):)  add-column:column1\r\n\r\n\r\next:mp3 dm:1year.. column1:=len($depth:)  add-column:column1\r\n\r\n\r\ncolumn1:=len($regular-expression-match-1:) add-column:column1\r\n\r\npath:C:\\Progs addcolumns:column1\r\n\r\nregex:$name, [^_], \"\")\r\n\r\n\r\n::\r\ndepth:15\r\n\r\nline-count:>1000\r\n\r\n::\r\n\r\n:: works, show only path\r\n:: reference: https://www.voidtools.com/forum/viewtopic.php?p=54894&hilit=ONLY+UNIQUE&sid=5331f8d62e30abae9e5174d244663abd#p54894\r\n:: https://www.voidtools.com/forum/viewtopic.php?t=10176\r\ndistinct:stem\r\ndistinct:path AND distinct:name\r\n\r\n\r\n:: works, if *down* in name or path, ignores casing\r\n\r\n:: Find all mp3files within previous year\r\next:mp3 dm:1year..\r\n\r\n\r\n:: Search for folders with the specified maximum child depth.\r\n\r\n\r\n:: Search for files with the specified extension frequency.\r\nextension-frequency:>10000\r\n\r\n\r\n:: Search for the specified extension (without the dot)\r\nextension:mp3\r\nextension:jpg\r\nregex:extension:^mp\r\n\r\n\r\n:: Works - Sort by exe first\r\n\r\n\r\n:: For example, show the size in bits in column 1:\r\ncolumn1:=$size:*8 add-column:column1\r\n\r\n:: Filter out anything other than the underscores from the filename\r\nregex:^(\\_+)\r\n\r\n\r\n:: 5 or more underscores in filename\r\nregex:^(?:[^_]*_){5,}[^_]*$\r\n:: 5 or more underscores in path\r\nregex:path:^(?:[^_]*_){5,}[^_]*$\r\n:: 5 or more \\ (levels) in path\r\nregex:path:^(?:[^\\\\]*\\\\){5,}[^\\\\]*$\r\n\r\nregex:path:(\\d)\r\n\r\n:: Antall digits i et filename (Kan brukes som sort-column)\r\n3d *\\*\\*\\*\\ regex:(\\d)\r\n\r\n\r\nWIP: C:\\Users\\<USER>\\AppData\\Roaming\\Everything\\\r\n\r\nPlace when finished: F:\\Workflow\\Everything\r\n\r\nExtract diff: D:\\Development\r\n\r\nchildcount:<count>  Search for folders that contain the specified number of subfolders and files.\r\n\r\n---\r\n"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n(No open tabs)\n\n# Current Time\n4/3/2025, 6:36:03 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<read_file>\n<path>notes_ini.ini</path>\n</read_file>\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}]}, {"role": "user", "content": [{"type": "text", "text": "[read_file for 'notes_ini.ini'] Result:"}, {"type": "text", "text": "\r\n;  ------------------------------------------------------------------------------------------------------------------\r\n;  DOCUMENTATION: https://www.voidtools.com/support/everything/searching/\r\n;  ------------------------------------------------------------------------------------------------------------------\r\n;  SEARCHING\r\n;  SJEKK: https://www.voidtools.com/support/everything/customizing/\r\n\r\n;  - Use the search edit to type in a search.\r\n;  - Type in a partial filename, files and folders matching your partial filename search will be instantly displayed in the result list\r\n;    For example, to find files and folders with the text \"reports\" in the filename, search for: reports\r\n;  - To limit the results to a drive, simply search for that drive\r\n;    For example, to find files and folders on your D: drive, search for: d:\r\n;  - Include the path to limit the results to a folder\r\n;    For example, to find files and folders in the folder D:\\Downloads, search for: d:\\downloads\\\r\n;  - Use double quotes to escape spaces\r\n;    For example, to find files and folders in C:\\Program Files, search for: \"c:\\program files\\\"\r\n;  - Use \\ to search for partial paths\r\n;    For example, to find files and folders in a folder ending with documents, search for: documents\\\r\n;  - Use a space to combine searches\r\n;    For example, to find files and folders containing the text order, located in the folder starting with work, search for: \\work order\r\n\r\n\r\n\r\n# =============================================================================\r\n# 12.01.2024 - Kl.09:48: Check what these does\r\nauto_complete_search_command=0\r\n\r\n\r\n# 11.12.2023 - Kl.18:31: Check what these does\r\nallow_tabs=1\r\nopen_new_window_in_tab=0\r\ngroup_lines=0\r\ngroup_match_case=0\r\ngroup_colors=0\r\nalpha_instance=1\r\nrename_trim=1\r\nrename_trim_stem_and_extension=0\r\nmouse_hwheel_action=3\r\nwheel_scroll_lines=0\r\nmouse_wheel_routing=0\r\nctrl_tab_action=1\r\npaste_multiline_type=0\r\ntreeview_order=0\r\ntreeview_high=256\r\ntreeview_tab_stop=0\r\ntreeview_subfolders_tab_stop=0\r\ntreeview_clear_selection_on_close=0\r\ntreeview_labels=1\r\n; =============================================================================\r\n; 10.12.2023 - Kl.09:18:\r\n; empty list if nothing us searcged for\r\nhide_empty_search_results=1\r\n\r\n; hotkey: \"CTRL+|\" -> toggle everything window\r\ntoggle_window_key=476\r\n\r\n; do not select extension when renaming\r\nrename_select_filepart_only=0\r\n\r\n; always show search suggestions\r\nsearch_history_always_suggest=1\r\n; sort suggestions by number of times searched for (count)\r\nsearch_history_show_all_sort=1\r\n\r\n; show \"copy name\" in the rightclick contextmenu\r\nshow_copy_name=1\r\n; show \"copy path\" in the rightclick contextmenu\r\nshow_copy_path=1\r\n; show \"copy path\" when shift-rightclicking in the contextmenu\r\nshow_copy_path=3\r\n; show \"copy full path\" in the rightclick contextmenu\r\nshow_copy_full_name=2\r\n\r\n; set the font for all menus and views\r\n; font=Fira Code\r\n\r\n; set the font and size to consolas specifically for the filters sidebar\r\n; filter_sidebar_font=Consolas\r\n; filter_sidebar_font_size=11\r\n; set the font to fira  specifically for the filters sidebar (default size)\r\nfilter_sidebar_font=Fira Code Light\r\nfilter_sidebar_font_size=\r\n\r\n\r\n# =============================================================================\r\n# 20.11.2023 - Kl.16:31:\r\n; sjekk:\r\nmatch_whole_filename_when_using_wildcards\r\n\r\n# 20.11.2023 - Kl.16:37:\r\n\r\nColumn Header Search: To search for text in an active column: Right click a\r\nresult list column header and click Search property... Searches will generally\r\nmatch the text shown for the associated property.\r\n\r\n\r\n; === \"Everything.ini - Notes.ini\" ===\r\n; - \"Everything.ini - Notes.ini\" serves as a curated, organized reference to\r\n;   the frequently changing 'Everything.ini'. It's designed as a static\r\n;   counterpart to facilitate the management and standardization of my\r\n;   workflow.\r\n\r\n; - \"Everything.ini\" is automatically updated by Everything and contains settings\r\n;   adjusted during your work session. Because it updates frequently,\r\n;   maintaining standardized configurations can be challenging. This reference\r\n;   file simplifies that process.\r\n\r\n; Intent:\r\n; - The goal is to provide an easily applicable, structured, and commented\r\n;   standardization for Everything, making it easier to track and revert\r\n;   changes.\r\n\r\n\r\n; -----------------------------------------------------------------------------\r\n; === TRACKED PARAMETERS ===\r\n\r\n; Do not select extension when renaming\r\nrename_select_filepart_only=2\r\n\r\n; Allow grouping commands within parenthesis (e.g: (!path:\".git\" !ext:pyc !ext:lnk) AND (path:\"sublime text\"))\r\nallow_round_bracket_parenthesis2\r\n\r\n\r\n; Allow Multiple Instances Of Everything\r\nallow_multiple_windows=1\r\n\r\n; -----------------------------------------------------------------------------\r\n; === GIT COMMAND ===\r\n; - Display -all- of the difference in a textfile\r\n; git --no-pager diff \"Everything.ini\"\r\n\r\n\r\n;\r\nsed -i '/^bookmark_organize_last_import_filename=/d' \"Everything.ini\"\r\n bookmark_organize_last_import_filename=\r\n; -----------------------------------------------------------------------------\r\n; === EXCLUDE ===\r\n; ===  The parameters below should be automatically discarded from git ===\r\nalways_keep_sort=\r\nbinary_type_column_width=\r\nbookmark_organize_last_export_filename=\r\nbookmark_organize_last_import_filename=\r\nbookmark_select_high=\r\nbookmark_select_key_order=\r\nbookmark_select_key_wide=\r\nbookmark_select_macro_order=\r\nbookmark_select_macro_wide=\r\nbookmark_select_maximized=\r\nbookmark_select_name_order=\r\nbookmark_select_name_wide=\r\nbookmark_select_search_order=\r\nbookmark_select_search_wide=\r\nbookmark_select_tree_wide=\r\nbookmark_select_wide=\r\nbookmarks_sidebar_high=\r\nbookmarks_sidebar_order=\r\nbookmarks_sidebar_tab_stop=\r\ncolumn_0_column_width=\r\ncolumn_1_column_pos=\r\ncolumn_1_column_visible=\r\ncolumn_1_column_width=\r\ncolumn_2_column_pos=\r\ncolumn_2_column_visible=\r\ncolumn_2_column_width=\r\ncreation_time_column_pos=\r\ncreation_time_column_visible=\r\ncreation_time_column_width=\r\nextension_column_pos=\r\nextension_column_visible=\r\nextension_column_width=\r\nfile_close_file_list_keys=\r\nfile_new_search_window_keys=\r\nfile_open_file_list_keys=\r\nhelp_regex_syntax_high=\r\nhelp_regex_syntax_maximized=\r\nhelp_regex_syntax_wide=\r\nhelp_regex_syntax_x=\r\nhelp_regex_syntax_y=\r\nhelp_search_commands_x=\r\nhelp_search_commands_y=\r\nhelp_search_syntax_maximized=\r\nlast_options_page=\r\nlast_write_time_column_pos=\r\nlast_write_time_column_visible=\r\nlast_write_time_column_width=\r\nleft_sidebar_wide=\r\nname_column_pos=\r\nname_column_width=\r\noptions_advanced_name_wide=\r\noptions_advanced_value_wide=\r\noptions_last_filelist_filename=\r\noptions_last_folder_custom_path=\r\noptions_last_folder_path=\r\noptions_tree_wide=\r\npath_column_pos=\r\npath_column_visible=\r\npath_column_width=\r\nplugin_enabled=\r\nplugin_filename=\r\nregular_expression_match_0_column_pos=\r\nregular_expression_match_0_column_visible=\r\nregular_expression_match_0_column_width=\r\nregular_expression_match_1_column_pos=\r\nregular_expression_match_1_column_visible=\r\nregular_expression_match_1_column_width=\r\nright_sidebar_wide=\r\nservice_pipe_name=\r\nservice_timeout=\r\nsize_column_pos=\r\nsize_column_visible=\r\nsize_column_width=\r\nsort=\r\nsort_ascending=\r\ntext_plain_extensions=\r\ntype_column_pos=\r\ntype_column_visible=\r\ntype_column_width=\r\n"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n(No open tabs)\n\n# Current Time\n4/3/2025, 6:36:14 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<read_file>\n<path>evr_scratchpad.py</path>\n</read_file>\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}]}, {"role": "user", "content": [{"type": "text", "text": "[read_file for 'evr_scratchpad.py'] Result:"}, {"type": "text", "text": "# cleanup space\r\n< !exact:ancestor-name:\".git\" > < !EX1: AND !EX2: AND !EX3: AND !EX4: > < size:>100mb >\r\n\r\nc: parent-size:>1GB parent-size:<30GB !appdata: file:\r\n\r\n< depth:<6  size:>2500mb sort:parent-size;path;folder-depth-ascending;size > file:\r\n\r\n!ex1:|ex2:|ex3:|ex4: sort:size\r\n\r\n# =======================================================\r\n# [2025.04.03 18:14]\r\n<py:|md:|json:><path:\"__GOTO__\"> content:\"=================\" <sort:dm>\r\n"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n(No open tabs)\n\n# Current Time\n4/3/2025, 6:36:22 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<read_file>\n<path>notes_search_syntax.cpp</path>\n</read_file>\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}]}, {"role": "user", "content": [{"type": "text", "text": "[read_file for 'notes_search_syntax.cpp'] Result:"}, {"type": "text", "text": "// ============================================================================\r\n// Help -> Everything Search Syntax\r\n// ============================================================================\r\n\r\nOperators:\r\n    space  -> AND\r\n    |      -> OR\r\n    !      -> NOT\r\n    < >    -> Grouping\r\n    \" \"    -> Search for an exact phrase.\r\n\r\nWildcard matching:\r\n    *   Matches zero or more characters (except \\).\r\n    **  Matches zero or more characters.\r\n    ?   Matches one character (except \\).\r\n    The whole filename is matched when using a wildcard.\r\n\r\nFilename parts:\r\n    Full Path: C:\\folder\\file.txt\r\n    Name:      file.txt\r\n    Path:      C:\\folder\r\n    Stem:      file\r\n    Extension: txt\r\n\r\nCharacter entities:\r\n    &#x20:  Literal space ( )\r\n    &vert:  Literal vertical line (|)\r\n    &excl:  Literal exclamation mark (!)\r\n    &lt:    Literal less than (<)\r\n    &gt:    Literal greater than (>)\r\n    &quot:  Literal double quote (\")\r\n    &#<n>:  Literal unicode character <n> in decimal.\r\n    &#x<n>: Literal unicode character <n> in hexadecimal.\r\n\r\nMacros:\r\n    audio:  Search for audio files.\r\n    zip:    Search for compressed files.\r\n    doc:    Search for document files.\r\n    exe:    Search for executable files.\r\n    pic:    Search for picture files.\r\n    video:  Search for video files.\r\n\r\nModifiers:\r\n    binary:     Match as a byte stream, \\\\ = \\ \\xff = hex byte.\r\n    case:       Match case.\r\n    diacritics: Match diacritical marks.\r\n    endwith:    Match the end of the filename or property.\r\n    file:       Match files only.\r\n    folder:     Match folders only.\r\n    hex:        Convert double hex characters to bytes.\r\n    highlight:  Highlight the search term.\r\n    ignorepunc: Ignore punctuation in the filename or property.\r\n    ignorews:   Ignore white space in the filename or property.\r\n    len:        Match the length of the filename or property in UTF-16 characters.\r\n    metric:     Use metric prefixes for sizes.\r\n    fromdisk:   Search for properties and content from disk.\r\n    tonumber:   Convert text to a number for comparison\r\n    path:       Match path and filename.\r\n    prefix:     Match the start of a word.\r\n    regex:      Enable regular expressions.\r\n    startwith:  Match the start of the filename or property.\r\n    suffix:     Match the end of a word.\r\n    tostring:   Convert property to the display string for comparison\r\n    whole:      Match the whole filename or property.\r\n    ww:         Match whole words only.\r\n    wildcards:  Enable content wildcards.\r\n    Disable a modifier with the no prefix.\r\n\r\nwildcards: search modifier syntax:\r\n    *     Matches any character zero or more times.\r\n    ?     Matches any single character.\r\n    #     Matches any single digit (0-9).\r\n    [ ]   Matches any one of the characters specified in the set.\r\n    [! ]  Matches any one character that is not specified in the set.\r\n    \\     Escape the following character.\r\n\r\nFunctions:\r\n    album:<text>            Search media files with the specified album.\r\n    artist:<text>           Search media files with the specified artist.\r\n    attrib:<attributes>     Search for files and folders with the specified file attributes.\r\n    bitdepth:<bitdepth>     Find images with the specified bits per pixel.\r\n    child:<filename>        Search for folders that contain a child with a matching filename.\r\n    childcount:<count>      Search for folders that contain the specified number of subfolders and files.\r\n    childfilecount:<count>  Search for folders that contain the specified number of files.\r\n    childfoldercount:<n>    Search for folders that contain the specified number of subfolders.\r\n    comment:<text>          Search media files with the specified comment.\r\n    content:<text>          Search file content with the specified text.\r\n    count:<max>             Limit the number of results to max.\r\n    da:<date>               Search for files and folders with the specified date accessed.\r\n    dc:<date>               Search for files and folders with the specified date created.\r\n    depth:<count>           Search for files and folders with the specified number of parent folders.\r\n    dimensions:<w>X<h>      Find images with the specified width and height.\r\n    dm:<date>               Search for files and folders with the specified date modified.\r\n    dr:<date>               Search for files and folders with the specified date run.\r\n    dupe:<property-type>    Search for duplicated files with the specified property type.\r\n    empty:                  Search for empty folders.\r\n    ext:<ext1;ext2;...>     Search for files with an extension in the specified semicolon delimited extension list.\r\n    filelist:<fn1;fn2;...>  Search for a list of filenames in the specified semicolon delimited file list.\r\n    filelistfilename:<name> Search for files and folders belonging to the file list filename.\r\n    frn:<frn>               Search for files and folders with the specified File Reference Number.\r\n    genre:<text>            Search media files with the specified genre.\r\n    height:<height>         Find images with the specified height in pixels.\r\n    indextype:<type>        Search for the specified index type, eg: ntfs, refs, folder or filelist.\r\n    length:<length>         Search media files with the specified length.\r\n    name:<name>             Search for base filenames without the path part.\r\n    orientation:<type>      Search for images with the specified orientation (landscape or portrait).\r\n    parent:<path>           Search for files and folders in the specified path, excluding subfolders.\r\n    pathpart:<path>         Search for files and folders in the specified location.\r\n    root:                   Search for files and folders with no parent folders.\r\n    runcount:<count>        Search for files and folders with the specified run count.\r\n    size:<size>             Search for files with the specified size in bytes.\r\n    stem:                   Search for filenames without the extension.\r\n    title:<text>            Search media files with the specified title.\r\n    track:<number>          Find media files with the specified track number.\r\n    type:<type>             Search for files and folders with the specified type.\r\n    width:<width>           Find images with the specified width in pixels.\r\n\r\nFunction Syntax:\r\n    function:value       Equal to value within the specified granularity.\r\n    function:<=value     Less than or equal to value.\r\n    function:<value      Less than value.\r\n    function:=value      Exactly equal to value.\r\n    function:==value     Exactly equal to value.\r\n    function:>value      Greater than value.\r\n    function:>=value     Greater than or equal to value.\r\n    function:!=value     Not equal to value.\r\n    function:!value      Not equal to value with specified granularity.\r\n    function:start..end  Is in the range of values from start to end.\r\n    function:start-end   Is in the range of values from start to end.\r\n\r\nSize Syntax:\r\n    size[kb|mb|gb]\r\n\r\nSize Constants:\r\n    empty\r\n    tiny     (0 KB < size <= 10 KB)\r\n    small    (10 KB < size <= 100 KB)\r\n    medium   (100 KB < size <= 1 MB)\r\n    large    (1 MB < size <= 16 MB)\r\n    huge     (16 MB < size <= 128 MB)\r\n    gigantic (size > 128 MB)\r\n    unknown\r\n\r\nDuration Syntax:\r\n    [[[DD:]HH:]MM:]SS[.sss]\r\n    [<n><d|day|days>][<n><h|hour|hours>][<n><m|min|mins|minute|minutes>][<n><s|sec|secs|second|seconds>]\r\n    P[n]Y[n]M[n]DT[n]H[n]M[n]S\r\n    P[n]W\r\n    PYYYYMMDDThhmmss\r\n    P[YYYY]-[MM]-[DD]T[hh]:[mm]:[ss]\r\n\r\nDuration Constants:\r\n    very-short  (Under 1 min)\r\n    short       (1 - 5 mins)\r\n    medium      (5 - 30 mins)\r\n    long        (30 - 60 mins)\r\n    very-long   (Over 60 mins)\r\n    unknown\r\n\r\nDate Syntax:\r\n    year\r\n    month/year or year/month depending on locale settings\r\n    day/month/year, month/day/year or year/month/day depending on locale settings\r\n    YYYY[-MM[-DD[Thh[:mm[:ss[.sss]]]]]]\r\n    YYYYMMDD[Thh[mm[ss[.sss]]]]]\r\n    YYMMDD\r\n    FILETIME (>99999999)\r\n\r\nDate Constants:\r\n    today\r\n    yesterday\r\n    <last|current|this|coming|next><year|month|week>\r\n    <last|coming|next><x><years|months|weeks|days|hours|minutes|mins|seconds|secs>\r\n    past[x]<year[s]|month[s]|week[s]|day[s]|hour[s]|minute[s]|min[s]|second[s]|sec[s]>\r\n    <prev|previous>[x]<year[s]|month[s]|week[s]>\r\n    january|february|march|april|may|june|july|august|september|october|november|december\r\n    jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec\r\n    sunday|monday|tuesday|wednesday|thursday|friday|saturday\r\n    sun|mon|tue|wed|thu|fri|sat\r\n    mtd|ytd\r\n    unknown\r\n\r\nAttribute Constants:\r\n    A   Archive\r\n    C   Compressed\r\n    D   Directory\r\n    E   Encrypted\r\n    H   Hidden\r\n    I   Not content indexed\r\n    L   Reparse point\r\n    M   Recall on data access\r\n    N   Normal\r\n    O   Offline\r\n    P   Pinned\r\n    R   Read only\r\n    S   System\r\n    T   Temporary\r\n    U   Unpinned\r\n    V   Integrity stream\r\n    X   No scrub data\r\n"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n(No open tabs)\n\n# Current Time\n4/3/2025, 6:36:30 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<read_file>\n<path>search_ux.png</path>\n</read_file>\n\n[Response interrupted by a tool use result. Only one\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}]}, {"role": "user", "content": [{"type": "text", "text": "[read_file for 'search_ux.png'] Result:"}, {"type": "text", "text": "The tool execution failed with the following error:\n<error>\nError reading file: {\"name\":\"<PERSON>rror\",\"message\":\"Cannot read text for file type: .png\",\"stack\":\"Error: Cannot read text for file type: .png\\n\\tat fle (c:\\\\Users\\\\<USER>\\\\Desktop\\\\User\\\\Nas_Flow_Jorn\\\\__GOTO__\\\\Apps\\\\app_vscode\\\\exe\\\\data\\\\extensions\\\\saoudrizwan.claude-dev-3.8.6\\\\dist\\\\extension.js:1183:7721)\\n\\tat async zhe.presentAssistantMessage (c:\\\\Users\\\\<USER>\\\\Desktop\\\\User\\\\Nas_Flow_Jorn\\\\__GOTO__\\\\Apps\\\\app_vscode\\\\exe\\\\data\\\\extensions\\\\saoudrizwan.claude-dev-3.8.6\\\\dist\\\\extension.js:2585:4119)\"}\n</error>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n(No open tabs)\n\n# Current Time\n4/3/2025, 6:36:47 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<read_file>\n<path>debug-grp-search-ex.txt.py</path>\n</read_file>\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}]}, {"role": "user", "content": [{"type": "text", "text": "[read_file for 'debug-grp-search-ex.txt.py'] Result:"}, {"type": "text", "text": "# =============================================================================\r\n# 14.07.2024 - 19:08:25\r\ngrp-paths-ex:\r\ngrp-paths-ex: grp-exts-ex:\r\ngrp-paths-ex: grp-exts-ex: grp-parents-ex:\r\ngrp-paths-ex: grp-exts-ex: grp-parents-ex: grp-names-ex:\r\ngrp-paths-ex: grp-exts-ex: grp-parents-ex: grp-names-ex: grp-strings-ex:\r\ngrp-paths-ex: grp-exts-ex: grp-parents-ex: grp-names-ex: grp-strings-ex: grp-meta-ex:\r\ngrp-paths-ex: grp-exts-ex: grp-parents-ex: grp-names-ex: grp-strings-ex: grp-meta-ex: grp-regex-ex:\r\ngrp-paths-ex: grp-exts-ex: grp-parents-ex: grp-names-ex: grp-strings-ex: grp-meta-ex: grp-regex-ex: grp-patterns-ex:\r\n\r\ngrp-paths-ex: grp-exts-ex: grp-parents-ex: grp-names-ex: grp-strings-ex: grp-meta-ex: grp-regex-ex: !< GUID:|HASH:>\r\n\r\n\r\n\r\ngrp-paths-ex:\r\n    nohighlight:!< (!ext:\"ms|py|nss|bat|cmd|mcr|mse\" path:\"Cache\")|(dir: path:\"Atlassian/SourceTree\")|(dir: path:\"Sync.Config\")|(dir: path:\"Windows Defender\")|path:\"%WINDIR%\"|path:\".dropbox.cache\"|path:\"appcompat\"|path:\"Autodesk/ADPSDK\"|path:\"Autodesk/ODIS\"|path:\"C:/$WinREAgent\"|path:\"C:/Windows/Logs\"|path:\"Chrome/User Data/Safe Browsing\"|path:\"customDestinations-ms\"|path:\"GoogleUpdater\"|path:\"inetcache\"|path:\"leveldb\"|path:\"Local/Google/Chrome\"|path:\"Local/Microsoft/Windows/History\"|path:\"Logs/SystemIndex\"|path:\"loguploadersettings*.ini\" |path:\"Microsoft/Windows/WebCache\"|path:\"OneDrive/logs\"|path:\"Packages/Microsoft.Windows\"|path:\"programdata/microsoft/network\"|path:\"ProgramData/Oracle/Java\"|path:\"ProgramData/USOPrivate\"|path:\"Recent/AutomaticDestinations\"|path:\"sublimetext/exe/Data/Cache\"|path:\"sublimetext/exe/Data/Crash Reports\"|path:\"sublimetext/exe/Data/Index\"|path:\"sublimetext/exe/Data/Lib\"|path:\"sublimetext/exe/Data/Package Storage\"|path:\"sublimetext/exe/Data/Packages\"|path:\"sublimetext/exe/Lib\"|path:\"Sync.cache\"|path:\"Sync.Logs\"|path:\"Windows/Prefetch\"|path:\"WindowsApps/MicrosoftTeams\" >\r\n\r\ngrp-exts-ex:\r\n    nohighlight:!< (EXT:)|(EXT:\"crx*\")|EXT:\"accdb|adp|ani|aodl|aux|ax|backup|bak|bak2|bak3|bck|bck2|bin|bin64|bk|bkf|bkp|body|bup|cab|cache|cat|cc|ccx|cdmp|cdp|cdpresource|cdxml|changecount|chk|class|com|csproj.user|ctf|cubin|dat|db|db-shm|db-wal|db3|db3|dcl|dep|diskcopy42|diskcopy50|diz|dll|dmp|docbook|dof|ds_store|dsp|dspuser|dvc|edb|edb|err|err2|etl|evtx|fatbin|ftg|fts|gid|hds|hds|hxd|hxd|hyb|ibd|ico|idl|imagemanifest|inc|inf_loc|inline|ipch|ivt|jar|jcp|jcp|jfm|jfm|jtx|jtx|lck|ldb|leveldb|lock|log|log1|log2|log3|map|mdb|mdmp|mmap|mo|mum|ncb|nwl|nwl|od|odlgz|old|otc|parc|part|partial|pb|pck|pdb|pem|pf|pid|pl|pm|pma|pri|pri|prm|prt|ptx|pyc|pyf|qm|qmlc|rcache|res|res2|resx|sav|scc|sdb|sdf|sds|sid|sin|so|spi1d|spj|sqlite|srd-shm|store|sublime-package|sublime_session|sublime-workspace|suo|suo2|swo|swp|sym|sys|tbres|tcl|tek|temp|thumb|thumbsdb|tjg|tlog|tmp|tmpfile|trc|vscdb|wae|lnk|wbk|wer|wlog|woff|woff2|xbf|xin|xlf|zsh|~debris|~lock|~temp\" >\r\n\r\ngrp-parents-ex:\r\n    nohighlight:!< EXACT:ANCESTOR-NAME:\".git\"|EXACT:ANCESTOR-NAME:\"Crash Reports\"|EXACT:ANCESTOR-NAME:\".backups\"|EXACT:ANCESTOR-NAME:\"venv\" >\r\n\r\n\r\ngrp-names-ex:\r\n    nohighlight:!< file: CASE:EXACT:NAME:\"Exact Quick Find.sublime-settings\"|CASE:EXACT:NAME:\"globalStorage\"|CASE:EXACT:NAME:\"Local\"|CASE:EXACT:NAME:\"Network Persistent State\" >\r\n\r\ngrp-strings-ex:\r\n    nohighlight:!< \"automaticDestinations-ms\"|\"Indexed*DB\"|\"microsoft*.xml\"|\"mpcache\"|\"PcaGeneralDb\"|\"Url*.store.*\" >\r\n\r\ngrp-meta-ex:\r\n    nohighlight:!< (FILE: SIZE:<24bytes)|(DIR: CHILD-COUNT:0) >\r\n    nohighlight:!< (FILE: SIZE:<24bytes)|(DIR: CHILD-COUNT:0)|(childfilecount:0 childfoldercount:0) >\r\n    nohighlight:!< (FILE: SIZE:<24bytes)|(DIR: CHILD-COUNT:0)|(empty: child-file-count-from-disk:0 child-folder-count-from-disk:0 !attrib:L) >\r\n\r\n\r\n\r\ngrp-paths-ex: grp-exts-ex: grp-parents-ex: grp-names-ex: grp-strings-ex: grp-meta-ex: grp-regex-ex: grp-patterns-ex:\r\n\r\n\r\n# =============================================================================\r\nnohighlight:!< (!ext:\"ms|py|nss|bat|cmd|mcr|mse\" path:\"Cache\")|(dir: path:\"Atlassian/SourceTree\")|(dir: path:\"Sync.Config\")|(dir: path:\"Windows Defender\")|path:\"%WINDIR%\"|path:\".dropbox.cache\"|path:\"appcompat\"|path:\"Autodesk/ADPSDK\"|path:\"Autodesk/ODIS\"|path:\"C:/$WinREAgent\"|path:\"C:/Windows/Logs\"|path:\"Chrome/User Data/Safe Browsing\"|path:\"customDestinations-ms\"|path:\"GoogleUpdater\"|path:\"inetcache\"|path:\"leveldb\"|path:\"Local/Google/Chrome\"|path:\"Local/Microsoft/Windows/History\"|path:\"Logs/SystemIndex\"|path:\"loguploadersettings*.ini\" |path:\"Microsoft/Windows/WebCache\"|path:\"OneDrive/logs\"|path:\"Packages/Microsoft.Windows\"|path:\"programdata/microsoft/network\"|path:\"ProgramData/Oracle/Java\"|path:\"ProgramData/USOPrivate\"|path:\"Recent/AutomaticDestinations\"|path:\"sublimetext/exe/Data/Cache\"|path:\"sublimetext/exe/Data/Crash Reports\"|path:\"sublimetext/exe/Data/Index\"|path:\"sublimetext/exe/Data/Lib\"|path:\"sublimetext/exe/Data/Package Storage\"|path:\"sublimetext/exe/Data/Packages\"|path:\"sublimetext/exe/Lib\"|path:\"Sync.cache\"|path:\"Sync.Logs\"|path:\"Windows/Prefetch\"|path:\"WindowsApps/MicrosoftTeams\" > nohighlight:!< (EXT:)|(EXT:\"crx*\")|EXT:\"accdb|adp|ani|aodl|aux|ax|backup|bak|bak2|bak3|bck|bck2|bin|bin64|bk|bkf|bkp|body|bup|cab|cache|cat|cc|ccx|cdmp|cdp|cdpresource|cdxml|changecount|chk|class|com|csproj.user|ctf|cubin|dat|db|db-shm|db-wal|db3|db3|dcl|dep|diskcopy42|diskcopy50|diz|dll|dmp|docbook|dof|ds_store|dsp|dspuser|dvc|edb|edb|err|err2|etl|evtx|fatbin|ftg|fts|gid|hds|hds|hxd|hxd|hyb|ibd|ico|idl|imagemanifest|inc|inf_loc|inline|ipch|ivt|jar|jcp|jcp|jfm|jfm|jtx|jtx|lck|ldb|leveldb|lock|log|log1|log2|log3|map|mdb|mdmp|mmap|mo|mum|ncb|nwl|nwl|od|odlgz|old|otc|parc|part|partial|pb|pck|pdb|pem|pf|pid|pl|pm|pma|pri|pri|prm|prt|ptx|pyc|pyf|qm|qmlc|rcache|res|res2|resx|sav|scc|sdb|sdf|sds|sid|sin|so|spi1d|spj|sqlite|srd-shm|store|sublime-package|sublime_session|sublime-workspace|suo|suo2|swo|swp|sym|sys|tbres|tcl|tek|temp|thumb|thumbsdb|tjg|tlog|tmp|tmpfile|trc|vscdb|wae|lnk|wbk|wer|wlog|woff|woff2|xbf|xin|xlf|zsh|~debris|~lock|~temp\" > nohighlight:!< EXACT:ANCESTOR-NAME:\".git\"|EXACT:ANCESTOR-NAME:\"Crash Reports\"|EXACT:ANCESTOR-NAME:\".backups\"|EXACT:ANCESTOR-NAME:\"venv\" > nohighlight:!< file: CASE:EXACT:NAME:\"Exact Quick Find.sublime-settings\"|CASE:EXACT:NAME:\"globalStorage\"|CASE:EXACT:NAME:\"Local\"|CASE:EXACT:NAME:\"Network Persistent State\" > nohighlight:!< \"automaticDestinations-ms\"|\"Indexed*DB\"|\"microsoft*.xml\"|\"mpcache\"|\"PcaGeneralDb\"|\"Url*.store.*\" > nohighlight:!< (FILE: SIZE:<24bytes)|(DIR: CHILD-COUNT:0)|(empty: child-file-count-from-disk:0 child-folder-count-from-disk:0 !attrib:L) >\r\n# =============================================================================\r\n# 12.07.2024\r\n((PATH:\"%APPDATA%\" OR PATH:\"%USERPROFILE%\\Desktop\" OR PATH:\"\\Dropbox\" OR PATH:\"\\Onedrive\" OR PATH:\"\\Progs\\\" OR PATH:\"PRJ\\GIT\\JHP\" OR PATH:\"Project\\\" OR PATH:\"Person*\" OR NOHIGHLIGHT:PATH:\"D:\" OR NOHIGHLIGHT:PATH:\"Sublime Text\" ) OR (EVERYTHING: OR NOHIGHLIGHT:PATH:\"Sublime Text\" OR REGEX:EXTENSION:\"SUBLIME.*\" OR NOHIGHLIGHT:REGEX:PATH:\"SUBLIME.*\" OR NOHIGHLIGHT:REGEX:NAME:\"SUBLIME.*\" ) AND SORT:\"DM;PATH;EXT;NAME;SIZE;\" )\r\n\r\n< EXT:\"3dm|3ds|3g2|3gp|3gp2|3gpp|3mf|aac|abc|ac3|ai|aif|aifc|aiff|amr|amv|ape|asf|asx|au|avi|bat|bdmv|bik|blend|c4d|cda|cfg|cmd|cob|cpp|csv|d2v|dae|divx|doc|docm|docx|drc|dsa|dsm|dss|dsv|dts|dwf|dwg|dxf|eps|epub|evo|exe|f4v|fbx|fla|flac|flc|fli|flic|flv|gdoc|git|gitignore|glb|gltf|gsheet|gslides|hdmov|hrc|htm|html|iam|ifo|indd|ini|iob|ipj|ipt|it|iv|ivf|js|json|keynote|log|lwo|m1a|m1v|m2a|m2p|m2t|m2ts|m2v|m3u|m4a|m4b|m4p|m4v|ma|max|mb|mcr|md|mid|midi|mka|mkv|mod|mov|mp2|mp2v|mp3|mp4|mp4v|mpa|mpc|mpe|mpeg|mpg|mpls|mpp|mpv2|mpv4|ms|mse|msg|msi|msp|msu|mts|mxp|mzp|nss|nwc|nwd|nwf|obj|ogg|ogm|ogv|opus|osg|pages|pc3|pdf|ply|pps|ppsx|ppt|pptx|ps1|psd|pss|pva|py|pyi|pyw|qt|ra|ram|rar|ratdvd|rm|rmi|rmm|rmvb|roq|rpm|rtf|scene|scn|scr|skp|smil|smk|snd|spc|sql|step|stl|stp|sublime|swf|tp|tpr|ts|txt|u3d|url|usd|usdc|usdz|vb|vbs|vdx|vob|voc|vp6|vrml|vsd|vsdm|vsdx|vss|vssm|vssx|vst|vstm|vstx|wav|weba|webm|wm|wma|wmp|wmv|wpd|wps|wrl|x3d|xlam|xls|xlsb|xlsm|xlsx|xltm|xltx|xm|xml|xsi|yml|zip|zpr\" OR REGEX:EXTENSION:\"^SUBLIME.*\" >\r\n\r\n\r\n14.07.2024\r\nNOHIGHLIGHT:!< !PATH:\"PRJ/GIT\" !PATH:\"__SHELL__\" REGEX:PATH:\"\\{[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}\" | REGEX:PATH:\"\\{?[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}?\" | REGEX:PATH:\"[0-9A-Fa-f]{32}\" | REGEX:PATH:\"S-1-5-21-\\d+-\\d+-\\d+-\\d+\" >\r\n\r\nex1:\r\n    nohighlight:!< !ext:\"sublime*\"\r\n    !ext:bat|cmd|csv|doc|docx|exe|git|gitignore|js|json|mcr|md|mp3|mp4|ms|pdf|psd|py|rar|scene|url|xls|xlsx|zip\r\n\r\n\r\n    !< ext:7z|ext:bat|ext:cmd|ext:csv|ext:doc|ext:docx|ext:exe|ext:git|ext:json|ext:mcr|ext:md|ext:mp3|ext:mp4|ext:ms|ext:nss|ext:pdf|ext:psd|ext:py|ext:rar|ext:rar|ext:scene|ext:url|ext:xls|ext:xlsx|ext:zip >\r\n\r\n    !EXT:\"accdb|adp|ani|aodl|aux|ax|backup|bak|bak2|bak3|bck|bck2|bin|bin64|bk|bkf|bkp|body|bup|cab|cache|cat|cc|ccx|cdmp|cdp|cdpresource|cdxml|changecount|chk|class|com|csproj.user|ctf|cubin|dat|db|db-shm|db-wal|db3|db3|dcl|dep|diskcopy42|diskcopy50|diz|dll|dmp|docbook|dof|ds_store|dsp|dspuser|dvc|edb|edb|err|err2|etl|evtx|fatbin|ftg|fts|gid|hds|hds|hxd|hxd|hyb|ibd|ico|idl|imagemanifest|inc|inf_loc|inline|ipch|ivt|jar|jcp|jcp|jfm|jfm|jtx|jtx|lck|ldb|leveldb|lock|log|log1|log2|log3|map|mdb|mdmp|mmap|mo|mum|ncb|nwl|nwl|od|odlgz|old|otc|parc|part|partial|pb|pck|pdb|pem|pf|pid|pl|pm|pma|pri|pri|prm|prt|ptx|pyc|pyf|qm|qmlc|rcache|res|res2|resx|sav|scc|sdb|sdf|sds|sid|sin|so|spi1d|spj|sqlite|srd-shm|store|sublime-package|sublime_session|sublime-workspace|suo|suo2|swo|swp|sym|sys|tbres|tcl|tek|temp|thumb|thumbsdb|tjg|tlog|tmp|tmpfile|trc|vscdb|wbk|wer|wlog|woff|woff2|xbf|xin|xlf|zsh|~debris|~lock|~temp\" >\r\n\r\n!< ext:7z|ext:bat|ext:cmd|ext:csv|ext:doc|ext:docx|ext:exe|ext:git|ext:json|ext:mcr|ext:md|ext:mp3|ext:mp4|ext:ms|ext:nss|ext:pdf|ext:psd|ext:py|ext:rar|ext:rar|ext:scene|ext:url|ext:xls|ext:xlsx|ext:zip > < GUID:|HASH:|MD5:|SID: >\r\n\r\n!< ext:\"7z|bat|cmd|csv|doc|docx|exe|git|json|mcr|md|mp3|mp4|ms|nss|pdf|psd|py|rar|rar|scene|url|xls|xlsx|zip\" > < GUID:|HASH:|MD5:|SID: >\r\n\r\nC:\\Users\\<USER>\\AppData\\Local\\Sync.Config\\1.1\\\r\ngrp-sys-ex:\r\n    nohighlight:!< path:\"%WINDIR%\"|path:\"%PROGRAMFILES%\"|path:\"%PROGRAMFILES(X86)%\" >\r\n    !< (DIR: PATH:\"Windows Defender\")|(DIR: PATH:\"Atlassian/SourceTree\") >\r\n    C:/Users/<USER>/AppData/Local/Atlassian/SourceTree.exe_Url_j5vkpf2j20xmggvsrjftjqeu5dz1xsmx/********/user.config\r\n"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n(No open tabs)\n\n# Current Time\n4/3/2025, 6:37:01 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<plan_mode_respond>\n<response>\n# Codebase Analysis: \"Everything\" Search Tool Configuration & Patterns\n\nAfter examining the available files, I can see this is a collection of search patterns, configurations, and examples for the \"Everything\" search utility - a Windows file search tool known for its speed and flexibility.\n\n## Phase 1: Quick Orientation\n\n### Technology & Purpose\n- **Primary Tool**: Everything Search Engine (Windows file search utility)\n- **Languages**: Search query syntax specific to Everything, stored in Python-like files (`.py` extensions)\n- **Purpose**: A personal knowledge base of search patterns, regex expressions, and configurations for optimizing file searches\n- **Configuration**: Uses `.ini` files for Everything tool configuration\n\n### File Structure Overview\n- **gpt_interaction_notes.MD**: Collection of regex patterns for various search scenarios\n- **Notes_ALL.txt.py**: Extensive collection of search queries, file type filters, and grouped file patterns\n- **notes_ini.ini**: Configuration settings for the Everything search tool\n- **notes_search_syntax.cpp**: Reference documentation of Everything's search syntax\n- **debug-grp-search-ex.txt.py**: Grouped search expressions for excluding specific file types and locations\n- **evr_scratchpad.py**: Simple search examples, likely for quick testing\n- **search_ux.png**: (Unable to read - likely a screenshot of the UI or search results)\n\n## Phase 2: Architecture & Components\n\nThe codebase isn't a traditional application but rather a structured knowledge base with these key components:\n\n### 1. Search Filter Groups\nOrganized collections of exclusion filters:\n```\ngrp-paths-ex:     # Path exclusions (cache folders, system files)\ngrp-exts-ex:      # File extension exclusions (temp files, logs)\ngrp-parents-ex:   # Parent folder exclusions (.git, venv)\ngrp-names-ex:     # Filename exclusions \ngrp-strings-ex:   # String pattern exclusions\ngrp-meta-ex:      # Metadata exclusions (empty files, empty folders)\ngrp-regex-ex:     # Regex pattern exclusions\ngrp-patterns-ex:  # Special pattern exclusions\n```\n\n### 2. Regular Expression Patterns\nSpecialized regex patterns for finding:\n- UUIDs/GUIDs: `\\{?[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}?`\n- IP addresses: `(?:[0-9]{1,3}\\.){3}[0-9]{1,3}`\n- Dates in various formats\n- Email addresses\n- RAR parts: `^.*\\.r(?!00)\\d{2}$`\n- Long alphabetical strings: `^[a-z]{25,}|[A-Z]{25,}|[a-zA-Z]{25,}$`\n\n### 3. Search Command Templates\nCommon search patterns for specific scenarios:\n- Find recently modified files\n- Exclude system/temporary files\n- Find specific file types (video, audio, 3D models)\n- Find duplicate files\n- Column customization and sorting\n\n### 4. Configuration Settings\nEverything tool settings for:\n- UI behavior\n- Search behavior\n- Keyboard shortcuts\n- Context menu options\n- Font preferences\n\n## Phase 3: Analysis & Recommendations\n\n### Pattern & Practice Recognition\n- **Date Stamping**: Entries are often marked with timestamps (e.g., `# [2025.03.02 12:09]`)\n- **Grouping Strategy**: Related filters are grouped into logical categories\n- **Progressive Complexity**: Simple patterns build toward more complex compound searches\n- **Comments**: Examples typically include comments explaining their purpose\n\n### Quality Assessment\nThis is a personal reference system rather than an application, so traditional quality metrics don't apply. However:\n- Documentation is thorough for search syntax\n- Examples are numerous and cover many use cases\n- Organization could be improved in some files where examples accumulate\n\n### Recommendations\n\n1. **Structural Improvements:**\n   - Create a more hierarchical organization system for search patterns\n   - Implement consistent naming and formatting across files\n   - Consider consolidating similar examples\n\n2. **Documentation Enhancements:**\n   - Add more explanatory comments for complex search patterns\n   - Create a cross-reference index of search techniques\n   - Categorize examples by use case (e.g., cleanup, file finding, analysis)\n\n3. **Expansion Opportunities:**\n   - Create parameterized search macros for common tasks\n   - Add visualization examples using the column features\n   - Develop workflow examples showing sequences of searches\n\nWould you like me to focus on any specific aspect of this codebase in more detail? I could create diagrams of the filter relationships, analyze specific search patterns, or provide more detailed recommendations.\n</response>\n<options>\n[\"Analyze the regex patterns in more detail\", \"Explore the filter grouping system\", \"Create a diagram of the search component relationships\", \"Develop guidance for standardizing the search pattern structure\", \"Recommend a strategy for better organizing these search snippets\"]\n</options>\n</plan_mode_respond>"}]}, {"role": "user", "content": [{"type": "text", "text": "[plan_mode_respond] Result:"}, {"type": "text", "text": "<user_message>\nAnalyze the regex patterns in more detail\n</user_message>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n(No open tabs)\n\n# Current Time\n4/3/2025, 6:38:02 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<plan_mode_respond>\n<response>\n# Detailed Analysis of Regex Patterns in Everything Search Configuration\n\nAfter examining the files, I've identified several categories of regex patterns that serve specific searching purposes. Let me break these down by category, analyzing their structure, purpose, and implementation details.\n\n## 1. Identifier & Format Recognition Patterns\n\n### UUID/GUID Patterns\n```regex\n# Standard UUID/GUID (with optional braces)\n\\{?[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}?\n\n# CLSID (with braces)\n\\{[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}\n\n# Windows SID format\n^S-1-5-21-\\d+-\\d+-\\d+-\\d+$\n\n# Simple hex pair pattern\n^[A-F0-9]{8}_[A-F0-9]{8}$\n\n# 32-character MD5/Hash\n[0-9A-Fa-f]{32}\n\\b[0-9A-Fa-f]{32}\\b  # Word boundary version\n```\n\n**Analysis:**\n- These patterns precisely match standard identifiers used in Windows and general computing\n- The optional braces `\\{?...\\}?` in the UUID pattern allows flexibility in matching formats\n- Word boundary versions (`\\b`) help avoid partial matches within longer strings\n- The SID pattern is highly specific to Windows security identifiers\n\n### Network & Communication Patterns\n```regex\n# IPv4 address (various versions)\n(?:[0-9]{1,3}\\.){3}[0-9]{1,3}         # Basic pattern\n\\b(?:[0-9]{1,3}\\.){3}[0-9]{1,3}\\b     # With word boundaries\n^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$       # Exact match only\n\n# Email address\n[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}\n```\n\n**Analysis:**\n- The IPv4 pattern uses a non-capturing group `(?:...)` with repetition `{3}` for efficiency\n- The email regex is moderately permissive but covers standard formats\n- The IP pattern doesn't validate numeric ranges (would allow 999.999.999.999)\n\n### Date Format Patterns\n```regex\n# DD.MM.YYYY format (e.g., 24.12.2023)\n(?:0[1-9]|[12][0-9]|3[01])\\.(?:0[1-9]|1[0-2])\\.\\d{4}\n\n# MM.DD.YYYY format (e.g., 12.24.2023)\n(?:0[1-9]|1[0-2])\\.(?:0[1-9]|[12][0-9]|3[01])\\.\\d{4}\n\n# YYYY.MM.DD format\n\\d{4}\\.(?:0[1-9]|1[0-2])\\.(?:0[1-9]|[12][0-9]|3[01])\n```\n\n**Analysis:**\n- These patterns properly validate date ranges (months 01-12, days 01-31)\n- Uses non-capturing groups for performance\n- Could be improved by further constraining day ranges based on month (30 days in some months, 28/29 in February)\n\n## 2. File System & Path Patterns\n\n### Path Structure Patterns\n```regex\n# Paths with at least 5 backslashes (directory levels)\n^(?:[^\\\\]*\\\\){5,}[^\\\\]*$\n\n# Extract fourth path segment\n(?:(?:^|\\\\)[^\\\\]*){4}\n\n# File with numbered extension\n^.*\\.r(?!00)\\d{2}$                  # RAR parts (excludes .r00)\nREGEX:EXTENSION:\"^\\d+$\"             # Any numeric extension\nREGEX:EXTENSION:\"^\\d{3,}$\"          # Numeric extension with 3+ digits\n```\n\n**Analysis:**\n- Path segment extraction uses a sophisticated pattern with alternation and repetition\n- The negative lookahead `(?!00)` in the RAR pattern shows advanced regex usage\n- These patterns demonstrate adaptation to the Windows file system's backslash separator\n\n### Advanced File Selection Patterns\n```regex\n# Folder name ending with digits 0-99 (e.g. \"Profile 2\")\nREGEX:ANCESTOR-NAME:\"\\s[0-9]{1,2}(?!\\d)$\"\n\n# Matches strings with 5+ underscores\n^(?:[^_]*_){5,}[^_]*$\n\n# Strings composed entirely of underscores\n^(\\_+)$\n```\n\n**Analysis:**\n- The ancestor-name pattern uses negative lookahead `(?!\\d)` to ensure no more digits follow\n- The underscore patterns demonstrate counting repetitions with specific character exclusions\n- These patterns show application of regex for structural analysis of filenames\n\n## 3. Content Filtering Patterns\n\n### Exclusion Patterns\n```regex\n# Long alphabet strings (likely machine-generated)\n^[a-z]{25,}|[A-Z]{25,}|[a-zA-Z]{25,}$\n\n# Specific format with hexadecimal\n^[a-z]{32}_[0-9]+\\.[a-f0-9]{64}$\n\n# URL pattern (wrapped in groups for extraction)\n\\b((https?|ftp|file):\\/\\/[-A-Z0-9+&@#\\/%?=~_|!:,.;]*[-A-Z0-9+&@#\\/%=~_|])\n```\n\n**Analysis:**\n- These patterns target machine-generated filenames for exclusion\n- The URL pattern is comprehensive but uses a broader character class than strictly necessary\n- These patterns demonstrate application for noise reduction in search results\n\n## 4. Pattern Implementation Details\n\n### Search Syntax Integration\nThe regex patterns are implemented with several modifiers within Everything's search syntax:\n\n```\nREGEX:path:pattern     # Apply to file paths\nREGEX:name:pattern     # Apply to filenames\nREGEX:extension:pattern # Apply to extensions\nREGEX:content:pattern  # Search file contents\n```\n\nMany patterns are wrapped in search commands:\n```\n!FILTER: REGEX:pattern    # Exclude matches\ncolumn1:=REGEX_EXTRACT($path:,pattern)  # Extract to column\n```\n\n### Pattern Composition Techniques\n\n1. **Pattern Chaining:**\n```\nnohighlight:!< (!ext:\"ms|py|nss|bat|cmd|mcr|mse\" path:\"Cache\")|(!ext:\"py|json|config\" path:\"Atlassian/SourceTree\") >\n```\n\n2. **Multi-Stage Exclusions:**\n```\ngrp-paths-ex: grp-exts-ex: grp-parents-ex: grp-names-ex: grp-strings-ex: grp-meta-ex: grp-regex-ex: grp-patterns-ex:\n```\n\n3. **Column Manipulation:**\n```\ncolumn1:=len($regex:path:(\\d):)  add-column:column1\n```\n\n## 5. Advanced Pattern Analysis\n\n### Strengths\n- **Precision**: Most patterns are crafted for exact matches with specific formats\n- **Performance**: Use of non-capturing groups and targeted character classes optimizes matching\n- **Flexibility**: Patterns often include variations (with/without word boundaries, etc.)\n\n### Potential Improvements\n1. **Validation**: The IPv4 pattern could be improved to validate numeric ranges (0-255)\n2. **Documentation**: Many complex patterns lack inline explanations\n3. **Consistency**: Different styles and approaches are used across patterns\n4. **Modularity**: Some patterns could be broken into reusable components\n5. **Date Validation**: Date patterns could be more restrictive regarding valid days per month\n\n### Pattern Evolution Trend\nLooking at the file timestamps and pattern complexity, there appears to be an evolution:\n1. Earlier patterns focus on simple file filtering\n2. Middle-stage development added specialized format recognition\n3. Latest patterns show sophisticated grouping and composition techniques\n\n## 6. Diagram: Regex Pattern Relationships\n\n```mermaid\ngraph TD\n    A[Root Search Query] --> B[Inclusion Patterns]\n    A --> C[Exclusion Patterns]\n    \n    B --> D[Format Recognition]\n    B --> E[Content Patterns]\n    \n    C --> F[System Exclusions]\n    C --> G[Format Exclusions]\n    \n    F --> H[grp-paths-ex]\n    F --> I[grp-parents-ex]\n    \n    G --> J[grp-exts-ex]\n    G --> K[grp-regex-ex]\n    G --> L[grp-meta-ex]\n    \n    D --> M[UUIDs/GUIDs]\n    D --> N[IP Addresses]\n    D --> O[Dates]\n    D --> P[Email Formats]\n    \n    E --> Q[File Content]\n    E --> R[URL Patterns]\n    \n    classDef standard fill:#f9f,stroke:#333,stroke-width:2px;\n    classDef exclusion fill:#ff9,stroke:#333,stroke-width:2px;\n    \n    class F,G,H,I,J,K,L exclusion;\n    class D,E,M,N,O,P,Q,R standard;\n```\n\nWould you like me to dive deeper into any specific category of regex patterns or explain the implementation details of any particular pattern?\n</response>\n<options>\n[\"Analyze the exclusion patterns and their grouping system\", \"Examine the date and time pattern variations and validation\", \"Explore the path manipulation patterns in more depth\", \"Analyze the column manipulation and extraction techniques\", \"Review implementation strategies for these patterns in search workflows\"]\n</options>\n</plan_mode_respond>"}]}, {"role": "user", "content": [{"type": "text", "text": "[plan_mode_respond] Result:"}, {"type": "text", "text": "<user_message>\nExplore the path manipulation patterns in more depth\n</user_message>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n../../../../../AppData/Local/Temp/temp_image_1743698344791.png\n\n# Current Time\n4/3/2025, 6:39:26 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<plan_mode_respond>\n<response>\n# Deep Dive: Path Manipulation Patterns in Everything Search\n\nPath manipulation is a critical aspect of the Everything search utility's power. The patterns in this codebase reveal sophisticated techniques for navigating, filtering, and extracting information from file system paths. Let's explore these in depth:\n\n## 1. Path Structure Analysis Patterns\n\n### Directory Depth Patterns\n```regex\n# Paths with exactly 5+ backslashes (directory levels)\n^(?:[^\\\\]*\\\\){5,}[^\\\\]*$\n\n# Files exactly N levels deep\ndepth:6                    # Exact depth match\ndepth:>11                  # Greater than depth\ndepth:<6                   # Less than depth\nparents:<8                 # Maximum number of parents until root\n```\n\n**Technical Analysis:**\n- The regex `^(?:[^\\\\]*\\\\){5,}[^\\\\]*$` uses a non-capturing group with quantifier to count path separators:\n  - `^` anchors to start of string\n  - `(?:[^\\\\]*\\\\)` matches any characters except backslash, followed by a backslash\n  - `{5,}` requires 5 or more repetitions of this pattern\n  - `[^\\\\]*$` matches remaining non-backslash characters to end of string\n\nThis pattern precisely counts directory levels while handling variations in directory name lengths.\n\n### Path Segment Extraction\n```regex\n# Extract the fourth segment from a file path\naddcolumn:a a:=regexextract($path:,\"(?:(?:^|\\\\)[^\\\\]*){4}\")\n\n# Extract folder with child file having specific extension\ndir: child-file:ext:mp3\n\n# Extract path part (parent folder)\npathpart:\"c:\\windows\\system32\"  # Returns \"c:\\windows\"\n```\n\n**Implementation Details:**\n- The segment extraction pattern `(?:(?:^|\\\\)[^\\\\]*){4}` is particularly clever:\n  - `(?:^|\\\\)` matches either start of string or backslash\n  - `[^\\\\]*` then matches characters until next backslash\n  - `{4}` repeats this 4 times to get the 4th segment\n  - This handles both absolute and relative paths gracefully\n\n## 2. Path Filtering Techniques\n\n### Negative Path Filtering (Exclusions)\n```\n# Multiple path exclusions using OR operator\nnohighlight:!< (!ext:\"ms|py|nss|bat|cmd|mcr|mse\" path:\"Cache\")|\n             (!ext:\"py|json|config\" path:\"Atlassian/SourceTree\")|\n             (path:\"Windows Defender\")|\n             path:\"%WINDIR%\"|\n             path:\".dropbox.cache\"|\n             path:\"appcompat\" >\n\n# Excluding specific user directories\n!path:\"%WINDIR%\" !path:\"%PROGRAMFILES%\" !path:\"%PROGRAMFILES(X86)%\" !path:\".GIT\"\n```\n\n**Analysis:**\n- These patterns use environment variables (e.g., `%WINDIR%`) for portable exclusions\n- The `nohighlight:!<` prefix prevents highlighting the excluded results\n- Complex conditions combine path locations with file type filters\n- Parentheses allow logical grouping of complex conditions\n\n### Path Inclusion Patterns\n```\n# Specific folder searches with depth limits\nPATH:\"%USERPROFILE%\\Desktop\" DEPTH:4\nPATH:\"C:\\Users\\<USER>\\Desktop\" DEPTH:4\n\n# Multi-folder OR search with disambiguation\n\"D:\\Dokumenter O.l\\Documens\\Privat\\Dokumenter\\Personlig\\MobilBackup\\2023.05.30_YVONNE\" | \n\"D:\\Dokumenter O.l\\Documens\\Privat\\Dokumenter\\Personlig\\MobilBackup\\2023.01.10_YVONNE - MOVED\"\n\n# Parent-child relationship filters\nparent:\"<path>\"           # Files directly in specified path (no subfolders)\nchild:\"<filename>\"        # Folders containing a file with specified name\nancestor:\"C:\\Program Files\"  # Files within specified path hierarchy\n```\n\n**Technical Detail:**\n- The parent-child filters create hierarchical relationships:\n  - `parent:` restricts to immediate children only (no recursion)\n  - `child:` matches folders containing specified files (parent→child relationship)\n  - `ancestor:` matches any descendant in a directory tree (ancestor→descendant)\n\n## 3. Path Component Manipulation\n\n### Path Part Extraction\n```\n# Extract path components with regex capturing groups\ncolumn1:=REGEX_EXTRACT($name:,\"^.exclude.*\")           # Extract matching part of filename\ncolumn1:=LEN(REGEX_EXTRACT($name:,\"^.exclude.*\"))      # Get length of match\ncolumn1:=(path:)                                       # Extract full path\nfolder:REGEX:^(.*)$ fileexists:\\1.zip                  # Check if .zip exists with same name\n```\n\n**Implementation Details:**\n- The `\\1` backreference refers to the first captured group from the previous regex\n- These patterns enable complex relationships between paths and names\n- Capturing groups often used to create dynamic references to other files/paths\n\n### Path-Based Calculations\n```\n# Calculate using path depth\ncolumn1:=len($depth:)                     # Get path depth length\ncolumn1:=len($regex:path:(\\d):)           # Count digits in path\ncolumn1:=len(sibling-file-count:*.txt)    # Count .txt files in same folder\n```\n\n**Technical Analysis:**\n- These use path properties as inputs to calculations\n- The `len(sibling-file-count:*.txt)` pattern is particularly useful:\n  - Counts files in same directory matching a pattern\n  - Can be used to identify folders with specific content distributions\n\n## 4. Advanced Hierarchical Path Relationships\n\n### Multiple Level Constraints\n```\n# Control recursion depth in searches\ndepth:6 max-child-depth:>=2              # Search to depth 6, requiring at least 2 child levels\ndir: path:\"D:\\Simulator Media\" depth:2 max-child-depth:1  # Limit both ways\n\n# File exists in specific related location\nREGEX:^(.*)\\\\([^\\\\]*)$ fileexists:\\1\\\\backup\\\\\\2  # Check if file exists in backup subfolder\n```\n\n**Implementation Details:**\n- The backup subfolder pattern uses two capture groups:\n  - `(.*)` captures the parent path\n  - `([^\\\\]*)` captures the filename\n  - `\\1\\\\backup\\\\\\2` references those to construct the backup path\n- This enables verification of backup file existence without hardcoding paths\n\n### Parent-Child Size Relationships\n```\n# Sort by folder size\nsort:parent-size;path;folder-depth-ascending;size  # Parent folder size as primary sort\n\n# Filter by parent folder size\nparent-size:>1GB parent-size:<30GB !appdata: file:  # Find large parent folders\n\n# Child file count from disk\nempty: child-file-count-from-disk:0 child-folder-count-from-disk:0 !attrib:L  # True empty folders\n```\n\n**Technical Analysis:**\n- These patterns allow filesystem analysis based on hierarchical relationships\n- The `parent-size` and `child-file-count` operators provide metadata about surrounding files\n- The `!attrib:L` excludes reparse points (shortcuts, junctions) for accurate empty folder detection\n\n## 5. Path Pattern Modularization and Reuse\n\n### Grouped Path Exclusions\n```\ngrp-paths-ex:\n    nohighlight:!< (!ext:\"ms|py|nss|bat|cmd|mcr|mse\" path:\"Cache\")|\n                 (!ext:\"py|json|config\" path:\"Atlassian/SourceTree\")|\n                 (!ext:\"py|json|config\" path:\"Sync.Config\")|\n                 path:\"Windows Defender\"|\n                 path:\"%WINDIR%\"|\n                 /* more paths... */ >\n```\n\n**Key Observation:**\n- These groups create reusable path filters that can be combined\n- The `grp-paths-ex:` pattern becomes a shorthand for the complex exclusions\n- Multiple groups can be chained: `grp-paths-ex: grp-exts-ex: grp-parents-ex:`\n- This approach enables modular, maintainable search patterns\n\n### Path Macros\n```\n# \"ii:\" inclusion macro \nii:\n    < EXT:\"3dm|3ds|3g2|3gp|3gp2|3gpp|aac|abc|ac3|ai|aif|aifc|aiff|amr|amv|ape|asf|asx|au|avi|/* more extensions... */\" OR REGEX:EXTENSION:\"^SUBLIME.*\" >\n\n# \"ee:\" exclusion macro\nee:\n    !< size:<24bytes >\n    !< ext:\"vscdb|jcp|jtx|nwl|backup|edb|jfm|hxd|hds|db3|pri\" >\n    !< case:exact:name:\"Local\"|\"globalStorage\"|\"Auto Save Session.sublime_session\" >\n    /* more exclusions... */\n```\n\n**Implementation Strategy:**\n- These act as search macros that can be invoked with a simple prefix\n- The `ii:` (include) and `ee:` (exclude extra) provide complementary filtering\n- This creates a standardized search vocabulary specific to the user's needs\n\n## 6. Path Manipulation Diagram\n\n```mermaid\ngraph TB\n    subgraph \"Path Structure Analysis\"\n        A[Directory Depth<br>^(?:[^\\\\]*\\\\){5,}[^\\\\]*$] --> B[Exact Depth<br>depth:N]\n        A --> C[Relative Depth<br>depth:>N, depth:<N]\n        A --> D[Parent Count<br>parents:<N]\n    end\n    \n    subgraph \"Path Filtering\"\n        E[Exclusion<br>!path:pattern] --> F[System Exclusions<br>%WINDIR%, etc.]\n        E --> G[Cache Exclusions<br>.cache, temp]\n        H[Inclusion<br>path:pattern] --> I[Specific Folders]\n        H --> J[Wildcard Paths<br>PATH:\"C:\\\\Users\\\\<USER>\\\\Desktop\"]\n    end\n    \n    subgraph \"Hierarchical Relationships\"\n        K[Parent-Child] --> L[parent:<path>]\n        K --> M[child:<filename>]\n        K --> N[ancestor:<path>]\n        O[Size Relationships] --> P[parent-size:>size]\n        O --> Q[child-count:<count>]\n    end\n    \n    subgraph \"Path Component Extraction\"\n        R[Segment Extraction<br>(?:(?:^|\\\\)[^\\\\]*){n}] --> S[Column Functions<br>column1:=regexextract(...)]\n        R --> T[Path Part Operations<br>pathpart:path]\n    end\n    \n    Z[Master Search Pattern] --> E\n    Z --> H\n    Z --> K\n    Z --> O\n    Z --> R\n    \n    classDef analysis fill:#f9d,stroke:#333,stroke-width:1px;\n    classDef filtering fill:#9df,stroke:#333,stroke-width:1px;\n    classDef hierarchy fill:#fd9,stroke:#333,stroke-width:1px;\n    classDef extraction fill:#9fd,stroke:#333,stroke-width:1px;\n    \n    class A,B,C,D analysis;\n    class E,F,G,H,I,J filtering;\n    class K,L,M,N,O,P,Q hierarchy;\n    class R,S,T extraction;\n```\n\n## 7. Optimization Considerations\n\n1. **Backslash Handling**: Many patterns account for Windows path separators (`\\`), which need double escaping in regex (`\\\\`)\n\n2. **Performance vs. Precision**: More precise patterns (like full regex path matching) can be slower than using Everything's built-in operators (`path:`, `parent:`)\n\n3. **Macro Modularity**: The grouped patterns provide reusability but add complexity - there's a trade-off between convenience and readability\n\n4. **Environment Variables**: Using `%VARIABLE%` makes patterns portable across machines but requires expansion support (`expand_environment_variables2=1` in INI)\n\nAre there specific aspects of these path manipulation patterns you'd like me to explore further, such as practical examples of applying them to specific search scenarios?\n</response>\n<options>\n[\"Analyze practical examples of these patterns in real-world search scenarios\", \"Explore the parent-child relationship filters in more depth\", \"Examine the search grouping strategies for combining path patterns\", \"Look at performance considerations for complex path searches\", \"Review path-based sorting techniques\"]\n</options>\n</plan_mode_respond>"}]}, {"role": "user", "content": [{"type": "text", "text": "[plan_mode_respond] Result:"}, {"type": "text", "text": "<user_message>\nutilize `browser-tools` mcp to verify patterns against voidtools everything\n</user_message>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n../../../../../AppData/Local/Temp/temp_image_1743698344791.png\n\n# Current Time\n4/3/2025, 6:41:13 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<plan_mode_respond>\n<response>\nI understand you'd like me to verify these search patterns against the actual Everything search utility from voidtools. This would indeed be valuable to confirm how these patterns work in practice.\n\nHowever, there are a couple of limitations I need to explain:\n\n1. I don't currently have access to a \"browser-tools\" MCP server in my available tools. The MCP servers I can currently access are:\n   - github.com/modelcontextprotocol/servers/tree/main/src/filesystem\n   - github.com/alexander-zuev/supabase-mcp-server\n   - github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking\n\n2. We're currently in PLAN MODE, which means I can't access tools like `browser_action` that would allow me to interact with websites or desktop applications to verify these patterns.\n\nTo proceed with verifying these patterns against the Everything search utility, we have two options:\n\n1. You could toggle to ACT MODE (by clicking the toggle button below), which would allow me to use the `browser_action` tool to navigate to the Everything search utility's website or documentation and verify patterns.\n\n2. Alternatively, you could share specific patterns you'd like me to explain or analyze in more depth based on the documentation we've already reviewed. I could also propose test cases for certain patterns that you could verify in the Everything utility on your system.\n\nWhich approach would you prefer to take?\n</response>\n</plan_mode_respond>"}]}, {"role": "user", "content": [{"type": "text", "text": "[plan_mode_respond] Result:"}, {"type": "text", "text": "<user_message>\nanalyze the newly added bookmarks_reference.csv\n</user_message>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n../../../../../AppData/Local/Temp/temp_image_1743698344791.png\n\n# Current Time\n4/3/2025, 6:43:26 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<read_file>\n<path>bookmarks_reference.csv</path>\n</read_file>\n\n[Response interrupted by a tool use result.\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}]}, {"role": "user", "content": [{"type": "text", "text": "[read_file for 'bookmarks_reference.csv'] Result:"}, {"type": "text", "text": "The tool execution failed with the following error:\n<error>\nError reading file: {\"name\":\"Error\",\"message\":\"File not found: c:\\\\Users\\\\<USER>\\\\Desktop\\\\SCRATCH\\\\app_everything2\\\\user\\\\_notes\\\\bookmarks_reference.csv\",\"stack\":\"Error: File not found: c:\\\\Users\\\\<USER>\\\\Desktop\\\\SCRATCH\\\\app_everything2\\\\user\\\\_notes\\\\bookmarks_reference.csv\\n\\tat fle (c:\\\\Users\\\\<USER>\\\\Desktop\\\\User\\\\Nas_Flow_Jorn\\\\__GOTO__\\\\Apps\\\\app_vscode\\\\exe\\\\data\\\\extensions\\\\saoudrizwan.claude-dev-3.8.6\\\\dist\\\\extension.js:1183:7504)\\n\\tat async zhe.presentAssistantMessage (c:\\\\Users\\\\<USER>\\\\Desktop\\\\User\\\\Nas_Flow_Jorn\\\\__GOTO__\\\\Apps\\\\app_vscode\\\\exe\\\\data\\\\extensions\\\\saoudrizwan.claude-dev-3.8.6\\\\dist\\\\extension.js:2585:4119)\"}\n</error>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n../../../../../AppData/Local/Temp/temp_image_1743698344791.png\n\n# Current Time\n4/3/2025, 6:43:32 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<plan_mode_respond>\n<response>\nI attempted to read the file `bookmarks_reference.csv`, but it doesn't exist in the current directory (`c:/Users/<USER>/Desktop/SCRATCH/app_everything2/user/_notes`). \n\nLooking at the available files in this directory, I don't see a file with that exact name. However, I do notice a file called `notes_exclusions.py.csv` which might contain similar information to what you're looking for.\n\nWould you like me to:\n\n1. Analyze `notes_exclusions.py.csv` instead?\n2. Check for bookmark patterns in the other files we've already examined?\n3. Look for other CSV files that might contain bookmark information?\n4. Continue with a different aspect of the analysis?\n</response>\n<options>\n[\"Analyze notes_exclusions.py.csv instead\", \"Check for bookmark patterns in other files\", \"Continue with path manipulation pattern analysis\", \"Create a comprehensive report on all findings so far\"]\n</options>\n</plan_mode_respond>"}]}, {"role": "user", "content": [{"type": "text", "text": "[plan_mode_respond] Result:"}, {"type": "text", "text": "<user_message>\nanalyze the newly added bookmarks_reference.csv\n</user_message>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n(No visible files)\n\n# VSCode Open Tabs\n../../../../../AppData/Local/Temp/temp_image_1743698344791.png\n\n# Current Time\n4/3/2025, 6:44:05 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<plan_mode_respond>\n<response>\nI've attempted to access the file `bookmarks_reference.csv`, but it appears this file doesn't exist in the current working directory (`c:/Users/<USER>/Desktop/SCRATCH/app_everything2/user/_notes`).\n\nFrom the files listed in your directory, I can see:\n- debug-grp-search-ex.txt.py\n- discarded_snippets.py\n- Everything.Unicode.Tables.txt\n- evr_scratchpad.py\n- gpt_interaction_notes.MD\n- Notes_ALL.txt.py\n- notes_column_snippets.py\n- notes_exclusions.py.csv\n- notes_ini.ini\n- notes_regex_snippets.py\n- notes_search_syntax.cpp\n- notes_unsorted_snippets.py\n- play.py\n- search_ux.png\n\nThere is a CSV file called `notes_exclusions.py.csv` which might contain bookmark-related information. Would you like me to analyze that file instead? Or perhaps you intended to create a new file called `bookmarks_reference.csv` for organizing the bookmark patterns we've been discussing?\n\nAlternatively, I could check for bookmark-related patterns in the existing files we've already examined. Many of these files contain search patterns and configurations that might include bookmark references.\n</response>\n<options>\n[\"Analyze notes_exclusions.py.csv instead\", \"Check for bookmark patterns in the existing files\", \"Create a suggested structure for a new bookmarks_reference.csv file\", \"Continue with our analysis of search patterns without focusing on bookmarks\"]\n</options>\n</plan_mode_respond>"}]}, {"role": "user", "content": [{"type": "text", "text": "[plan_mode_respond] Result:"}, {"type": "text", "text": "<user_message>\nyou need to actually re-check, it is added and open\n</user_message>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\nbookmarks_reference.csv\n\n# VSCode Open Tabs\nbookmarks_reference.csv\n\n# Current Time\n4/3/2025, 6:44:48 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<read_file>\n<path>bookmarks_reference.csv</path>\n</read_file>\n\n[Response interrupted by a tool use result. Only one tool\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]"}]}, {"role": "user", "content": [{"type": "text", "text": "[read_file for 'bookmarks_reference.csv'] Result:"}, {"type": "text", "text": "\"Bookmarks\"\t1\t\"\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"【  DATEPATTERNS:  】\t:: Datepatterns (example: YYYY.MM.DD)\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"REGEX:\"\"((19\\d{2}|20\\d{2})([\\.\\-\\\\/ ]?)(0[1-9]|1[0-2])\\3(0[1-9]|[12][0-9]|3[01]))|((0[1-9]|[12][0-9]|3[01])([\\.\\-\\\\/ ]?)(0[1-9]|1[0-2])\\8(19\\d{2}|20\\d{2}))\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"datepatterns\"\t\t\"\"\r\n\"Environments\"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"【  env-priv:  】 Env: PRIV\t:: !exact:ancestor-name:\"\"_JOBB\"\"\"\t0\t\"Bookmarks/Environments\"\t\t\t\t\t\t\t\t\t\t\"!exact:ancestor-name:\"\"_JOBB\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"env-priv\"\t\t\"\"\r\n\"【  env-jobb:  】 Env: JOBB\t:: exact:ancestor-name:\"\"_JOBB\"\"\"\t0\t\"Bookmarks/Environments\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:\"\"_JOBB\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"env-jobb\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks\"\r\n\"🠊 Search Reference \t(Examples)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"Parent / Ancestors\t(Files/Folders)    \"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Parent Size)\tparent-size:>1GB\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Parent%20%2F%20Ancestors%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"parent-size:>1GB\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Exact Parent Folder Name)\texact:ancestor-name:venv\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Parent%20%2F%20Ancestors%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:\"\"venv\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"Children / Descendants\t(Folders)    \"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Find Empty Folders #1)\tchild-count:<1 OR (dir: AND size:0bytes)\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"child-count:<\"\"1\"\" OR (dir: AND size:\"\"0bytes\"\")\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Find Empty Folders #2)\tdescendantcount:0\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"descendantcount:0\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Exclude Empty Folders)\t!(dir: AND size:<24bytes) AND !(child-count:0)\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!(dir: AND size:<\"\"24bytes\"\") AND !(child-count:\"\"0\"\")  \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Folders With +10000 Descendants)\tdescendant-count:>10000\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"descendant-count:>\"\"10000\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Folders With +300 Children)\tchild-count:>300\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"child-count:>\"\"300\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\r\n\"🠆 (Folder Contains Specific Name)\texact:child-name:.git\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:child-name:\"\".git\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Child Folder Names)\texact:child-folder-name:Lib\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:child-folder-name:\"\"Lib\"\" AND exact:child-folder-name:\"\"Include\"\" AND exact:child-folder-name:\"\"Scripts\"\" \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\r\n\"🠆 (Folders Contains Extension)\tchild-file:ext:mp3\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"child-file:ext:\"\"mp3\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\r\n\"🠆 (Folders Changed This Month)\tchild-date-run:month|child-date-modified:month\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"child-date-run:\"\"month\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Folders Changed This Week)\tchild-date-run:week|child-date-modified:week\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"child-date-run:\"\"week\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Folders Used Today)\tchild-date-run:today\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"child-date-run:\"\"today\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Folders Changed Today)\tchild-date-run:today|child-date-modified:today\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Children%20%2F%20Descendants%09%28Folders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"child-date-run:\"\"today\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"Regex\t(Files/Folders)    \"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Extension Contains)\tREGEX:EXTENSION:^sublime.*\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Regex%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"REGEX:EXTENSION:\"\"^sublime.*\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"Sublime\"\t\t\"\"\r\n\"🠆 (Extension Character Count: 1)\tREGEX:EXTENSION:\\b[a-zA-Z0-9]{1,1}\\b\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Regex%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"REGEX:EXTENSION:\"\"\\b[a-zA-Z0-9]{1,1}\\b\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Extension Character Count < 5)\tREGEX:EXTENSION:\\b[a-zA-Z0-9]{1,5}\\b\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Regex%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"REGEX:EXTENSION:\"\"\\b[a-zA-Z0-9]{1,5}\\b\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Regex%09%28Files%2FFolders%29%20%20%20%20\"\r\n\"🠆 (Match Without Highlight)\tNOHIGHLIGHT:REGEX:EXTENSION:^sublime.*\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Regex%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"NOHIGHLIGHT:REGEX:EXTENSION:\"\"^sublime.*\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Match With Highlight)\tHIGHLIGHT:REGEX:EXTENSION:^sublime.*\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Regex%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"HIGHLIGHT:REGEX:EXTENSION:\"\"^sublime.*\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Regex%09%28Files%2FFolders%29%20%20%20%20\"\r\n\"🠆 (Sort by Column)\tregex:\\.(pdf)$ sort:regmatch1-descending;name-ascending\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Regex%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"regex:\\.(\"\"pdf\"\")$ | nohighlight:regex:\"\".*\"\" sort:\"\"regmatch1-descending;name-ascending\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Regex%09%28Files%2FFolders%29%20%20%20%20\"\r\n\"🠆 (Date Pattern)\tREGEX:(?:0[1-9]|[12][0-9]|3[01])\\.(?:0[1-9]|1[0-2])\\.\\d{4}\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Regex%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"REGEX:\"\"(?:0[1-9]|[12][0-9]|3[01])\\.(?:0[1-9]|1[0-2])\\.\\d{4}\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Content: Date Pattern)\tREGEX:CONTENT:(?:0[1-9]|[12][0-9]|3[01])\\.(?:0[1-9]|1[0-2])\\.\\d{4}\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Regex%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"EXT:TXT REGEX:CONTENT:\"\"(?:0[1-9]|[12][0-9]|3[01])\\.(?:0[1-9]|1[0-2])\\.\\d{4}\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Content: Time Pattern)\tREGEX:CONTENT:Kl\\.(?:[01][0-9]|2[0-3]):[0-5][0-9]\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Regex%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"REGEX:CONTENT:\"\"Kl\\.(?:[01][0-9]|2[0-3]):[0-5][0-9]\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Content: URL Pattern)\tTEXT:REGEX:CONTENT:\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Regex%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"TEXT: REGEX:CONTENT:\"\"\\b((https?|ftp|file):\\/\\/[-A-Z0-9+&@#\\/%?=~_|!:,.;]*[-A-Z0-9+&@#\\/%=~_|])\"\" column1:=regmatch0: columns:dm;size;ext;name;column1;path;name-frequency;extension-frequency;run-count; \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"Exclusions\t(Files/Folders)    \"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Exclude Empty Folders)\t!(dir: AND size:<24bytes) AND !(child-count:0)\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Exclusions%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!(dir: AND size:<\"\"24bytes\"\") AND !(child-count:\"\"0\"\")  \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Exclude Empty files)\t!(!folder: AND size:<24bytes)\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Exclusions%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!(!folder: AND size:<24bytes)\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"Duplicates\"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Files With Duplicate Content)\text:txt dupe:size;sha256\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Duplicates\"\t\t\t\t\t\t\t\t\t\t\"ext:txt dupe:size;sha256\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Duplicates)\t\"\"INSERT_SEARCH_HERE\"\" dupe:size;sha256\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Duplicates\"\t\t\t\t\t\t\t\t\t\t\"\"\"INSERT_SEARCH_HERE\"\" dupe:size;sha256 \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Find Duplicates Between Two Folders)\t\"\"path1\"\" \"\"path2\"\" dupe:size;sha256\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Duplicates\"\t\t\t\t\t\t\t\t\t\t\"\"\"C:\\Users\\<USER>\\Desktop\\PRJ\\GIT_BLENDER\"\" | \"\"C:\\Users\\<USER>\\Desktop\\PRJ\\GIT_WORKFLOW\"\" dupe:size;sha256\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Presort to use Before Finding Duplicates)\tdupe-sort:dm: ext:txt dupe:size;sha256 \"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Duplicates\"\t\t\t\t\t\t\t\t\t\t\"dupe-sort:dm: ext:txt dupe:size;sha256 \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"Sorting\"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Sort by Multiple)\tsort:dm;path;ext;name;size;\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\t\t\t\t\t\t\t\t\t\t\"sort:\"\"dm;path;ext;name;size;\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\r\n\"🠆 (Sort by Largest Size)\tsort:size-descending\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\t\t\t\t\t\t\t\t\t\t\"sort:size-descending\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\r\n\"🠆 (Sort and Secondary Sort)\tsecondary-sort:dm secondary-sort-descending:ext\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\t\t\t\t\t\t\t\t\t\t\"secondary-sort:\"\"dm\"\" secondary-sort-descending:\"\"ext\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\r\n\"🠆 (Sort Folders First)\tsort:isfolder-ascending\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\t\t\t\t\t\t\t\t\t\t\"sort:\"\"isfolder-ascending\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Sort Mixed Files/Folders)\tsort:!isfolder\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\t\t\t\t\t\t\t\t\t\t\"sort:\"\"!isfolder\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\r\n\"🠆 (Sort-Column: Filename/basename Characters)\tcolumn1:=(stem-length:) add-column:column1\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\t\t\t\t\t\t\t\t\t\t\"column1:=(stem-length:) add-column:column1\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\r\n\"🠆 (Sort by Characters)\text:pdf sort:chars:\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\t\t\t\t\t\t\t\t\t\t\"ext:\"\"pdf\"\" sort:chars:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Sort by Stem Length)\text:pdf sort:stem-length\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\t\t\t\t\t\t\t\t\t\t\"ext:pdf sort:\"\"stem-length\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Sort by Path Length)\tsort:full-path-len-ascending\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\t\t\t\t\t\t\t\t\t\t\"sort:full-path-len-ascending\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\r\n\"🠆 (Sort by Column)\tregex:\\.(pdf)$ sort:regmatch1-descending;name-ascending\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\t\t\t\t\t\t\t\t\t\t\"regex:\\.(\"\"pdf\"\")$ | nohighlight:regex:\"\".*\"\" sort:\"\"regmatch1-descending;name-ascending\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\r\n\"🠆 (Sort by Child Count)\tfolder: addcolumn:child-file-count sort:child-file-count\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\t\t\t\t\t\t\t\t\t\t\"folder: addcolumn:child-file-count sort:child-file-count\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Sort by Descentant Count)\tfolder: addcolumn:descendant-count sort:descendant-count\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\t\t\t\t\t\t\t\t\t\t\"folder: addcolumn:descendant-count sort:descendant-count\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Sort by Child & Descentant Count)\tfolder: addcolumn:child-file-count;descendant-count sort:child-file-count;descendant-count\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Sorting\"\t\t\t\t\t\t\t\t\t\t\"folder: addcolumn:child-file-count;descendant-count sort:child-file-count;descendant-count\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\r\n\"Formulas\"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Second letter of name is '_')\t$name:[1]=='_' \"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Formulas\"\t\t\t\t\t\t\t\t\t\t\"$name:[1]=='_' \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"Columns\"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Search in Column-A)\t... sort:dm group-colors: column-a:minutes\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Columns\"\t\t\t\t\t\t\t\t\t\t\"a-label:=\"\"Last Modified\"\" addcolumn:a a:=if(now()-$dm:>315576000000000,\"\"f.  : \"\"(now()-$dm:)/315576000000000..\"\" years\"\",if(now()-$dm:>864000000000,\"\"c.  : \"\"(now()-$dm:)/864000000000..\"\" days [ D ]\"\",if(now()-$dm:>36000000000,\"\"b.  : \"\"(now()-$dm:)/36000000000..\"\" hours [ + ]\"\",\"\"a.  : \"\"(now()-$dm:)/600000000..\"\" minutes [ * ]\"\")) sort:dm group-colors: column-a:minutes\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Sort by Column-A)\ta-label:=\"\"TEST\"\" addcolumn:a a:=(stem-length:) sort:a-descending\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Columns\"\t\t\t\t\t\t\t\t\t\t\"a-label:=\"\"TEST\"\" addcolumn:a a:=(stem-length:) sort:a-descending\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Column Formulas)\t... addcolumn:a:0;b:1;c:2\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Columns\"\t\t\t\t\t\t\t\t\t\t\"addcolumn:a:0;b:1;c:2 a:=FORMATFILETIMEUTC($date-created:) b:=FORMATFILETIMEUTC($date-modified:) c:=formatduration($date-created:-$date-modified:,\"\"[d]:hh:mm:ss.SSSSSSS\"\") a-label:=\"\"Date Created UTC\"\" b-label:=\"\"Date Modified UTC\"\" c-label:=\"\"Time difference in 100-nano-seconds\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\r\n\"🠆 Attributes\t(Files/Folders)    \"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Filename Characters)\tchars:20..30\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Attributes%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"chars:20..30\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Name Frequency)\tname-frequency:>20\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Attributes%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"name-frequency:>20\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Number of Siblings)\tsibling-file-count:>=10\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Attributes%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"sibling-file-count:>=10\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 Groups\t(Files/Folders)    \"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Colorize by Path)\tgroup-colors:path no-alternate-row-color: sort:path;size\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Groups%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"group-colors:path no-alternate-row-color: sort:path;size\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Colorize Dirs by Depth)\tgroup-colors:depth|dir: !file: sort:folder-depth-ascending;path\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Groups%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"group-colors:depth|dir: !file: sort:folder-depth-ascending;path\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Colorize Files by Depth)\tgroup-colors:depth|file: !dir: sort:folder-depth-ascending;path\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Groups%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"group-colors:depth|file: !dir: sort:folder-depth-ascending;path\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\r\n\"🠆 Size\t(Files/Folders)    \"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Minimum Filesize)\tsize:>500KB\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Size%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"size:>500KB\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 Modified\t(Files/Folders)    \"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Modified last 60 days)\tdm:>60days\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Modified%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm:>60days\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Modified%09%28Files%2FFolders%29%20%20%20%20\"\r\n\"🠆 (Files Changed This Month)\tdm:month:\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Modified%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm:month:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Files Changed This Week)\tdm:week:\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Modified%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm:week:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Files Changed Today)\tdm:today:\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Modified%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm:today:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Month Range)\tdm-month:11..12\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Modified%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm-month:11..12\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Date Range)\tdm:2023.04.25-2023.04.30 sort:dm-ascending\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Modified%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm:2023.04.25-2023.04.30 sort:dm-ascending\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Time Range)\tdm-hour:8..16\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Modified%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm-hour:8..16\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 Name\t(Files/Folders)    \"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Exact Name)\texact:basename:hosts\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Name%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:basename:hosts\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Unique Paths Only)\tdistinct:path\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Name%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"distinct:path\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Whole Filename)\twholefilename:.git\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Name%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(wfn:.git)\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Filenames containing non-ASCII characters)\tchar-range:>0x7F\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Name%09%28Files%2FFolders%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"char-range:>0x7F\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\r\n\"🠆 Meta\t(Files/Folders/Shortcuts)    \"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠆 (Missing Shortcuts)\t!exists($shortcut-target:)\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/🠆%20Meta%09%28Files%2FFolders%2FShortcuts%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(ext:lnk !exists($shortcut-target:))\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"Unsorted\"\t1\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\" runcount:>1\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20/Unsorted\"\t\t\t\t\t\t\t\t\t\t\" runcount:>\"\"1\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"accessed today #1\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:\"\"%appdata%\\Microsoft\\Windows\\Recent\"\" distinct:name date-accessed:currentday columns:da;ext;name;shortcut-target;type sort:da\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"regex: alphanumberic, dot, string\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"regex:\"\"^[a-zA-Z0-9]+\\.(lnk)$\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"accessed today #2\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:\"\"%appdata%\\Microsoft\\Windows\\Recent\"\" distinct:name date-accessed:currentday columns:da;ext;name;shortcut-target;type sort:da exists($shortcut-target:)\"\t\t\"Date Accessed;Extension;Name;Shortcut Target;Type\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"existing shortcuts\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"*.lnk exists($shortcut-target:) sort:da\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"dm:>=#format-filetime:< #get-date-modified:<\"\"%1\"\">,01/01/2021 >\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm:>=#format-filetime:< #get-date-modified:<\"\"%1\"\">,01/01/2021 >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠆 (Sibling File Count)\tfile: sibling-file-count:>=1 sibling-file-count:<2\"\t0\t\"Bookmarks/🠊%20Search%20Reference%20%09%28Examples%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!py: file: sibling-file-count:>=1 sibling-file-count:<2\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠊 Extensions \t(Basic)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"【  ahk:  】 :: ext:ahk\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:ahk|<dir: child-file:ext:ahk> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"ahk\"\t\t\"\"\r\n\"【  bat:  】 :: ext:bat|cmd\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:bat;cmd;|<dir: child-file:ext:bat;cmd;> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"bat\"\t\t\"\"\r\n\"【  bin:  】 :: ext:bin\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:bin|<dir: child-file:ext:bin> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"bin\"\t\t\"\"\r\n\"【  blend:  】   :: ext:blend\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:blend|<dir: child-file:ext:blend> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"blend\"\t\t\"\"\r\n\"【  bmp:  】 :: ext:bmp\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:bmp|<dir: child-file:ext:bmp> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"bmp\"\t\t\"\"\r\n\"【  cfg:  】 :: ext:cfg\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:cfg|<dir: child-file:ext:cfg> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"cfg\"\t\t\"\"\r\n\"【  cmd:  】 :: ext:cmd\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:cmd|<dir: child-file:ext:cmd> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"cmd\"\t\t\"\"\r\n\"【  css:  】 :: ext:css\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:css|<dir: child-file:ext:css> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"cfg\"\t\t\"\"\r\n\"【  csv:  】 :: ext:csv\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:csv|<dir: child-file:ext:csv> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"csv\"\t\t\"\"\r\n\"【  dll:  】 :: ext:dll\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:dll|<dir: child-file:ext:dll> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"dll\"\t\t\"\"\r\n\"【  doc:  】 :: ext:doc|docx\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:doc;docx;|<dir: child-file:ext:doc;docx;> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"doc\"\t\t\"\"\r\n\"【  efu:  】 :: ext:efu\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:efu|<dir: child-file:ext:efu> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"efu\"\t\t\"\"\r\n\"【  eps:  】 :: ext:eps\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:eps|<dir: child-file:ext:eps> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"eps\"\t\t\"\"\r\n\"【  exe:  】 :: ext:exe\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:exe|<dir: child-file:ext:exe> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"exe\"\t\t\"\"\r\n\"【  htm:  】 :: ext:htm|html\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:htm;html;|<dir: child-file:ext:htm;html;> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"htm\"\t\t\"\"\r\n\"【  ico:  】 :: ext:ico\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:ico|<dir: child-file:ext:ico> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"ico\"\t\t\"\"\r\n\"【  idb:  】 :: ext:idb\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:idb|<dir: child-file:ext:idb> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"idb\"\t\t\"\"\r\n\"【  ini:  】 :: ext:ini\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:ini|<dir: child-file:ext:ini> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"ini\"\t\t\"\"\r\n\"【  jpg:  】 :: ext:jpg|jpeg\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:jpg;jpeg;|<dir: child-file:ext:jpg;jpeg;> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"jpg\"\t\t\"\"\r\n\"【  json:  】    :: ext:json\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:json|ext:jinja-json;|<dir: child-file:ext:json|ext:jinja-json;> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"json\"\t\t\"\"\r\n\"【  lnk:  】 :: ext:lnk\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:lnk|<dir: child-file:ext:lnk> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"lnk\"\t\t\"\"\r\n\"【  log:  】 :: ext:log\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:log|<dir: child-file:ext:log> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"log\"\t\t\"\"\r\n\"【  mcr:  】 :: ext:mcr\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:mcr|<dir: child-file:ext:mcr> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"mcr\"\t\t\"\"\r\n\"【  md:  】  :: ext:md\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:md|ext:jinja-md;|<dir: child-file:ext:md|ext:jinja-md;> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"md\"\t\t\"\"\r\n\"【  mel:  】 :: ext:mel\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:mel|<dir: child-file:ext:mel> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"mel\"\t\t\"\"\r\n\"【  mp3:  】 :: ext:mp3\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:mp3|<dir: child-file:ext:mp3> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"mp3\"\t\t\"\"\r\n\"【  mp4:  】 :: ext:mp4\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:mp4|<dir: child-file:ext:mp4> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"mp4\"\t\t\"\"\r\n\"【  nss:  】 :: ext:nss\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:nss|<dir: child-file:ext:nss> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"nss\"\t\t\"\"\r\n\"【  wav:  】    :: ext:wav\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:wav|<dir: child-file:ext:wav> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"wav\"\t\t\"\"\r\n\"【  pdf:  】 :: ext:pdf\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:pdf|<dir: child-file:ext:pdf> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"pdf\"\t\t\"\"\r\n\"【  png:  】 :: ext:png\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:png|<dir: child-file:ext:png> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"png\"\t\t\"\"\r\n\"【  ps1:  】 :: ext:ps1\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:ps1|<dir: child-file:ext:ps1> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"ps1\"\t\t\"\"\r\n\"【  psd:  】 :: ext:psd\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:psd|<dir: child-file:ext:psd> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"psd\"\t\t\"\"\r\n\"【  py:  】  :: ext:py\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:py|<dir: child-file:ext:py> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"py\"\t\t\"\"\r\n\"【  reg:  】 :: ext:reg\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:reg|<dir: child-file:ext:reg> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"reg\"\t\t\"\"\r\n\"【  stl:  】 :: ext:stl\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:stl|<dir: child-file:ext:stl> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"stl\"\t\t\"\"\r\n\"【  svg:  】 :: ext:svg\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:svg|<dir: child-file:ext:svg> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"svg\"\t\t\"\"\r\n\"【  tga:  】 :: ext:tga\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:tga|<dir: child-file:ext:tga> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"tga\"\t\t\"\"\r\n\"【  tiff:  】    :: ext:tiff\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:tiff|<dir: child-file:ext:tiff> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"tiff\"\t\t\"\"\r\n\"【  ts:  】 :: ext:ts\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:ts|<dir: child-file:ext:ts> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"ts\"\t\t\"\"\r\n\"【  tsx:  】 :: ext:tsx\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:tsx|<dir: child-file:ext:tsx> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"tsx\"\t\t\"\"\r\n\"【  url:  】 :: ext:url\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:url|<dir: child-file:ext:url> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"url\"\t\t\"\"\r\n\"【  usd:  】 :: ext:usd\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:usd|<dir: child-file:ext:usd> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"usd\"\t\t\"\"\r\n\"【  xls:  】 :: ext:xls|xlsx|xlsb\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:xls OR ext:xlsx OR ext:xlsb\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"xls\"\t\t\"\"\r\n\"【  xml:  】 :: ext:xml\"\t0\t\"Bookmarks/🠊%20Extensions%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:xml|<dir: child-file:ext:xml> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"xml\"\t\t\"\"\r\n\"🠊 Foldernames \t(Basic)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"【  .git:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:.git\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\".git\"\t\t\"\"\r\n\"【  nilesoftshell:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:NilesoftShell\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"nilesoftshell\"\t\t\"\"\r\n\"【  .vscode:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:.vscode\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\".vscode\"\t\t\"\"\r\n\"【  adobe:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:adobe\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"adobe\"\t\t\"\"\r\n\"【  autodesk:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:autodesk\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"autodesk\"\t\t\"\"\r\n\"【  backup:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:backup\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"backup\"\t\t\"\"\r\n\"【  bilcd:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:bilcd\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"bilcd\"\t\t\"\"\r\n\"【  bilder:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:bilder\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"bilder\"\t\t\"\"\r\n\"【  cache:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:cache\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"cache\"\t\t\"\"\r\n\"【  code:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:code\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"code\"\t\t\"\"\r\n\"【  common:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:common\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"common\"\t\t\"\"\r\n\"【  config:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:config\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"config\"\t\t\"\"\r\n\"【  sublimetext::  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:\"\"Sublime Text\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"sublimetext\"\t\t\"\"\r\n\"【  debug:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:debug\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"debug\"\t\t\"\"\r\n\"【  default:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:default\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"default\"\t\t\"\"\r\n\"【  desktop:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:desktop\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"desktop\"\t\t\"\"\r\n\"【  docs:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:docs\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"docs\"\t\t\"\"\r\n\"【  dokumenter:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:dokumenter\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"dokumenter\"\t\t\"\"\r\n\"【  downloads:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:downloads\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"downloads\"\t\t\"\"\r\n\"【  dropbox:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:dropbox\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"dropbox\"\t\t\"\"\r\n\"【  everything:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:everything\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"everything\"\t\t\"\"\r\n\"【  extensions:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:extensions\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"extensions\"\t\t\"\"\r\n\"【  fonts:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:fonts|ext:conf;eot;otf;pfb;sfd;ttf;|<dir: child-file:ext:conf;eot;otf;pfb;sfd;ttf;> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"fonts\"\t\t\"\"\r\n\"【  idefagskolen:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:idefagskolen\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"idefagskolen\"\t\t\"\"\r\n\"【  images:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:images\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"images\"\t\t\"\"\r\n\"【  include:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:include\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"include\"\t\t\"\"\r\n\"【  includes:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:includes\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"includes\"\t\t\"\"\r\n\"【  jhp:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:jhp\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"jhp\"\t\t\"\"\r\n\"【  jobbrelatert:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:jobbrelatert\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"jobbrelatert\"\t\t\"\"\r\n\"【  lib:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:lib\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"lib\"\t\t\"\"\r\n\"【  local:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:local\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"local\"\t\t\"\"\r\n\"【  metadata:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:metadata\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"metadata\"\t\t\"\"\r\n\"【  microsoft:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:microsoft\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"microsoft\"\t\t\"\"\r\n\"【  midlertidig:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:midlertidig\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"midlertidig\"\t\t\"\"\r\n\"【  mobilbackup:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:mobilbackup\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"mobilbackup\"\t\t\"\"\r\n\"【  modules:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:modules\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"modules\"\t\t\"\"\r\n\"【  packages:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:packages\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"packages\"\t\t\"\"\r\n\"【  personal:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:personal\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"personal\"\t\t\"\"\r\n\"【  personlig:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:personlig\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"personlig\"\t\t\"\"\r\n\"【  pip:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:pip\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"pip\"\t\t\"\"\r\n\"【  plugins:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:plugins\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"plugins\"\t\t\"\"\r\n\"【  prefetch:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:prefetch\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"prefetch\"\t\t\"\"\r\n\"【  privat:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:privat\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"privat\"\t\t\"\"\r\n\"【  programdata:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:programdata\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"programdata\"\t\t\"\"\r\n\"【  programmer:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:programmer\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"programmer\"\t\t\"\"\r\n\"【  progs:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:progs\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"progs\"\t\t\"\"\r\n\"【  properties:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:properties\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"properties\"\t\t\"\"\r\n\"【  prosjekter:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:prosjekter\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"prosjekter\"\t\t\"\"\r\n\"【  readme:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:readme\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"readme\"\t\t\"\"\r\n\"【  scripts:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:scripts\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"scripts\"\t\t\"\"\r\n\"【  settings:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:settings\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"settings\"\t\t\"\"\r\n\"【  shared:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:shared\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"shared\"\t\t\"\"\r\n\"【  snippets:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:snippets\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"snippets\"\t\t\"\"\r\n\"【  source:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:source\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"source\"\t\t\"\"\r\n\"【  src:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:src\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"src\"\t\t\"\"\r\n\"【  system:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:system\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"system\"\t\t\"\"\r\n\"【  themes:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:themes\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"themes\"\t\t\"\"\r\n\"【  types:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:types\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"types\"\t\t\"\"\r\n\"【  users:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:users\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"users\"\t\t\"\"\r\n\"【  util:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:util\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"util\"\t\t\"\"\r\n\"【  utils:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:utils\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"utils\"\t\t\"\"\r\n\"【  venv:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:venv\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"venv\"\t\t\"\"\r\n\"【  views:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:views\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"views\"\t\t\"\"\r\n\"【  webcache:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:WebCache\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"webcache\"\t\t\"\"\r\n\"【  widgets:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:widgets\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"widgets\"\t\t\"\"\r\n\"【  wordpress:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:wordpress\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"wordpress\"\t\t\"\"\r\n\"【  workflow:  】\t:: exact:ancestor-name:\"\t0\t\"Bookmarks/🠊%20Foldernames%20%09%28Basic%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"exact:ancestor-name:workflow\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"workflow\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks\"\r\n\"🠊 Regex: Search \t(Patterns)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"【  DATEPATTERNSPATH:  】\t:: Datepatterns (example: YYYY.MM.DD)\"\t0\t\"Bookmarks/🠊%20Regex%3A%20Search%20%09%28Patterns%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"REGEX:PATH:\"\"((19\\d{2}|20\\d{2})([\\.\\-\\\\/ ]?)(0[1-9]|1[0-2])\\3(0[1-9]|[12][0-9]|3[01]))|((0[1-9]|[12][0-9]|3[01])([\\.\\-\\\\/ ]?)(0[1-9]|1[0-2])\\8(19\\d{2}|20\\d{2}))\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"datepatternspath\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Regex%3A%20Search%20%09%28Patterns%29%20%20%20%20\"\r\n\"【  LANGISO:  】\t:: ISO Language Codes (example: \"\"en-US\"\")\"\t0\t\"Bookmarks/🠊%20Regex%3A%20Search%20%09%28Patterns%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"regex:\"\"\\b[a-z]{2}-[A-Z]{2}\\.\\b\"\" \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"LANGISO\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Regex%3A%20Search%20%09%28Patterns%29%20%20%20%20\"\r\n\"【  GUID:  】\t:: {123e4567-e89b-12d3-a456-9AC7CBDCEE52}\"\t0\t\"Bookmarks/🠊%20Regex%3A%20Search%20%09%28Patterns%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"< REGEX:PATH:\"\"\\{[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}\"\" >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"GUID\"\t\t\"\"\r\n\"【  HASH:  】\t:: cd2469b92b21d219\"\t0\t\"Bookmarks/🠊%20Regex%3A%20Search%20%09%28Patterns%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"< REGEX:PATH:\"\"\\b[A-Fa-f0-9]{16}\\b\"\" >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"HASH\"\t\t\"\"\r\n\"【  MD5:  】\t:: 123e4567e89b12d3a456426614174000\"\t0\t\"Bookmarks/🠊%20Regex%3A%20Search%20%09%28Patterns%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"< REGEX:PATH:\"\"[A-Fa-f0-9]{32}\"\" >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"MD5\"\t\t\"\"\r\n\"【  SID:  】\t:: S-1-5-21-3623811015-3361044348-*************\"\t0\t\"Bookmarks/🠊%20Regex%3A%20Search%20%09%28Patterns%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"< REGEX:PATH:\"\"S-1-5(-\\d+){4,}\"\" >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"SID\"\t\t\"\"\r\n\"🠊 Contents \t(Patterns)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"🠊 Contents: Duplicates \t(Patterns)    \"\t1\t\"Bookmarks/🠊%20Contents%20%09%28Patterns%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"【  content-dupe:  】\t:: size:<1mb dupe:size;sha256\"\t0\t\"Bookmarks/🠊%20Contents%20%09%28Patterns%29%20%20%20%20/🠊%20Contents%3A%20Duplicates%20%09%28Patterns%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"size:<1mb dupe:size;sha256\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"content-dupe\"\t\t\"\"\r\n\"🠊 Regex: Contents \t(Patterns)    \"\t1\t\"Bookmarks/🠊%20Contents%20%09%28Patterns%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"【  content-urls:  】\t:: text:regex:content:{URL}\"\t0\t\"Bookmarks/🠊%20Contents%20%09%28Patterns%29%20%20%20%20/🠊%20Regex%3A%20Contents%20%09%28Patterns%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ii: ee: size:<2mb focus: nohighlight:text: REGEX:CONTENT:\"\"\\b((https?|ftp|file):\\/\\/[-A-Z0-9+&@#\\/%?=~_|!:,.;]*[-A-Z0-9+&@#\\/%=~_|])\"\" column1:=regmatch0: columns:dm;size;ext;name;column1;path;name-frequency;extension-frequency;run-count; \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"content-urls\"\t\t\"\"\r\n\"【  content-datepatterns:  】\t:: regex:content:{DATEPATTERNS}\"\t0\t\"Bookmarks/🠊%20Contents%20%09%28Patterns%29%20%20%20%20/🠊%20Regex%3A%20Contents%20%09%28Patterns%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ii: ee: size:<2mb focus: nohighlight:text: REGEX:CONTENT:\"\"((19\\d{2}|20\\d{2})([\\.\\-\\\\/ ]?)(0[1-9]|1[0-2])\\3(0[1-9]|[12][0-9]|3[01]))|((0[1-9]|[12][0-9]|3[01])([\\.\\-\\\\/ ]?)(0[1-9]|1[0-2])\\8(19\\d{2}|20\\d{2}))\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"content-datepatterns\"\t\t\"\"\r\n\"【  pyprompts:  】\t:: py: content:\"\"chatgpt\"\" content-datepatterns: !path:\"\".gpt\"\" \"\t0\t\"Bookmarks/🠊%20Contents%20%09%28Patterns%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"py: content:\"\"chatgpt\"\" content-datepatterns: !path:\"\".gpt\"\" \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"pyprompts\"\t\t\"\"\r\n\"【  mdprompts:  】\t:: pd: content:\"\"chatgpt\"\" content-datepatterns: !path:\"\".gpt\"\"\"\t0\t\"Bookmarks/🠊%20Contents%20%09%28Patterns%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"md: content:\"\"chatgpt\"\" content-datepatterns: !path:\"\".gpt\"\" \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"mdprompts\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks\"\r\n\"🠊 Columns \t(Macros)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"add-column:a a-label:\"\"Depth\"\" a:=LEN($fullpath:)-LEN(SUBSTITUTE($fullpath:,\"\"\\\"\",\"\"\"\"))\"\t0\t\"Bookmarks/🠊%20Columns%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"add-column:a a-label:\"\"Depth\"\" a:=LEN($fullpath:)-LEN(SUBSTITUTE($fullpath:,\"\"\\\"\",\"\"\"\"))\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"coldepth\"\t\t\"\"\r\n\"🠆 [ match1: ]\t:: 1 column\"\t0\t\"Bookmarks/🠊%20Columns%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"col-a:=regmatch0: a-label:=\"\"match1\"\"  addcolumn:a:5\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"match1\"\t\t\"\"\r\n\"🠆 [ match2: ]\t:: 2 column\"\t0\t\"Bookmarks/🠊%20Columns%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"col-b:=regmatch0: b-label:=\"\"match2\"\"  addcolumn:b:5\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"match2\"\t\t\"\"\r\n\"🠊 Sorting \t(Macros)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Sorting%20%09%28Macros%29%20%20%20%20\"\r\n\"【  SDM:  】\t:: Sort: Date-Modified\"\t0\t\"Bookmarks/🠊%20Sorting%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(sort:date-modified)\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"SDM\"\t\t\"\"\r\n\"【  SDA:  】\t:: Sort: Date-Accessed\"\t0\t\"Bookmarks/🠊%20Sorting%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(sort:date-accessed)\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"SDA\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Sorting%20%09%28Macros%29%20%20%20%20\"\r\n\"【  SFD:  】\t:: Sort: Folder-Depth\"\t0\t\"Bookmarks/🠊%20Sorting%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(sort:folder-depth-ascending)\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"SFD\"\t\t\"\"\r\n\"【  SFDA:  】\t:: Sort: Folder-Depth & Size\"\t0\t\"Bookmarks/🠊%20Sorting%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(sort:folder-depth-ascending;size)\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"SFDA\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Sorting%20%09%28Macros%29%20%20%20%20\"\r\n\"【  SDM:  】\t:: Sort: Date-Modified\"\t0\t\"Bookmarks/🠊%20Sorting%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(sort:date-modified)\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"SDM\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Sorting%20%09%28Macros%29%20%20%20%20\"\r\n\"【  SLS:  】\t:: Sort: Stem-Length (Longest)\"\t0\t\"Bookmarks/🠊%20Sorting%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"sort:stem-length\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"SLS\"\t\t\"\"\r\n\"【  SLSA:  】\t:: Sort: Stem-Length (Shortest)\"\t0\t\"Bookmarks/🠊%20Sorting%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"sort:stem-length-ascending\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"SLSA\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks\"\r\n\"🠊 Shortcuts: Common \t(Macros)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"Shortcuts/URLs\t【  URLS:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:url|< ext:url|dir:nohighlight:path:urls >|< ext:url|dir:exact:ancestor-name:shortcuts|ext:url|dir:exact:ancestor-name:_shortcuts >|< ext:url|dir:exact:ancestor-name:urls|ext:url|dir:exact:ancestor-name:_urls >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"URLS\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\r\n\"Chrome Profiles\t【  CHROME:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"PATH:\"\"%LOCALAPPDATA%/Google/Chrome/User Data\"\" REGEX:ANCESTOR-NAME:\"\"\\s[0-9]{1,2}(?!\\d)$\"\" !< CHILDFOLDERCOUNT:0 > !< SIZE:<24BYTES > !< REGEX:ANCESTOR-NAME:\"\".*cache.*\"\" > !< REGEX:PATH:\"\"\\{?[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}?\"\" > !< REGEX:PATH:\"\"[0-9A-Fa-f]{32}\"\" >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"chromeprofiles\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\r\n\"Shortcuts\t【  SHORTCUTS:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:lnk exists($shortcut-target:) addcolumns:dc;shortcut-target: !path:\"\"%APPDATA%\\Microsoft\\Windows\\Recent\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"shortcuts\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\r\n\"Startup\t【  STARTUP:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(Autoruns OR HijackThis AND EXT:exe) OR PATH:\"\"%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"Startup\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\r\n\"Downloads\t【  DWN:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(EXT:avi;divx;flv;hdmov;m1v;m2p;m2t;m2ts;m2v;m4v;mkv;mov;mp2v;mp4;mp4v;mpe;mpeg;mpg;mpls;mpv2;mpv4;ogm;ogv;webm;wm;wmp;wmv;7z;ace;arj;bz2;cab;gz;gzip;jar;r00;r01;r02;r03;r04;r05;r06;r07;r08;r09;r10;r11;r12;r13;r14;r15;r16;r17;r18;r19;r20;r21;r22;r23;r24;r25;r26;r27;r28;r29;rar;tar;tgz;z;zip; AND (NOHIGHLIGHT:PATH:\"\"Downloads\\\"\" OR NOHIGHLIGHT:REGEX:NAME:\"\"Download.*\"\" OR NOHIGHLIGHT:REGEX:PATH:\"\"Download.*\"\" OR REGEX:PATH:\"\"Downloads\\\\.*\"\" ) AND (SIZE:>\"\"24BYTES\"\" AND PATH:CHARS:<\"\"190\"\" AND !REGEX:\"\"\\b.*\\.r(?!00)\\d{2}$\\b\"\" ) AND (rel: ) ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"DWN\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\r\n\"Everything\t【  EVR:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"PATH:\"\"app_everything\"\" REL:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"evr\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\r\n\"Virtual Environments (venv)\t【  VENV:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"((!PATH:\"\"__pycache__\"\" AND !PATH:\"\"*.pyc\"\") AND (size:>12bytes exact:ancestor-name:\"\"venv\"\" OR path:\"\"_py_venvs\\\"\") OR (exact:child-folder-name:\"\"Lib\"\" OR exact:child-folder-name:\"\"Include\"\" OR exact:child-folder-name:\"\"Scripts\"\") )\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"Venv\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\r\n\"【  PRJ:  】\t:: Projects\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"PATH:prj\\ rel:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"PRJ\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\r\n\"3D Related\t【  3D:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"((EXT:\"\"3dm\"\" OR EXT:\"\"3mf\"\" OR EXT:\"\"3ds\"\" OR EXT:\"\"abc\"\" OR EXT:\"\"blend\"\" OR EXT:\"\"c4d\"\" OR EXT:\"\"cob\"\" OR EXT:\"\"curaproject\"\" OR EXT:\"\"dae\"\" OR EXT:\"\"dwf\"\" OR EXT:\"\"dwg\"\" OR EXT:\"\"dxf\"\" OR EXT:\"\"fbx\"\" OR EXT:\"\"glb\"\" OR EXT:\"\"gltf\"\" OR EXT:\"\"hrc\"\" OR EXT:\"\"iam\"\" OR EXT:\"\"iob\"\" OR EXT:\"\"ipj\"\" OR EXT:\"\"ipt\"\" OR EXT:\"\"iv\"\" OR EXT:\"\"lwo\"\" OR EXT:\"\"ma\"\" OR EXT:\"\"max\"\" OR EXT:\"\"mb\"\" OR EXT:\"\"nwc\"\" OR EXT:\"\"nwd\"\" OR EXT:\"\"nwf\"\" OR EXT:\"\"obj\"\" OR EXT:\"\"osg\"\" OR EXT:\"\"ply\"\" OR EXT:\"\"scn\"\" OR EXT:\"\"skp\"\" OR EXT:\"\"step\"\" OR EXT:\"\"stl\"\" OR EXT:\"\"stp\"\" OR EXT:\"\"u3d\"\" OR EXT:\"\"usd\"\" OR EXT:\"\"usdc\"\" OR EXT:\"\"usdz\"\" OR EXT:\"\"vob\"\" OR EXT:\"\"vrml\"\" OR EXT:\"\"wrl\"\" OR EXT:\"\"x3d\"\" OR EXT:\"\"xsi\"\" OR EXT:\"\"zpr\"\" OR EXT:\"\"mtl\"\") AND (rel: SORT:DM;PATH;EXT;NAME;SIZE ) )\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"3Dold\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\r\n\"Cloud Drives (Sync/DropBox/OneDrive/GoogleDrive)\t【  CL:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"SYNC: OR CLDB: OR CLOD: OR CLGD:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"CL\"\t\t\"C:\\Users\\<USER>\\Desktop\\PRJ\\GIT_WORKFLOW\\__SHELL__\\PORTAL\\APPS\\app_everything\\user\\_downloads\\icons\\Icons-Cloud\\CloudIconSynced.ico\"\r\n\"🠆 Sync\t【  SYNC:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!PATH:\"\"/Sync.Cache/\"\" PATH:\"\"%USERNAME%/Sync/\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"SYNC\"\t\t\"C:\\Users\\<USER>\\Desktop\\PRJ\\GIT_WORKFLOW\\__SHELL__\\PORTAL\\APPS\\app_everything\\user\\_downloads\\icons\\Icons-Cloud\\CloudIconSynced.ico\"\r\n\"🠆 Dropbox\t【  CLDB:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"PATH:\"\"Dropbox\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"CLDB\"\t\t\"C:\\Users\\<USER>\\Desktop\\PRJ\\GIT_WORKFLOW\\__SHELL__\\PORTAL\\APPS\\app_everything\\user\\_downloads\\icons\\Icons-Cloud\\CloudIconSynced.ico\"\r\n\"🠆 OneDrive\t【  CLOD:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"PATH:\"\"Onedrive\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"CLOD\"\t\t\"C:\\Users\\<USER>\\Desktop\\PRJ\\GIT_WORKFLOW\\__SHELL__\\PORTAL\\APPS\\app_everything\\user\\_downloads\\icons\\Icons-Cloud\\CloudIconSynced.ico\"\r\n\"🠆 Google Drive\t【  CLGD:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"PATH:\"\"GoogleDrive\"\" OR PATH:\"\"My Drive\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"CLGD\"\t\t\"C:\\Users\\<USER>\\Desktop\\PRJ\\GIT_WORKFLOW\\__SHELL__\\PORTAL\\APPS\\app_everything\\user\\_downloads\\icons\\Icons-Cloud\\CloudIconSynced.ico\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\r\n\"Sublime Text\t【  ST:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"((NOHIGHLIGHT:PATH:\"\"%APPDATA%\"\" AND PATH:\"\"Sublime Text\"\" NOHIGHLIGHT:PATH:\"\"Sublime Text\"\" OR REGEX:EXTENSION:\"\"SUBLIME.*\"\" OR REGEX:PATH:\"\"Sublime Text\"\" OR NOHIGHLIGHT:REGEX:NAME:\"\"SUBLIME.*\"\" ) OR (NOHIGHLIGHT:STP: OR NOHIGHLIGHT:STW: OR NOHIGHLIGHT:STT: OR NOHIGHLIGHT:STC: OR NOHIGHLIGHT:STS: OR NOHIGHLIGHT:STM: OR NOHIGHLIGHT:STB: ) AND (rel: ) ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"ST\"\t\t\"C:\\Users\\<USER>\\AppData\\Roaming\\Everything\\AlwaysMostRecent\\Icons\\SublimeText\\Filetype-Sublime-White Black.ico\"\r\n\"🠆 Sublime: Projects\t【  STP:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t0\t0\t\t\"ext:sublime-project;|<dir: child-file:ext:sublime-project;> <sort:path; secondary-sort:!isfolder:>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"STP\"\t\t\"C:\\Users\\<USER>\\AppData\\Roaming\\Everything\\AlwaysMostRecent\\Icons\\SublimeText\\Filetype-Sublime-Yellow Black.ico\"\r\n\"🠆 Sublime: Workspaces\t【  STW:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"EXT:sublime-workspace rel:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"STW\"\t\t\"C:\\Users\\<USER>\\AppData\\Roaming\\Everything\\AlwaysMostRecent\\Icons\\SublimeText\\Filetype-Sublime-Yellow Black.ico\"\r\n\"🠆 Sublime: Themes\t【  STT:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"EXT:sublime-theme OR EXT:tmTheme rel:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"STT\"\t\t\"C:\\Users\\<USER>\\AppData\\Roaming\\Everything\\AlwaysMostRecent\\Icons\\SublimeText\\Filetype-Sublime-Yellow Black.ico\"\r\n\"🠆 Sublime: Schemes|Commands|Completions\t【  STC:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:sublime-color-scheme|ext:sublime-commands|ext:sublime-completions rel:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"STC\"\t\t\"C:\\Users\\<USER>\\AppData\\Roaming\\Everything\\AlwaysMostRecent\\Icons\\SublimeText\\Filetype-Sublime-Yellow Black.ico\"\r\n\"🠆 Sublime: Settings\t【  STS:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"EXT:sublime-settings OR EXT:tmPreferences rel:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"STS\"\t\t\"C:\\Users\\<USER>\\AppData\\Roaming\\Everything\\AlwaysMostRecent\\Icons\\SublimeText\\Filetype-Sublime-Yellow Black.ico\"\r\n\"🠆 Sublime: Menus\t【  STM:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"EXT:sublime-m* rel:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"STM\"\t\t\"C:\\Users\\<USER>\\AppData\\Roaming\\Everything\\AlwaysMostRecent\\Icons\\SublimeText\\Filetype-Sublime-Yellow Black.ico\"\r\n\"🠆 Sublime: Keymap\t【  STK:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"EXT:sublime-keymap rel:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"STK\"\t\t\"C:\\Users\\<USER>\\AppData\\Roaming\\Everything\\AlwaysMostRecent\\Icons\\SublimeText\\Filetype-Sublime-Yellow Black.ico\"\r\n\"🠆 Sublime: Build System\t【  STB:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Common%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"EXT:sublime-build rel:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"STB\"\t\t\"C:\\Users\\<USER>\\AppData\\Roaming\\Everything\\AlwaysMostRecent\\Icons\\SublimeText\\Filetype-Sublime-Yellow Black.ico\"\r\n\"🠊 Shortcuts: Specific \t(Macros)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"My Text Files\t【  MyTxt:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(TEXT: AND content:\"\"=============================================================================\"\") SORT:DM\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"MyTxt\"\t\t\"\"\r\n\"My Script Files\t【  MySc:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(SC: AND content:\"\"=============================================================================\"\") SORT:DM\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"MySc\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\r\n\"mYtXT nEW tEST\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"\trel: AND TEXT: AND ((TEXT: AND content:\"\"# =================================================================\"\") OR (TEXT: AND content:\"\"# -----------------------------------------------------------------\"\") OR (TEXT: AND content:\"\":: ================================================================\"\") OR (TEXT: AND content:\"\":: ----------------------------------------------------------------\"\") OR (TEXT: AND content:\"\"// ================================================================\"\") OR (TEXT: AND content:\"\"// ----------------------------------------------------------------\"\") OR (TEXT: AND content:\"\"; =================================================================\"\") OR (TEXT: AND content:\"\"; -----------------------------------------------------------------\"\") )\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"【  TEXT:  】\t:: Text Files\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(((EXT:\"\"ad\"\" OR EXT:\"\"adb\"\" OR EXT:\"\"ads\"\" OR EXT:\"\"ai\"\" OR EXT:\"\"alg\"\" OR EXT:\"\"ans\"\" OR EXT:\"\"asc\"\" OR EXT:\"\"asciidoc\"\" OR EXT:\"\"ascx\"\" OR EXT:\"\"asm\"\" OR EXT:\"\"asmx\"\" OR EXT:\"\"asp\"\" OR EXT:\"\"aspx\"\" OR EXT:\"\"asx\"\" OR EXT:\"\"awk\"\" OR EXT:\"\"bas\"\" OR EXT:\"\"bash\"\" OR EXT:\"\"bat\"\" OR EXT:\"\"bcp\"\" OR EXT:\"\"bf\"\" OR EXT:\"\"bib\"\" OR EXT:\"\"btm\"\" OR EXT:\"\"c\"\" OR EXT:\"\"cbl\"\" OR EXT:\"\"cc\"\" OR EXT:\"\"cd\"\" OR EXT:\"\"cfg\"\" OR EXT:\"\"chm\"\" OR EXT:\"\"clj\"\" OR EXT:\"\"cls\"\" OR EXT:\"\"cmake\"\" OR EXT:\"\"cmd\"\" OR EXT:\"\"cob\"\" OR EXT:\"\"cod\"\" OR EXT:\"\"conf\"\" OR EXT:\"\"cpp\"\" OR EXT:\"\"cs\"\" OR EXT:\"\"csa\"\" OR EXT:\"\"csh\"\" OR EXT:\"\"cshader\"\" OR EXT:\"\"csproj\"\" OR EXT:\"\"css\"\" OR EXT:\"\"csv\"\" OR EXT:\"\"cxx\"\" OR EXT:\"\"d\"\" OR EXT:\"\"dart\"\" OR EXT:\"\"dbs\"\" OR EXT:\"\"def\"\" OR EXT:\"\"dic\"\" OR EXT:\"\"diff\"\" OR EXT:\"\"do\"\" OR EXT:\"\"doc\"\" OR EXT:\"\"dockerfile\"\" OR EXT:\"\"docm\"\" OR EXT:\"\"docx\"\" OR EXT:\"\"dos\"\" OR EXT:\"\"dot\"\" OR EXT:\"\"dotm\"\" OR EXT:\"\"dotx\"\" OR EXT:\"\"dsh\"\" OR EXT:\"\"dshader\"\" OR EXT:\"\"dsp\"\" OR EXT:\"\"dsw\"\" OR EXT:\"\"dtd\"\" OR EXT:\"\"dwg\"\" OR EXT:\"\"dxf\"\" OR EXT:\"\"elm\"\" OR EXT:\"\"env\"\" OR EXT:\"\"eps\"\" OR EXT:\"\"epub\"\" OR EXT:\"\"erl\"\" OR EXT:\"\"ext\"\" OR EXT:\"\"f\"\" OR EXT:\"\"f90\"\" OR EXT:\"\"faq\"\" OR EXT:\"\"filters\"\" OR EXT:\"\"fky\"\" OR EXT:\"\"frm\"\" OR EXT:\"\"fs\"\" OR EXT:\"\"fsx\"\" OR EXT:\"\"fx\"\" OR EXT:\"\"gdoc\"\" OR EXT:\"\"git*\"\" OR EXT:\"\"go\"\" OR EXT:\"\"groovy\"\" OR EXT:\"\"gsh\"\" OR EXT:\"\"gshader\"\" OR EXT:\"\"gsheet\"\" OR EXT:\"\"gslides\"\" OR EXT:\"\"h\"\" OR EXT:\"\"hgignore\"\" OR EXT:\"\"hh\"\" OR EXT:\"\"hlsl\"\" OR EXT:\"\"hlsli\"\" OR EXT:\"\"hpp\"\" OR EXT:\"\"hs\"\" OR EXT:\"\"hsh\"\" OR EXT:\"\"hshader\"\" OR EXT:\"\"hta\"\" OR EXT:\"\"htaccess\"\" OR EXT:\"\"htm\"\" OR EXT:\"\"html\"\" OR EXT:\"\"htt\"\" OR EXT:\"\"htw\"\" OR EXT:\"\"htx\"\" OR EXT:\"\"hxx\"\" OR EXT:\"\"ibq\"\" OR EXT:\"\"ics\"\" OR EXT:\"\"idl\"\" OR EXT:\"\"idq\"\" OR EXT:\"\"inc\"\" OR EXT:\"\"indd\"\" OR EXT:\"\"inf\"\" OR EXT:\"\"ini\"\" OR EXT:\"\"inl\"\" OR EXT:\"\"inx\"\" OR EXT:\"\"ipp\"\" OR EXT:\"\"jav\"\" OR EXT:\"\"java\"\" OR EXT:\"\"jl\"\" OR EXT:\"\"js\"\" OR EXT:\"\"json\"\" OR EXT:\"\"jsp\"\" OR EXT:\"\"jsproj\"\" OR EXT:\"\"jsx\"\" OR EXT:\"\"kci\"\" OR EXT:\"\"keynote\"\" OR EXT:\"\"kt\"\" OR EXT:\"\"less\"\" OR EXT:\"\"lgn\"\" OR EXT:\"\"license\"\" OR EXT:\"\"lisp\"\" OR EXT:\"\"log\"\" OR EXT:\"\"lst\"\" OR EXT:\"\"lua\"\" OR EXT:\"\"m\"\" OR EXT:\"\"m3u\"\" OR EXT:\"\"mak\"\" OR EXT:\"\"makefile\"\" OR EXT:\"\"map\"\" OR EXT:\"\"mcr\"\" OR EXT:\"\"md\"\" OR EXT:\"\"mdp\"\" OR EXT:\"\"me\"\" OR EXT:\"\"mht\"\" OR EXT:\"\"mhtml\"\" OR EXT:\"\"mk\"\" OR EXT:\"\"ml\"\" OR EXT:\"\"mli\"\" OR EXT:\"\"mobi\"\" OR EXT:\"\"mod\"\" OR EXT:\"\"mpp\"\" OR EXT:\"\"ms\"\" OR EXT:\"\"msg\"\" OR EXT:\"\"nfo\"\" OR EXT:\"\"nss\"\" OR EXT:\"\"odp\"\" OR EXT:\"\"ods\"\" OR EXT:\"\"odt\"\" OR EXT:\"\"pages\"\" OR EXT:\"\"pas\"\" OR EXT:\"\"patch\"\" OR EXT:\"\"pc3\"\" OR EXT:\"\"pdf\"\" OR EXT:\"\"php\"\" OR EXT:\"\"pkgdef\"\" OR EXT:\"\"pkgundef\"\" OR EXT:\"\"pl\"\" OR EXT:\"\"plist\"\" OR EXT:\"\"potm\"\" OR EXT:\"\"potx\"\" OR EXT:\"\"ppam\"\" OR EXT:\"\"pps\"\" OR EXT:\"\"ppsm\"\" OR EXT:\"\"ppsx\"\" OR EXT:\"\"ppt\"\" OR EXT:\"\"pptm\"\" OR EXT:\"\"pptx\"\" OR EXT:\"\"prc\"\" OR EXT:\"\"pro\"\" OR EXT:\"\"properties\"\" OR EXT:\"\"ps\"\" OR EXT:\"\"ps1\"\" OR EXT:\"\"ps1xml\"\" OR EXT:\"\"psd\"\" OR EXT:\"\"psh\"\" OR EXT:\"\"pshader\"\" OR EXT:\"\"pub\"\" OR EXT:\"\"py\"\" OR EXT:\"\"q\"\" OR EXT:\"\"r\"\" OR EXT:\"\"rb\"\" OR EXT:\"\"rc\"\" OR EXT:\"\"rc2\"\" OR EXT:\"\"rct\"\" OR EXT:\"\"readme\"\" OR EXT:\"\"reg\"\" OR EXT:\"\"res\"\" OR EXT:\"\"rgs\"\" OR EXT:\"\"rkt\"\" OR EXT:\"\"rmd\"\" OR EXT:\"\"rs\"\" OR EXT:\"\"rst\"\" OR EXT:\"\"rtf\"\" OR EXT:\"\"rul\"\" OR EXT:\"\"s\"\" OR EXT:\"\"sas\"\" OR EXT:\"\"sass\"\" OR EXT:\"\"scala\"\" OR EXT:\"\"scc\"\" OR EXT:\"\"scene\"\" OR EXT:\"\"scm\"\" OR EXT:\"\"scss\"\" OR EXT:\"\"sed\"\" OR EXT:\"\"sh\"\" OR EXT:\"\"shtm\"\" OR EXT:\"\"shtml\"\" OR EXT:\"\"sldm\"\" OR EXT:\"\"sldx\"\" OR EXT:\"\"slnf\"\" OR EXT:\"\"sol\"\" OR EXT:\"\"sql\"\" OR EXT:\"\"srf\"\" OR EXT:\"\"srt\"\" OR EXT:\"\"ss\"\" OR EXT:\"\"stm\"\" OR EXT:\"\"stp\"\" OR EXT:\"\"sub\"\" OR EXT:\"\"sublime*\"\" OR EXT:\"\"sv\"\" OR EXT:\"\"swift\"\" OR EXT:\"\"t\"\" OR EXT:\"\"tab\"\" OR EXT:\"\"tcl\"\" OR EXT:\"\"tdl\"\" OR EXT:\"\"tex\"\" OR EXT:\"\"thmx\"\" OR EXT:\"\"tlh\"\" OR EXT:\"\"tli\"\" OR EXT:\"\"tmPreferences\"\" OR EXT:\"\"tmSnippet\"\" OR EXT:\"\"toml\"\" OR EXT:\"\"trg\"\" OR EXT:\"\"ts\"\" OR EXT:\"\"tsv\"\" OR EXT:\"\"tsx\"\" OR EXT:\"\"tt\"\" OR EXT:\"\"txt\"\" OR EXT:\"\"ucf\"\" OR EXT:\"\"url\"\" OR EXT:\"\"v\"\" OR EXT:\"\"vb\"\" OR EXT:\"\"vba\"\" OR EXT:\"\"vbs\"\" OR EXT:\"\"vcf\"\" OR EXT:\"\"vdx\"\" OR EXT:\"\"vhd\"\" OR EXT:\"\"vhdl\"\" OR EXT:\"\"vsc*\"\" OR EXT:\"\"vsd\"\" OR EXT:\"\"vsdm\"\" OR EXT:\"\"vsdx\"\" OR EXT:\"\"vss\"\" OR EXT:\"\"vssm\"\" OR EXT:\"\"vssx\"\" OR EXT:\"\"vst\"\" OR EXT:\"\"vstm\"\" OR EXT:\"\"vstx\"\" OR EXT:\"\"wpd\"\" OR EXT:\"\"wps\"\" OR EXT:\"\"wri\"\" OR EXT:\"\"xlam\"\" OR EXT:\"\"xls\"\" OR EXT:\"\"xlsb\"\" OR EXT:\"\"xlsm\"\" OR EXT:\"\"xlsx\"\" OR EXT:\"\"xltm\"\" OR EXT:\"\"xltx\"\" OR EXT:\"\"xml\"\" OR EXT:\"\"xq\"\" OR EXT:\"\"xquery\"\" OR EXT:\"\"yaml\"\" OR EXT:\"\"yml\"\") ) AND ((!SIZE:<24BYTES AND !REGEX:^[a-z]{32}_[0-9]+\\.[a-f0-9]{64}$ AND !REGEX:^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$ AND !REGEX:^[A-F0-9]{8}_[A-F0-9]{8}$ AND !REGEX:^S-1-5-21-\\d+-\\d+-\\d+-\\d+$ ) ) ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"Text\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\r\n\"Filter 🠆 Images\t【  IMG_:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"IMG: AND rel:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"Img_\"\t\t\"\"\r\n\"🠆 Images: Private\t【  IMGP:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"((DIR: AND PATH:\"\"Privat\"\" AND PATH:\"\"Personlig\"\" ) OR (FILE: AND PATH:\"\"Privat\"\" AND PATH:\"\"Personlig\"\" AND IMG: ) AND (!NAME:\"\"SCREENSHOT\"\" AND !NAME:\"\"SNAPCHAT\"\" ) AND (SIZE:>\"\"1MB\"\" ) AND (rel: ) )\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"IMGP\"\t\t\"\"\r\n\"🠆 Images: Datepatterns\t【  IMGDP:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"< nohighlight:datepatterns: nohighlight:img: size:>800kb > < path:android|path:mobilback|path:alva|path:privat > sort:name\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"IMGDP\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\r\n\"🠆 Scripts: Batch-Scripts\t【  BATCH:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"((DIR: AND REGEX:PATH:\"\"Batch-Scripts\"\") OR (REGEX:PATH:\"\"Batch-Scripts\"\" AND (EXT:BAT OR EXT:CMD ) ) AND (rel: ) ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"Batch\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\r\n\"🠆 Sublime Project Dirs\t【  stdir:  】\"\t0\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"child-file:ext:\"\"sublime-project\"\" sfda:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"stdir\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Shortcuts%3A%20Specific%20%09%28Macros%29%20%20%20%20\"\r\n\"(Separator)\"\t2\t\"Bookmarks\"\r\n\"🠊 Filtering: Show/Hide\t(Exclusions)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"【  dup:  】 Show duplicates\t:: file: !distinct:size;name;\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"file: !distinct:size;name;\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"dup\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\r\n\"【  hext:  】 Hide Extensions\t:: Unfrequent Usecases\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!NOHIGHLIGHT:($$$|ext:accdb|ext:adp|ext:ani|ext:aodl|ext:aux|ext:ax|ext:backup|ext:bak|ext:bak2|ext:bak3|ext:bck|ext:bck2|ext:bin|ext:bk|ext:bkf|ext:bkp|ext:body|ext:bup|ext:cab|ext:cache|ext:cat|ext:cc|ext:ccx|ext:cdmp|ext:cdp|ext:cdxml|ext:changecount|ext:chk|ext:class|ext:com|ext:csproj.user|ext:ctf|ext:dat|ext:db|ext:db-shm|ext:db-wal|ext:db3|ext:dcl|ext:dep|ext:diskcopy42|ext:diskcopy50|ext:diz|ext:dll|ext:dmp|ext:docbook|ext:dof|ext:ds_store|ext:dsp|ext:dspuser|ext:dvc|ext:edb|ext:err|ext:err2|ext:etl|ext:evtx|ext:ftg|ext:fts|ext:gid|ext:hds|ext:hxd|ext:hyb|ext:ibd|ext:idl|ext:imagemanifest|ext:inc|ext:inf_loc|ext:inline|ext:ipch|ext:ivt|ext:jar|ext:jcp|ext:jfm|ext:jtx|ext:lck|ext:ldb|ext:lock|ext:log|ext:log1|ext:log2|ext:log3|ext:map|ext:mdb|ext:mdmp|ext:mo|ext:mum|ext:ncb|ext:nwl|ext:od|ext:old|ext:otc|ext:parc|ext:part|ext:partial|ext:pb|ext:pck|ext:pdb|ext:pem|ext:pf|ext:pid|ext:pl|ext:pm|ext:pma|ext:pri|ext:prm|ext:prt|ext:pyc|ext:pyf|ext:qm|ext:qmlc|ext:rcache|ext:res|ext:res2|ext:resx|ext:sav|ext:scc|ext:sdf|ext:sds|ext:sid|ext:sin|ext:so|ext:spi1d|ext:spj|ext:sqlite|ext:srd-shm|ext:store|ext:suo|ext:suo2|ext:swo|ext:swp|ext:sym|ext:sys|ext:tcl|ext:tek|ext:temp|ext:thumb|ext:thumbsdb|ext:tjg|ext:tlog|ext:tmp|ext:tmpfile|ext:trc|ext:vscdb|ext:wbk|ext:wer|ext:wlog|ext:woff|ext:woff2|ext:xbf|ext:xin|ext:xlf|ext:~debris|ext:~lock|ext:~temp)\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"hext\"\t\t\"\"\r\n\"【  hsys:  】 Hide Locations\t:: System\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!NOHIGHLIGHT:(PATH:\"\"%WINDIR%\"\" OR PATH:\"\"%PROGRAMFILES%\"\" OR PATH:\"\"%PROGRAMFILES(X86)%\"\")\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"hsys\"\t\t\"\"\r\n\"【  hhash:  】 Hide Regex\t:: Hashes\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!NOHIGHLIGHT:(regex:path:\"\"\\{[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}\"\" OR regex:path:\"\"\\{?[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}?\"\" OR regex:path:\"\"[0-9A-Fa-f]{32}\"\" OR regex:path:\"\"S-1-5-21-\\d+-\\d+-\\d+-\\d+\"\")\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"hhash\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\r\n\"【  focus:  】 Show Relevant\t:: All\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"NOHIGHLIGHT:< hr1:hr2:hr3:hr4:hr5: ext:\"\"3dm|3ds|3g2|3gp|3gp2|3gpp|3mf|aac|abc|ac3|ai|aif|aifc|aiff|amr|amv|ape|asf|asx|au|avi|bat|bdmv|bik|blend|c4d|cda|cfg|cmd|cob|cpp|csv|d2v|dae|divx|doc|docm|docx|drc|dsa|dsm|dss|dsv|dts|dwf|dwg|dxf|eps|epub|evo|exe|f4v|fbx|fla|flac|flc|fli|flic|flv|gdoc|git|gitignore|glb|gltf|gsheet|gslides|hdmov|hrc|htm|html|iam|ifo|indd|ini|iob|ipj|ipt|it|iv|ivf|js|json|keynote|log|lwo|m1a|m1v|m2a|m2p|m2t|m2ts|m2v|m3u|m4a|m4b|m4p|m4v|ma|max|mb|mcr|md|mid|midi|mka|mkv|mod|mov|mp2|mp2v|mp3|mp4|mp4v|mpa|mpc|mpe|mpeg|mpg|mpls|mpp|mpv2|mpv4|ms|mse|msg|msi|msp|msu|mts|mxp|mzp|nss|nwc|nwd|nwf|obj|ogg|ogm|ogv|opml|opus|osg|pages|pc3|pdf|ply|pps|ppsx|ppt|pptx|ps1|psd|pss|pva|py|pyi|pyw|qt|ra|ram|rar|ratdvd|rm|rmi|rmm|rmvb|roq|rpm|rtf|scene|scn|scr|skp|smil|smk|snd|spc|sql|step|stl|stp|sublime|swf|tp|tpr|ts|txt|u3d|url|usd|usdc|usdz|vb|vbs|vdx|vob|voc|vp6|vrml|vsd|vsdm|vsdx|vss|vssm|vssx|vst|vstm|vstx|wav|weba|webm|wm|wma|wmp|wmv|wpd|wps|wrl|x3d|xlam|xls|xlsb|xlsm|xlsx|xltm|xltx|xm|xml|xsi|yml|zip|zpr\"\" OR regex:extension:\"\"^sublime.*\"\" OR regex:extension:\"\"^jinja.*\"\" >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"focus\"\t\t\"\"\r\n\"【  hide:  】 Hide Unrelevant\t:: All\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"hr1:hr2:hr3:hr4:hr5:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"hide\"\t\t\"\"\r\n\"【  hr1:  】 Hide Generated \t:: Perpetual\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!NOHIGHLIGHT:(PATH:\"\".dropbox.cache\"\" OR path:\"\"%localappdata%/**/Chrome\"\" OR path:\"\"Sync.Cache\"\" OR exact:ancestor-name:\"\"Sync.Config\"\" OR path:\"\"Sync.Logs\"\" OR path:\"\"Windows/Caches\"\" OR path:\"\"Dropbox/Update\"\" OR path:\"\"Windows/WebCache\"\" OR path:\"\"INetCache\"\" OR path:\"\"ProgramData/Microsoft/Network\"\" OR path:\"\"%localappdata%/**/state.db.changecount\"\" OR exact:name:\"\"Session-1.5a.json\"\" OR exact:name:\"\"FETCH_HEAD\"\")\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"hr1\"\t\t\"\"\r\n\"【  hr2:  】 Hide Unrelevant\t:: Paths\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!NOHIGHLIGHT:(PATH:\"\".dropbox.cache\"\")\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"hr2\"\t\t\"\"\r\n\"【  hr3:  】 Hide Unrelevant\t:: Files\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!NOHIGHLIGHT:(exact:name:\"\"store.bin\"\" OR path:\"\"%localappdata%/**/*.odlgz\"\")\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"hr3\"\t\t\"\"\r\n\"【  hr4:  】 Hide Unrelevant\t:: Folders\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!NOHIGHLIGHT:(exact:ancestor-name:\"\".git\"\" OR  exact:ancestor-name:\"\"__pycache__\"\")\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"hr4\"\t\t\"\"\r\n\"【  hr5:  】 Hide Unrelevant\t:: Extensions\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!nohighlight:(ext:|ext:\"\"ldb\"\"|exact:ext:\"\"log\"\"|exact:ext:\"\"tmp\"\"|ext:\"\"*~*\"\"|exact:ext:\"\"php\"\"|ext:\"\"store\"\"|ext:\"\"pma\"\"|ext:\"\"db3\"\"|ext:\"\"imagemanifest\"\"|ext:\"\"lock\"\"|ext:\"\"dat\"\"|ext:\"\"srd-shm\"\"|ext:\"\"db-wal\"\"|ext:\"\"rcache\"\") \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"hr5\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\r\n\"【  webprofiles:  】 firefox/chrome/brave\t:: firefox: chrome: brave:\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"< firefox: >|< chrome: >|< brave: >|< edge: >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"webprofiles\"\t\t\"\"\r\n\"【  chrome:  】 webprofiles: chrome\t:: path:\"\"Google/Chrome\"\"\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:\"\"Local/Google/Chrome\"\" OR path:\"\"%PROGRAMFILES%/Google/Chrome\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"chrome\"\t\t\"\"\r\n\"【  brave:  】 webprofiles: brave\t:: path:\"\"BraveSoftware/Brave-Browser\"\"\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:\"\"Local/BraveSoftware/Brave-Browser\"\" OR path:\"\"%PROGRAMFILES%/BraveSoftware/Brave-Browser\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"brave\"\t\t\"\"\r\n\"【  edge:  】 webprofiles: edge\t:: path:\"\"Microsoft/Edge\"\"\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:\"\"Microsoft/Edge\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"edge\"\t\t\"\"\r\n\"【  firefox:  】 webprofiles: firefox\t:: path:\"\"Roaming/Mozilla/Firefox/Profiles\"\"\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:\"\"Roaming/Mozilla/Firefox/Profiles\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"firefox\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Show%2FHide%09%28Exclusions%29%20%20%20%20\"\r\n\"🠊 Filtering: Meta \t(Macros)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"【  MYTYPES:  】\t:: Filtering on my set of filetypes\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"grp-parents-ex: <name:\"\".bat*\"\";\"\".bak*\"\";\"\".cmd*\"\";\"\".jinja-*\"\";\"\".sublime-*\"\";\"\".mcr*\"\";\"\".md*\"\";\"\".ms*\"\";\"\".nss*\"\";\"\".py*\"\";\"\".py*\"\";\"\".txt*\"\";\"\".txt*\"\";\"\".xml*\"\";|ext:3dm;3ds;3g2;3gp;3gp2;3gpp;3mf;7z;aac;abc;ac3;ai;aif;aifc;aiff;amr;amv;ani;ape;apng;asf;asx;au;avi;bat;bdmv;bik;blend;bmp;bpg;c4d;cda;cfg;cmd;cob;conf;config;cpp;csv;cur;curaproject;d2v;dae;divx;doc;docm;docx;drc;dsa;dsm;dss;dsv;dts;dwf;dwg;dxf;eps;epub;evo;exe;f4v;fbx;fla;flac;flc;fli;flic;flv;gdoc;gif;git;gitignore;glb;gltf;gsheet;gslides;h264;hdmov;heic;hrc;htm;html;iam;ico;ifo;indd;ini;iob;ipj;ipt;it;iv;ivf;jfi;jfif;jif;jpe;jpeg;jpg;js;json;keynote;lnk;log;lwo;m1a;m1v;m2a;m2p;m2t;m2ts;m2v;m3u;m4a;m4b;m4p;m4v;ma;max;mb;mcr;md;mid;midi;mka;mkv;mod;mov;mp2;mp2v;mp3;mp4;mp4v;mpa;mpc;mpe;mpeg;mpg;mpls;mpp;mpv2;mpv4;ms;mse;msg;msi;msp;msu;mtl;mts;mxp;mzp;nss;nwc;nwd;nwf;obj;ogg;ogm;ogv;opus;osg;pages;pc3;pcx;pdf;ply;png;pps;ppsx;ppt;pptx;ps1;psb;psd;pss;pva;py;pyi;pyw;qt;ra;ram;rar;ratdvd;raw;rle;rm;rmi;rmm;rmvb;roq;rpm;rtf;scene;scn;scr;skp;smil;smk;snd;spc;sql;step;stl;stp;sublime;svg;swf;tga;tif;tiff;tp;tpr;ts;txt;u3d;url;usd;usdc;usdz;vb;vbs;vdx;vob;voc;vp6;vrml;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;wav;weba;webm;webp;wm;wma;wmf;wmp;wmv;wpd;wps;wrl;x3d;xlam;xls;xlsb;xlsm;xlsx;xltm;xltx;xm;xml;xsi;yml;zip;zpr;|<dir: child-file:ext:ext:3dm;3ds;3g2;3gp;3gp2;3gpp;3mf;7z;aac;abc;ac3;ai;aif;aifc;aiff;amr;amv;ani;ape;apng;asf;asx;au;avi;bat;bdmv;bik;blend;bmp;bpg;c4d;cda;cfg;cmd;cob;conf;config;cpp;csv;cur;curaproject;d2v;dae;divx;doc;docm;docx;drc;dsa;dsm;dss;dsv;dts;dwf;dwg;dxf;eps;epub;evo;exe;f4v;fbx;fla;flac;flc;fli;flic;flv;gdoc;gif;git;gitignore;glb;gltf;gsheet;gslides;h264;hdmov;heic;hrc;htm;html;iam;ico;ifo;indd;ini;iob;ipj;ipt;it;iv;ivf;jfi;jfif;jif;jpe;jpeg;jpg;js;json;keynote;lnk;log;lwo;m1a;m1v;m2a;m2p;m2t;m2ts;m2v;m3u;m4a;m4b;m4p;m4v;ma;max;mb;mcr;md;mid;midi;mka;mkv;mod;mov;mp2;mp2v;mp3;mp4;mp4v;mpa;mpc;mpe;mpeg;mpg;mpls;mpp;mpv2;mpv4;ms;mse;msg;msi;msp;msu;mtl;mts;mxp;mzp;nss;nwc;nwd;nwf;obj;ogg;ogm;ogv;opus;osg;pages;pc3;pcx;pdf;ply;png;pps;ppsx;ppt;pptx;ps1;psb;psd;pss;pva;py;pyi;pyw;qt;ra;ram;rar;ratdvd;raw;rle;rm;rmi;rmm;rmvb;roq;rpm;rtf;scene;scn;scr;skp;smil;smk;snd;spc;sql;step;stl;stp;sublime;svg;swf;tga;tif;tiff;tp;tpr;ts;txt;u3d;url;usd;usdc;usdz;vb;vbs;vdx;vob;voc;vp6;vrml;vsd;vsdm;vsdx;vss;vssm;vssx;vst;vstm;vstx;wav;weba;webm;webp;wm;wma;wmf;wmp;wmv;wpd;wps;wrl;x3d;xlam;xls;xlsb;xlsm;xlsx;xltm;xltx;xm;xml;xsi;yml;zip;zpr;>\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"MYTYPES\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\r\n\"【  TMN:  】\t:: This Minute\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(date-modified:currentminute OR date-modified:lastminute OR date-created:currentminute) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"TMN\"\t\t\"\"\r\n\"【  THR:  】\t:: This Hour\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(date-modified:currenthour OR date-modified:lasthour OR date-created:currenthour ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"THR\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\r\n\"【  TD:  】\t:: Today\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(date-modified:currentday OR date-created:currentday ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"TD\"\t\t\"\"\r\n\"【  TW:  】\t:: This Week\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm:last7days|dc:last7days|date-modified:currentweek|date-created:currentweek\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"TW\"\t\t\"\"\r\n\"【  TM:  】\t:: This Month\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(date-modified:currentmonth OR date-created:currentmonth ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"TM\"\t\t\"\"\r\n\"【  TY:  】\t:: This Year\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(date-modified:currentyear OR date-created:currentyear ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"TY\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\r\n\"【  DA:  】\t:: Distinct All (Path/Name/Stem)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(distinct:name AND distinct:stem AND distinct:path)\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"DA\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\r\n\"【  DN:  】\t:: Distinct Name\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"DISTINCT:NAME\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"DN\"\t\t\"\"\r\n\"【  DP:  】\t:: Distinct Path\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"DISTINCT:PATH\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"DP\"\t\t\"\"\r\n\"【  DS:  】\t:: Distinct Stem\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"distinct:stem\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"DS\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\r\n\"【  DE:  】\t:: Distinct Extension\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"distinct:ext\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"DE\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\r\n\"【  EM:  】\t:: Empty\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"SIZE:<12BYTES\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"EM\"\t\t\"\"\r\n\"【  NE:  】\t:: Not Empty\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"SIZE:>12BYTES\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"NE\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\r\n\"【  DPT:  】\t:: Date Patterns\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Meta%20%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(REGEX:NAME:\\b\\d{4}-\\d{2}-\\d{2}\\b OR REGEX:NAME:\\b\\d{4}\\.\\d{2}\\.\\d{2}\\b OR REGEX:NAME:\\b\\d{2}-\\d{2}-\\d{4}\\b OR REGEX:NAME:\\b\\d{2}\\.\\d{2}\\.\\d{4}\\b ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"DPT\"\t\t\"\"\r\n\"🠊 Filtering: Inclusions\t(Macros)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"【  INC:  】\t:: Include Relevant Files/Folders\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Inclusions%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"((PATH:\"\"%APPDATA%\"\" OR PATH:\"\"%USERPROFILE%\\Desktop\"\" OR PATH:\"\"\\Dropbox\"\" OR PATH:\"\"\\Onedrive\"\" OR PATH:\"\"\\Progs\\\"\" OR PATH:\"\"PRJ\\GIT\\JHP\"\" OR PATH:\"\"Project\\\"\" OR PATH:\"\"Person*\"\" OR NOHIGHLIGHT:PATH:\"\"D:\"\" OR NOHIGHLIGHT:PATH:\"\"Sublime Text\"\" ) OR (EVERYTHING: OR NOHIGHLIGHT:PATH:\"\"Sublime Text\"\" OR REGEX:EXTENSION:\"\"SUBLIME.*\"\" OR NOHIGHLIGHT:REGEX:PATH:\"\"SUBLIME.*\"\" OR NOHIGHLIGHT:REGEX:NAME:\"\"SUBLIME.*\"\" ) AND SORT:\"\"DM;PATH;EXT;NAME;SIZE;\"\" )\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"INC\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Inclusions%09%28Macros%29%20%20%20%20\"\r\n\"【  II:  】\t:: Include (highlights relevant filetypes)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Inclusions%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"< EXT:\"\"3dm|3ds|3g2|3gp|3gp2|3gpp|3mf|aac|abc|ac3|ai|aif|aifc|aiff|amr|amv|ape|asf|asx|au|avi|bat|bdmv|bik|blend|c4d|cda|cfg|cmd|cob|cpp|csv|d2v|dae|divx|doc|docm|docx|drc|dsa|dsm|dss|dsv|dts|dwf|dwg|dxf|eps|epub|evo|exe|f4v|fbx|fla|flac|flc|fli|flic|flv|gdoc|git|gitignore|glb|gltf|gsheet|gslides|hdmov|hrc|htm|html|iam|ifo|indd|ini|iob|ipj|ipt|it|iv|ivf|js|json|keynote|log|lwo|m1a|m1v|m2a|m2p|m2t|m2ts|m2v|m3u|m4a|m4b|m4p|m4v|ma|max|mb|mcr|md|mid|midi|mka|mkv|mod|mov|mp2|mp2v|mp3|mp4|mp4v|mpa|mpc|mpe|mpeg|mpg|mpls|mpp|mpv2|mpv4|ms|mse|msg|msi|msp|msu|mts|mxp|mzp|nss|nwc|nwd|nwf|obj|ogg|ogm|ogv|opus|osg|pages|pc3|pdf|ply|pps|ppsx|ppt|pptx|ps1|psd|pss|pva|py|pyi|pyw|qt|ra|ram|rar|ratdvd|rm|rmi|rmm|rmvb|roq|rpm|rtf|scene|scn|scr|skp|smil|smk|snd|spc|sql|step|stl|stp|sublime|swf|tp|tpr|ts|txt|u3d|url|usd|usdc|usdz|vb|vbs|vdx|vob|voc|vp6|vrml|vsd|vsdm|vsdx|vss|vssm|vssx|vst|vstm|vstx|wav|weba|webm|wm|wma|wmp|wmv|wpd|wps|wrl|x3d|xlam|xls|xlsb|xlsm|xlsx|xltm|xltx|xm|xml|xsi|yml|zip|zpr\"\" OR REGEX:EXTENSION:\"\"^SUBLIME.*\"\" >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"ii\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Inclusions%09%28Macros%29%20%20%20%20\"\r\n\"【  REL:  】\t:: Relevant/Recent\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Inclusions%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(!(PATH:\"\"%WINDIR%\"\" OR PATH:\"\".git\\\"\" OR PATH:\"\"%LOCALAPPDATA%\\Sublime Text\\\"\" OR PATH:\"\"%LOCALAPPDATA%\\pip\\cache\"\" OR PATH:\"\"#\\\"\" OR PATH:\"\"%PROGRAMFILES%\"\" OR PATH:\"\"%PROGRAMFILES(X86)%\"\" ) AND !(PATH:\"\"Microsoft.VisualStudio.Setup.Service\\\"\" OR PATH:\"\"\\Windows\\WebCache\\\"\" OR PATH:\"\"*\\Chrome\\**\\Code Cache\"\" OR PATH:\"\"*\\Chrome\\**\\Default\\Network\"\" OR PATH:\"\"*\\Microsoft.*\\TempState\\BgTask_*\"\" OR PATH:\"\"*Chrome\\**File System\"\" OR PATH:\"\"*Chrome\\**IndexedDB\"\" OR PATH:\"\".dropbox.cache\"\" OR PATH:\"\"__pycache__\"\" OR PATH:\"\"AppData\\Local\\Dropbox\"\" OR PATH:\"\"Dropbox\\Local Storage\"\" OR PATH:\"\"Dropbox\\Update\"\" OR PATH:\"\"Google\\DriveFS\"\" OR PATH:\"\"INetCache\"\" OR PATH:\"\"Local\\Google\\Chrome\\User Data\"\" OR PATH:\"\"Local\\Packages\\Microsoft\"\" OR PATH:\"\"Local\\Packages\\MicrosoftWindows\"\" OR PATH:\"\"Microsoft\\**Edge\"\" OR PATH:\"\"MicrosoftWindows.Client.WebExperience\"\" OR PATH:\"\"Packages\\Microsoft.WindowsFeedbackHub_*\"\" OR PATH:\"\"ProgramData\\Microsoft\\Network\"\" OR PATH:\"\"qBittorrent\\BT_backup\"\" OR PATH:\"\"qBittorrent\\qBittorrent-data.ini\"\" OR PATH:\"\"SourceTree\\cache\\\"\" OR PATH:\"\"SourceTree.exe_Url_*\"\" ) AND !(NAME:\"\"FETCH_HEAD\"\" OR NAME:\"\"HxStore.hxd\"\" OR NAME:\"\"__init__\"\" OR NAME:\"\"AdobeARM.log\"\" OR NAME:\"\"DropboxUpdate.log*\"\" ) AND !(EXT:\"\"\"\" OR EXT:\"\"PYC\"\" OR EXT:\"\"LNK\"\" OR EXT:\"\"BIN\"\" OR EXT:\"\"STORE\"\" OR EXT:\"\"DB\"\" OR EXT:\"\"OTC\"\" OR EXT:\"\"ETL\"\" OR EXT:\"\"TOK\"\" OR EXT:\"\"CHK\"\" OR EXT:\"\"TBRES\"\" OR EXT:\"\"SQL*\"\" OR EXT:\"\"odl*\"\" OR EXT:\"\"DB\"\" OR EXT:\"\"db3\"\" OR EXT:\"\"fastresume\"\" OR EXT:\"\".automaticDestinations*\"\" ) AND !(REGEX:\"\"\\b[A-Fa-f0-9]{32}\\b\"\" OR REGEX:\"\"\\b[A-Fa-f0-9]{40}\\b\"\" OR REGEX:\"\"\\b[A-Fa-f0-9]{64}\\b\"\" OR REGEX:\"\"\\{[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}\"\" OR REGEX:\"\"\\b[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\b\"\" OR REGEX:\"\"^\\b[A-Fa-f0-9]{8}_[A-Fa-f0-9]{8}$\\b\"\" OR REGEX:\"\"^\\b[A-Fa-f0-9]{8}.[A-Fa-f0-9]{8}$\\b\"\" OR REGEX:\"\"^\\b[A-Fa-f0-9]{8}[A-Fa-f0-9]{8}$\\b\"\" OR REGEX:\"\"^S-1-5-21-\\d+-\\d+-\\d+-\\d+$\"\" ) ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"REL\"\t\t\"\"\r\n\"【  RC:  】\t:: Recent\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Inclusions%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"((NOHIGHLIGHT:(EXT:\"\"LNK\"\" DISTINCT:\"\"NAME\"\" DATE-ACCESSED:\"\"CURRENTDAY\"\" SORT:\"\"DATE-ACCESSED\"\") OR NOHIGHLIGHT:(EXT:\"\"LNK\"\" AND PATH:\"\"%APPDATA%\\Microsoft\\Windows\\Recent\"\" AND DEPTH:\"\"8\"\") ) SORT:\"\"DM\"\" ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"RC\"\t\t\"\"\r\n\"🠊 Filtering: Exclusions\t(Macros)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"【  EX1:  】\t:: Exclude Level 1 (safe substrings/patterns/paths)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Exclusions%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"NOHIGHLIGHT:!< (!PATH:\"\"PRJ/GIT\"\" !PATH:\"\"__SHELL__\"\" !REGEX:EXTENSION:\"\"^sublime.*\"\" !EXT:\"\"BAT\"\" !EXT:\"\"CMD\"\" !EXT:\"\"CSV\"\" !EXT:\"\"DOC\"\" !EXT:\"\"DOCX\"\" !EXT:\"\"EXE\"\" !EXT:\"\"GIT\"\" !EXT:\"\"GITIGNORE\"\" !EXT:\"\"JS\"\" !EXT:\"\"JSON\"\" !EXT:\"\"MCR\"\" !EXT:\"\"MD\"\" !EXT:\"\"MP3\"\" !EXT:\"\"MP4\"\" !EXT:\"\"MS\"\" !EXT:\"\"PDF\"\" !EXT:\"\"PSD\"\" !EXT:\"\"PY\"\" !EXT:\"\"RAR\"\" !EXT:\"\"SCENE\"\" !EXT:\"\"SUBLIME-*\"\" !EXT:\"\"URL\"\" !EXT:\"\"XLS\"\" !EXT:\"\"XLSX\"\" !EXT:\"\"ZIP\"\") REGEX:PATH:\"\"\\{[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}\"\" | REGEX:PATH:\"\"\\{?[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}?\"\" | REGEX:PATH:\"\"[0-9A-Fa-f]{32}\"\" | REGEX:PATH:\"\"S-1-5-21-\\d+-\\d+-\\d+-\\d+\"\" >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"EX1\"\t\t\"\"\r\n\"【  EX1H:  】\t:: Exclude Level 1 (With Highlight)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Exclusions%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!< (!PATH:\"\"PRJ/GIT\"\" !PATH:\"\"__SHELL__\"\" !REGEX:EXTENSION:\"\"^sublime.*\"\" !EXT:\"\"BAT\"\" !EXT:\"\"CMD\"\" !EXT:\"\"CSV\"\" !EXT:\"\"DOC\"\" !EXT:\"\"DOCX\"\" !EXT:\"\"EXE\"\" !EXT:\"\"GIT\"\" !EXT:\"\"GITIGNORE\"\" !EXT:\"\"JS\"\" !EXT:\"\"JSON\"\" !EXT:\"\"MCR\"\" !EXT:\"\"MD\"\" !EXT:\"\"MP3\"\" !EXT:\"\"MP4\"\" !EXT:\"\"MS\"\" !EXT:\"\"PDF\"\" !EXT:\"\"PSD\"\" !EXT:\"\"PY\"\" !EXT:\"\"RAR\"\" !EXT:\"\"SCENE\"\" !EXT:\"\"SUBLIME-*\"\" !EXT:\"\"URL\"\" !EXT:\"\"XLS\"\" !EXT:\"\"XLSX\"\" !EXT:\"\"ZIP\"\") REGEX:PATH:\"\"\\{[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}\"\" | REGEX:PATH:\"\"\\{?[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\\}?\"\" | REGEX:PATH:\"\"[0-9A-Fa-f]{32}\"\" | REGEX:PATH:\"\"S-1-5-21-\\d+-\\d+-\\d+-\\d+\"\" >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"EX1H\"\t\t\"\"\r\n\"【  EX2:  】\t:: Exclude Level 2 (empty dirs/files and missing lnk-targets)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Exclusions%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"NOHIGHLIGHT:!< (EMPTY: | (FILE: EXT:) | (EXT:\"\"lnk\"\" SIZE:>8b) | (EXT:\"\"lnk\"\" !exists($shortcut-target:) >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"EX2\"\t\t\"\"\r\n\"【  EX3:  】\t:: Exclude Level 3 (rarely used paths and extensions)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Exclusions%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!NOHIGHLIGHT:((PATH:\"\"%WINDIR%\"\" OR PATH:\"\"%PROGRAMFILES%\"\" OR PATH:\"\"%PROGRAMFILES(X86)%\"\" OR PATH:\"\".GIT/\"\" OR PATH:\"\"SourceTree.exe_Url_*\"\" OR PATH:\"\"*/Microsoft.*/TempState/BgTask_*\"\" OR PATH:\"\"*/Chrome/**/Default/Network\"\" OR PATH:\"\"*/Chrome/**/Code Cache\"\" OR PATH:\"\"*Chrome/**IndexedDB\"\" OR PATH:\"\"*Chrome/**File System\"\" OR PATH:\"\"Microsoft/**Edge\"\" OR PATH:\"\".dropbox.cache\"\" OR PATH:\"\"Google/DriveFS\"\" OR PATH:\"\"Packages/Microsoft.WindowsFeedbackHub_*\"\" OR PATH:\"\"qBittorrent/BT_backup\"\" OR PATH:\"\"qBittorrent/qBittorrent-data.ini\"\" OR PATH:\"\"AppData/Local/Dropbox\"\" OR PATH:\"\"ProgramData/Microsoft/Network\"\" OR PATH:\"\"INetCache\"\" OR PATH:\"\"__pycache__\"\" OR PATH:\"\"Dropbox/Update\"\" OR EXT:\"\"BIN\"\" OR EXT:\"\"CHK\"\" OR EXT:\"\"DB\"\" OR EXT:\"\"DLL\"\" OR EXT:\"\"ETL\"\" OR EXT:\"\"PF\"\" OR EXT:\"\"PYC\"\" OR EXT:\"\"SQL*\"\" OR EXT:\"\"STANDE\"\" OR EXT:\"\"TBRES\"\" OR EXT:\"\"TOK\"\" OR EXT:\"\"TOK\"\" OR EXT:\"\"fastresume\"\" OR EXT:\"\".automaticDestinations*\"\" ) !REGEX:EXTENSION:\"\"^sublime.*\"\" AND !EXT:\"\"BAT\"\" AND !EXT:\"\"CMD\"\" AND !EXT:\"\"CSV\"\" AND !EXT:\"\"DOC\"\" AND !EXT:\"\"DOCX\"\" AND !EXT:\"\"EXE\"\" AND !EXT:\"\"CFG\"\" AND !EXT:\"\"GIT\"\" AND !EXT:\"\"GITIGNORE\"\" AND !EXT:\"\"JSON\"\" AND !EXT:\"\"MAX\"\" AND !EXT:\"\"BLEND\"\" AND !EXT:\"\"MCR\"\" AND !EXT:\"\"MD\"\" AND !EXT:\"\"MP3\"\" AND !EXT:\"\"MP4\"\" AND !EXT:\"\"MS\"\" AND !EXT:\"\"PDF\"\" AND !EXT:\"\"PSD\"\" AND !EXT:\"\"PY\"\" AND !EXT:\"\"RAR\"\" AND !EXT:\"\"SCENE\"\" AND !EXT:\"\"URL\"\" AND !EXT:\"\"XLS\"\" AND !EXT:\"\"XLSX\"\" AND !EXT:\"\"ZIP\"\" ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"EX3\"\t\t\"\"\r\n\"【  EX3H:  】\t:: Exclude Level 3 (With Highlight)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Exclusions%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!((PATH:\"\"%WINDIR%\"\" OR PATH:\"\"%PROGRAMFILES%\"\" OR PATH:\"\"%PROGRAMFILES(X86)%\"\" OR PATH:\"\".GIT/\"\" OR PATH:\"\"SourceTree.exe_Url_*\"\" OR PATH:\"\"*/Microsoft.*/TempState/BgTask_*\"\" OR PATH:\"\"*/Chrome/**/Default/Network\"\" OR PATH:\"\"*/Chrome/**/Code Cache\"\" OR PATH:\"\"*Chrome/**IndexedDB\"\" OR PATH:\"\"*Chrome/**File System\"\" OR PATH:\"\"Microsoft/**Edge\"\" OR PATH:\"\".dropbox.cache\"\" OR PATH:\"\"Google/DriveFS\"\" OR PATH:\"\"Packages/Microsoft.WindowsFeedbackHub_*\"\" OR PATH:\"\"qBittorrent/BT_backup\"\" OR PATH:\"\"qBittorrent/qBittorrent-data.ini\"\" OR PATH:\"\"AppData/Local/Dropbox\"\" OR PATH:\"\"ProgramData/Microsoft/Network\"\" OR PATH:\"\"INetCache\"\" OR PATH:\"\"__pycache__\"\" OR PATH:\"\"Dropbox/Update\"\" OR EXT:\"\"BIN\"\" OR EXT:\"\"CHK\"\" OR EXT:\"\"DB\"\" OR EXT:\"\"DLL\"\" OR EXT:\"\"ETL\"\" OR EXT:\"\"PF\"\" OR EXT:\"\"PYC\"\" OR EXT:\"\"SQL*\"\" OR EXT:\"\"STANDE\"\" OR EXT:\"\"TBRES\"\" OR EXT:\"\"TOK\"\" OR EXT:\"\"TOK\"\" OR EXT:\"\"fastresume\"\" OR EXT:\"\".automaticDestinations*\"\" ) !REGEX:EXTENSION:\"\"^sublime.*\"\" AND !EXT:\"\"BAT\"\" AND !EXT:\"\"CMD\"\" AND !EXT:\"\"CSV\"\" AND !EXT:\"\"DOC\"\" AND !EXT:\"\"DOCX\"\" AND !EXT:\"\"EXE\"\" AND !EXT:\"\"CFG\"\" AND !EXT:\"\"GIT\"\" AND !EXT:\"\"GITIGNORE\"\" AND !EXT:\"\"JSON\"\" AND !EXT:\"\"MAX\"\" AND !EXT:\"\"BLEND\"\" AND !EXT:\"\"MCR\"\" AND !EXT:\"\"MD\"\" AND !EXT:\"\"MP3\"\" AND !EXT:\"\"MP4\"\" AND !EXT:\"\"MS\"\" AND !EXT:\"\"PDF\"\" AND !EXT:\"\"PSD\"\" AND !EXT:\"\"PY\"\" AND !EXT:\"\"RAR\"\" AND !EXT:\"\"SCENE\"\" AND !EXT:\"\"URL\"\" AND !EXT:\"\"XLS\"\" AND !EXT:\"\"XLSX\"\" AND !EXT:\"\"ZIP\"\" ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"EX3H\"\t\t\"\"\r\n\"【  EX4:  】\t:: Exclude Level 4 (hard enforcement, no considerations)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Exclusions%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!NOHIGHLIGHT:((REGEX:EXTENSION:\"\"^\\d{3,}$\"\" OR REGEX:EXTENSION:\"\"^r\\d{2,}\"\" OR PATH:\"\"__pycache__\"\" OR PATH:\"\"venv/Lib/site-packages\"\" OR PATH:\"\"C:/$WINDOWS.~BT\"\" OR PATH:\"\"C:/$WinREAgent\"\" OR PATH:\"\"C:/ProgramData/Autodesk/IDSDK\"\" OR PATH:\"\"%APPDATA%/Code/User/History/\"\" OR PATH:\"\"/Google/Chrome/User Data/Default/Sync Data/LevelDB\"\" OR PATH:\"\"%LOCALAPPDATA%/Microsoft/Windows/Caches\"\" OR EXT:\"\"TMP\"\" OR EXT:\"\"STORE\"\" OR EXT:\"\"LDB\"\" OR EXT:\"\"PMA\"\" OR EXT:\"\"DB3\"\" OR EXT:\"\"IMAGEMANIFEST\"\" OR EXT:\"\"LOCK\"\" OR EXT:\"\"DAT\"\" OR EXT:\"\"SRD-SHM\"\" OR EXT:\"\"DB-WAL\"\" OR EXT:\"\"RCACHE\"\" OR EXT:\"\"OLD\"\" OR EXT:\"\"LOG\"\" ) ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"EX4\"\t\t\"\"\r\n\"【  EX4H:  】\t:: Exclude Level 4 (With Highlight)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Exclusions%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!((REGEX:EXTENSION:\"\"^\\d{3,}$\"\" OR REGEX:EXTENSION:\"\"^r\\d{2,}\"\" OR PATH:\"\"__pycache__\"\" OR PATH:\"\"venv/Lib/site-packages\"\" OR PATH:\"\"C:/$WINDOWS.~BT\"\" OR PATH:\"\"C:/$WinREAgent\"\" OR PATH:\"\"C:/ProgramData/Autodesk/IDSDK\"\" OR PATH:\"\"%APPDATA%/Code/User/History/\"\" OR PATH:\"\"/Google/Chrome/User Data/Default/Sync Data/LevelDB\"\" OR PATH:\"\"%LOCALAPPDATA%/Microsoft/Windows/Caches\"\" OR EXT:\"\"TMP\"\" OR EXT:\"\"STORE\"\" OR EXT:\"\"LDB\"\" OR EXT:\"\"PMA\"\" OR EXT:\"\"DB3\"\" OR EXT:\"\"IMAGEMANIFEST\"\" OR EXT:\"\"LOCK\"\" OR EXT:\"\"DAT\"\" OR EXT:\"\"SRD-SHM\"\" OR EXT:\"\"DB-WAL\"\" OR EXT:\"\"RCACHE\"\" OR EXT:\"\"OLD\"\" OR EXT:\"\"LOG\"\" ) ) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"EX4H\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Exclusions%09%28Macros%29%20%20%20%20\"\r\n\"【  EE:  】\t:: Exclude Extra (not typically of interest)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Exclusions%09%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!< childfoldercount:0 > !< size:<24bytes > !< ext:\"\"vscdb|jcp|jtx|nwl|backup|edb|jfm|hxd|hds|db3|pri\"\" > !< case:exact:name:\"\"Local\"\"|\"\"globalStorage\"\"|\"\"Auto Save Session.sublime_session\"\"|\"\"Exact Quick Find.sublime-settings\"\" > !< path:\"\"%LOCALAPPDATA%\"\"|\"\"%TEMP%\"\" ext:\"\"tmp\"\"|\"\"aodl\"\"|\"\"OD\"\" > !< path:\"\"%TEMP%\"\" regex:\"\"^[0-9a-z]{8}\\.[a-z0-9]{3}$\"\" > !< path:\"\"*/.vscode/extensions/*/dist\"\"|\"\"%TEMP%/CreativeCloud\"\"|\"\"%TEMP%/*.tek/\"\"|\"\"CCX Welcome\"\" > !< path:\"\"/Local/pip\"\"|\"\"/stdlib/\"\"|\"\"Google/Chrome/User Data\"\"|\"\"Code/User/Sync\"\"|\"\"Code/logs\"\"|\"\"Sublime Text/Index\"\"|\"\"FancyZones/Logs\"\" > !< path:\"\"/Python*/Lib\"\"|\"\"/usr/lib\"\"|\"\"/local/Lib\"\"|\"\"/site-packages\"\"|\"\"/python/data\"\" > !< path:\"\"microsoft.\"\"|\"\".microsoft\"\"|\"\"Microsoft/Windows\"\"|\"\"Microsoft/Edge\"\" >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"ee\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks\"\r\n\"🠊 Filtering: Groups\t(Include/Exclude)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"【  grp-ex:  】\t:: Groups (Exclusions)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Groups%09%28Include%2FExclude%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"nohighlight:!< (name:\"\".py*\"\"|!ext:\"\"ms|py|nss|bat|cmd|mcr|mse\"\" path:\"\"Cache\"\")|(!ext:\"\"py|json|config\"\" path:\"\"Atlassian/SourceTree\"\")|(!ext:\"\"py|json|config\"\" path:\"\"Sync.Config\"\")|path:\"\"Windows Defender\"\"|path:\"\"%WINDIR%\"\"|path:\"\".dropbox.cache\"\"|path:\"\"appcompat\"\"|path:\"\"Autodesk/ADPSDK\"\"|path:\"\"Autodesk/ODIS\"\"|path:\"\"C:/$WinREAgent\"\"|path:\"\"C:/Windows/Logs\"\"|path:\"\"Chrome/User Data/Safe Browsing\"\"|path:\"\"customDestinations-ms\"\"|path:\"\"GoogleUpdater\"\"|path:\"\"inetcache\"\"|path:\"\"leveldb\"\"|path:\"\"Local/Google/Chrome\"\"|path:\"\"Local/Microsoft/Windows/History\"\"|path:\"\"Logs/SystemIndex\"\"|path:\"\"loguploadersettings*.ini\"\" |path:\"\"Microsoft/Windows/WebCache\"\"|path:\"\"OneDrive/logs\"\"|path:\"\"Packages/Microsoft.Windows\"\"|path:\"\"programdata/microsoft/network\"\"|path:\"\"ProgramData/Oracle/Java\"\"|path:\"\"ProgramData/USOPrivate\"\"|path:\"\"Recent/AutomaticDestinations\"\"|path:\"\"sublimetext/exe/Data/Cache\"\"|path:\"\"sublimetext/exe/Data/Crash Reports\"\"|path:\"\"sublimetext/exe/Data/Index\"\"|path:\"\"sublimetext/exe/Data/Lib\"\"|path:\"\"sublimetext/exe/Data/Package Storage\"\"|path:\"\"sublimetext/exe/Lib\"\"|path:\"\"sync.cache\"\"|path:\"\"Sync.Logs\"\"|path:\"\"Windows/Prefetch\"\"|path:\"\"WindowsApps/MicrosoftTeams\"\"|path:\"\"$WINDOWS.~BT\"\"|path:\"\"Windows/AppRepository\"\"|path:\"\"WindowsApps\"\"|path:\"\"Acrobat/WebResources\"\" > nohighlight:!< (EXT:)|(EXT:\"\"crx*\"\")|EXT:\"\"accdb|adp|ani|aodl|aux|ax|backup|bak|bak2|bak3|bck|bck2|bin|bin64|bk|bkf|bkp|body|bup|cab|cache|cat|cc|ccx|cdmp|cdp|cdpresource|cdxml|changecount|chk|class|com|csproj.user|ctf|cubin|dat|db|db-shm|db-wal|db3|db3|dcl|dep|diskcopy42|diskcopy50|diz|dll|dmp|docbook|dof|ds_store|dsp|dspuser|dvc|edb|edb|err|err2|etl|evtx|fatbin|ftg|fts|gid|hds|hds|hxd|hxd|hyb|ibd|ico|idl|imagemanifest|inc|inf_loc|inline|ipch|ivt|jar|jcp|jcp|jfm|jfm|jtx|jtx|lck|ldb|leveldb|lock|log|log1|log2|log3|map|mdb|mdmp|mmap|mo|mum|ncb|nwl|nwl|od|odlgz|old|otc|parc|part|partial|pb|pck|pdb|pem|pf|pid|pl|pm|pma|pri|pri|prm|prt|ptx|pyc|pyf|qm|qmlc|rcache|res|res2|resx|sav|scc|sdb|sdf|sds|sid|sin|so|spi1d|spj|sqlite|srd-shm|store|sublime-package|pack|sublime_session|sublime-workspace|suo|suo2|swo|swp|sym|sys|tbres|tcl|tek|temp|thumb|thumbsdb|tjg|tlog|tmp|tmpfile|trc|vscdb|wae|lnk|wbk|wer|wlog|woff|woff2|xbf|xin|xlf|zsh|zfe|~debris|~lock|~temp\"\" > nohighlight:!< EXACT:ANCESTOR-NAME:\"\".git\"\"|EXACT:ANCESTOR-NAME:\"\"Crash Reports\"\"|EXACT:ANCESTOR-NAME:\"\".backups\"\"|EXACT:ANCESTOR-NAME:\"\"venv\"\"|EXACT:ANCESTOR-NAME:\"\"node_modules\"\" > nohighlight:!< file: CASE:EXACT:NAME:\"\"Exact Quick Find.sublime-settings\"\"|CASE:EXACT:NAME:\"\"globalStorage\"\"|CASE:EXACT:NAME:\"\"Local\"\"|CASE:EXACT:NAME:\"\"Network Persistent State\"\" > nohighlight:!< \"\"automaticDestinations-ms\"\"|\"\"Indexed*DB\"\"|\"\"microsoft*.xml\"\"|\"\"mpcache\"\"|\"\"PcaGeneralDb\"\"|\"\"Url*.store.*\"\" > nohighlight:!< (FILE: SIZE:<24bytes)|(DIR: CHILD-COUNT:0)|(empty: child-file-count-from-disk:0 child-folder-count-from-disk:0 !attrib:L) > nohighlight:!( REGEX:\"\"^\\d{1,30}$\"\" | REGEX:EXTENSION:\"\"^\\d{0,}$\"\" ) nohighlight:!< !ext:\"\"7z|bat|cmd|csv|doc|docx|exe|git|json|mcr|md|mp3|mp4|ms|nss|pdf|psd|py|rar|rar|scene|url|xls|xlsx|zip\"\" GUID:|HASH:|MD5:|SID: >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"grp-ex\"\t\t\"\"\r\n\"【  grp-ex-safe:  】\t:: Groups (Exclusions)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Groups%09%28Include%2FExclude%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"name:\"\".bat*\"\";\"\".cmd*\"\";\"\".mcr*\"\";\"\".md*\"\";\"\".ms*\"\";\"\".mse*\"\";\"\".nss*\"\";\"\".py*\"\";\"\".py*\"\";\"\".txt*\"\";\"\".txt*\"\";\"\".xml*\"\";|grp-ex: (sort:dm;path; secondary-sort:!isfolder:) \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"grp-ex-safe\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Filtering%3A%20Groups%09%28Include%2FExclude%29%20%20%20%20\"\r\n\"【  grp-paths-ex:  】\t:: Path Groups (Exclusions)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Groups%09%28Include%2FExclude%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"nohighlight:!< (!ext:\"\"ms|py|nss|bat|cmd|mcr|mse\"\" path:\"\"Cache\"\")|(!ext:\"\"py|json|config\"\" path:\"\"Atlassian/SourceTree\"\")|(!ext:\"\"py|json|config\"\" path:\"\"Sync.Config\"\")|path:\"\"Windows Defender\"\"|path:\"\"%WINDIR%\"\"|path:\"\".dropbox.cache\"\"|path:\"\"appcompat\"\"|path:\"\"Autodesk/ADPSDK\"\"|path:\"\"Autodesk/ODIS\"\"|path:\"\"C:/$WinREAgent\"\"|path:\"\"C:/Windows/Logs\"\"|path:\"\"Chrome/User Data/Safe Browsing\"\"|path:\"\"customDestinations-ms\"\"|path:\"\"GoogleUpdater\"\"|path:\"\"inetcache\"\"|path:\"\"leveldb\"\"|path:\"\"Local/Google/Chrome\"\"|path:\"\"Local/Microsoft/Windows/History\"\"|path:\"\"Logs/SystemIndex\"\"|path:\"\"loguploadersettings*.ini\"\" |path:\"\"Microsoft/Windows/WebCache\"\"|path:\"\"OneDrive/logs\"\"|path:\"\"Packages/Microsoft.Windows\"\"|path:\"\"programdata/microsoft/network\"\"|path:\"\"ProgramData/Oracle/Java\"\"|path:\"\"ProgramData/USOPrivate\"\"|path:\"\"Recent/AutomaticDestinations\"\"|path:\"\"sublimetext/exe/Data/Cache\"\"|path:\"\"sublimetext/exe/Data/Crash Reports\"\"|path:\"\"sublimetext/exe/Data/Index\"\"|path:\"\"sublimetext/exe/Data/Lib\"\"|path:\"\"sublimetext/exe/Data/Package Storage\"\"|path:\"\"sublimetext/exe/Lib\"\"|path:\"\"sync.cache\"\"|path:\"\"Sync.Logs\"\"|path:\"\"Windows/Prefetch\"\"|path:\"\"WindowsApps/MicrosoftTeams\"\"|path:\"\"$WINDOWS.~BT\"\"|path:\"\"Windows/AppRepository\"\"|path:\"\"WindowsApps\"\"|path:\"\"Acrobat/WebResources\"\" >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"grp-paths-ex\"\t\t\"\"\r\n\"【  grp-exts-ex:  】\t:: Extension Groups (Exclusions)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Groups%09%28Include%2FExclude%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"nohighlight:!< (EXT:)|(EXT:\"\"crx*\"\")|EXT:\"\"accdb|adp|ani|aodl|aux|ax|backup|bak|bak2|bak3|bck|bck2|bin|bin64|bk|bkf|bkp|body|bup|cab|cache|cat|cc|ccx|cdmp|cdp|cdpresource|cdxml|changecount|chk|class|com|csproj.user|ctf|cubin|dat|db|db-shm|db-wal|db3|db3|dcl|dep|diskcopy42|diskcopy50|diz|dll|dmp|docbook|dof|ds_store|dsp|dspuser|dvc|edb|edb|err|err2|etl|evtx|fatbin|ftg|fts|gid|hds|hds|hxd|hxd|hyb|ibd|ico|idl|imagemanifest|inc|inf_loc|inline|ipch|ivt|jar|jcp|jcp|jfm|jfm|jtx|jtx|lck|ldb|leveldb|lock|log|log1|log2|log3|map|mdb|mdmp|mmap|mo|mum|ncb|nwl|nwl|od|odlgz|old|otc|parc|part|partial|pb|pck|pdb|pem|pf|pid|pl|pm|pma|pri|pri|prm|prt|ptx|pyc|pyf|qm|qmlc|rcache|res|res2|resx|sav|scc|sdb|sdf|sds|sid|sin|so|spi1d|spj|sqlite|srd-shm|store|sublime-package|pack|sublime_session|sublime-workspace|suo|suo2|swo|swp|sym|sys|tbres|tcl|tek|temp|thumb|thumbsdb|tjg|tlog|tmp|tmpfile|trc|vscdb|wae|lnk|wbk|wer|wlog|woff|woff2|xbf|xin|xlf|zsh|zfe|~debris|~lock|~temp\"\" >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"grp-exts-ex\"\t\t\"\"\r\n\"【  grp-parents-ex:  】\t:: Parent Groups (Exclusions)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Groups%09%28Include%2FExclude%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"nohighlight:!( EXACT:ANCESTOR-NAME:\"\".git\"\"|EXACT:ANCESTOR-NAME:\"\"Crash Reports\"\"|EXACT:ANCESTOR-NAME:\"\".backups\"\"|EXACT:ANCESTOR-NAME:\"\"venv\"\"|EXACT:ANCESTOR-NAME:\"\"node_modules\"\" )\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"grp-parents-ex\"\t\t\"\"\r\n\"【  grp-names-ex:  】\t:: File/dirnames (Exclusions)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Groups%09%28Include%2FExclude%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"nohighlight:!( file: CASE:EXACT:NAME:\"\"Exact Quick Find.sublime-settings\"\"|CASE:EXACT:NAME:\"\"globalStorage\"\"|CASE:EXACT:NAME:\"\"Local\"\"|CASE:EXACT:NAME:\"\"Network Persistent State\"\" )\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"grp-names-ex\"\t\t\"\"\r\n\"【  grp-strings-ex:  】\t:: (Sub)Strings (Exclusions)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Groups%09%28Include%2FExclude%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"nohighlight:!( \"\"automaticDestinations-ms\"\"|\"\"Indexed*DB\"\"|\"\"microsoft*.xml\"\"|\"\"mpcache\"\"|\"\"PcaGeneralDb\"\"|\"\"Url*.store.*\"\" )\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"grp-strings-ex\"\t\t\"\"\r\n\"【  grp-meta-ex:  】\t:: Meta Group (Exclusions)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Groups%09%28Include%2FExclude%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"nohighlight:!( !ext:\"\"gitignore|ms|py|nss|bat|cmd|mcr|mse\"\" (FILE: SIZE:<24bytes)|(DIR: CHILD-COUNT:0)|(empty: child-file-count-from-disk:0 child-folder-count-from-disk:0 !attrib:L) )\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"grp-meta-ex\"\t\t\"\"\r\n\"【  grp-regex-ex:  】\t:: Regex Group (Exclusions)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Groups%09%28Include%2FExclude%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"nohighlight:!( REGEX:\"\"^\\d{1,30}$\"\" | REGEX:EXTENSION:\"\"^\\d{0,}$\"\" )\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"grp-regex-ex\"\t\t\"\"\r\n\"【  grp-patterns-ex:  】\t:: Regex Group (Exclusions)\"\t0\t\"Bookmarks/🠊%20Filtering%3A%20Groups%09%28Include%2FExclude%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"< !ext:\"\"7z|bat|cmd|csv|doc|docx|exe|git|json|mcr|md|mp3|mp4|ms|nss|pdf|psd|py|rar|rar|scene|url|xls|xlsx|zip\"\" GUID:|HASH:|MD5:|SID: >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"grp-patterns-ex\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks\"\r\n\"🠊 Functions: Commands \t(Custom)     \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Functions%3A%20Commands%20%09%28Custom%29%20%20%20%20%20\"\r\n\"function with variable input #1 \t:: plus:<variable>\"\t0\t\"Bookmarks/🠊%20Functions%3A%20Commands%20%09%28Custom%29%20%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"regex:\"\"^([^\\+]*\\+[^\\+]*){variable:}$\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"plus<variable>\"\t\t\"\"\r\n\"function with variable input #2 \t:: underscore:<variable>\"\t0\t\"Bookmarks/🠊%20Functions%3A%20Commands%20%09%28Custom%29%20%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"regex:\"\"^(?:[^_]*_){variable:,}[^_]*$\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"underscore<variable>\"\t\t\"\"\r\n\"🠊 Actions: Clean Up Space \t(Categories)     \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"log: clean up space\"\t0\t\"Bookmarks/🠊%20Actions%3A%20Clean%20Up%20Space%20%09%28Categories%29%20%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"< !exact:ancestor-name:\"\".git\"\" > < !EX1: AND !EX2: AND !EX3: AND !EX4: > < size:>100mb >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"size:<1mb !dupe:size;sha256 sort:dm gitignore\"\t0\t\"Bookmarks/🠊%20Actions%3A%20Clean%20Up%20Space%20%09%28Categories%29%20%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"size:<1mb !dupe:size;sha256 sort:dm gitignore\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"cleanup space - c: parent-size:>1GB parent-size:<30GB !appdata: file:\"\t0\t\"Bookmarks/🠊%20Actions%3A%20Clean%20Up%20Space%20%09%28Categories%29%20%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"c: parent-size:>1GB parent-size:<30GB !appdata: file:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: depth:<4  size:>2500mb\"\t0\t\"Bookmarks/🠊%20Actions%3A%20Clean%20Up%20Space%20%09%28Categories%29%20%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"depth:<4  size:>2500mb\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks\"\r\n\"🠊 Saved Searches\t:: (Macros)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"【  singlefile:  】\t:: Saved Websites (htm: content:SingleFile !dup:)\"\t0\t\"Bookmarks/🠊%20Saved%20Searches%09%3A%3A%20%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"htm: content:SingleFile !dup:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"singlefile\"\t\t\"\"\r\n\"【  cfl:  】\t:: sync-conflicts (text: sync conf size:<1mb dup:)\"\t0\t\"Bookmarks/🠊%20Saved%20Searches%09%3A%3A%20%28Macros%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"text: sync conf size:<1mb dup: \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"cfl\"\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks\"\r\n\"🠊 Unsorted\t:: (LOGS)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"dirs-accessed: this month\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:\"\"%appdata%\\Microsoft\\Windows\\Recent\"\" distinct:name date-accessed:currentmonth columns:da;ext;name;shortcut-target;type sort:da exists($shortcut-target:) !regex:shortcut-target:\"\"\\.([^.]+)$\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"dirs-accessed - this year\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:\"\"%appdata%\\Microsoft\\Windows\\Recent\"\" date-accessed:currentyear columns:da;ext;name;shortcut-target;type sort:da exists($shortcut-target:) !regex:shortcut-target:\"\"\\.([^.]+)$\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"all recently access (existing) directories\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:\"\"%appdata%\\Microsoft\\Windows\\Recent\"\" columns:da;ext;name;shortcut-target;type sort:da exists($shortcut-target:) !regex:shortcut-target:\"\"\\.([^.]+)$\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"date-range\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm:2023.08.01-2023.09.01 sort:dm-ascending\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"regex:content\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:py regex:content:\"\".*assi.*dyn.*mat*.\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"** Sort Depth Special **\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"sort:folder-size;path;folder-depth-ascending;size\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: dir: dp: sort:parent-size;path;folder-depth-ascending;size \"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dir: dp: sort:parent-size;path;folder-depth-ascending;size \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"chatgpt folders\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"htm: content:\"\"chat.openai\"\" !name:bookma parent-name:\"\"_chatgpt\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"chatgpt unsorted\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"htm: size:>100kb content:\"\"SingleFile\"\" content:\"\"chat.openai\"\" !parent-name:\"\"_chatgpt\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"path:mobil ancestor-name:_yvonne\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:mobil ancestor-name:_yvonne\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"path:mobil ancestor-name:_yvonne dupe:size;sha256  dupe-sort:dm: \"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:mobil ancestor-name:_yvonne dupe:size;sha256  dupe-sort:dm: \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"path:mobil ancestor-name:_yvonne dupe:size;name dupe-sort:dm: \"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:mobil ancestor-name:_yvonne dupe:size;name dupe-sort:dm: \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"path:mobil ancestor-name:_yvonne distinct:name;size\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:mobil ancestor-name:_yvonne distinct:name;size\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: path:sync\\jorn name:dreamstudio dupe:size;sha256\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:sync\\jorn name:dreamstudio dupe:size;sha256\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: path:sync\\jorn name:dreamstudio dn: sort:dm\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:sync\\jorn name:dreamstudio dn: sort:dm\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: ee: content:\"\"=================================\"\" content:\"\"kl.\"\" !htm: !ext:js  !ext:json !ext:csv\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ee: content:\"\"=================================\"\" content:\"\"kl.\"\" !htm: !ext:js  !ext:json !ext:csv\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: all recent files (1 hour)\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(DATE-MODIFIED:CURRENTHOUR OR DATE-MODIFIED:LASTHOUR) file:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: sort:name-length\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"PATH:\"\"C:\\Users\\<USER>\\Sync\\JORN\\Venners prosjekter\\Kim - Ringerike Landskap\\VectorizeLogo\"\"  sort:name-length\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: dir:|psd:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dir:|psd:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: c: dir: dp: sort:parent-size;path;folder-depth-ascending;size !dupe:size\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"c: dir: dp: sort:parent-size;path;folder-depth-ascending;size !dupe:size\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: child-file:ext:\"\"sublime-project\"\" sfda:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"child-file:ext:\"\"sublime-project\"\" sfda:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: sort:column1: group-colors:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"sort:column1: group-colors:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"dm:>=#format-filetime:< #get-date-modified:<\"\"%1\"\">,01/01/2021 >\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm:>=#format-filetime:< #get-date-modified:<\"\"%1\"\">,01/01/2021 >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: group-colors:depth|dir:|file: sort:dm secondary-sort:!isfolder:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"group-colors:depth|dir:|file: sort:dm secondary-sort:!isfolder:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: a-label:=\"\"Hours Since Modified\"\" addcolumn:a a:=(now()-$dm:)/36000000000 sort:a\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"a-label:=\"\"Hours Since Modified\"\" addcolumn:a a:=(now()-$dm:)/36000000000 sort:a\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: group-colors:column2:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"group-colors:column2:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: descendant-file:ext:subl*pro* dm:>6days\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"descendant-file:ext:subl*pro* dm:>6days\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: powertoys folders\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!EXT:TXT PATH:\"\"Local\\Microsoft\\PowerToys\"\" OR (PATH:\"\"PRJ\"\" PATH:PowerToys) sort:parent-date-modified;dm secondary-sort:!isfolder:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"todo: add regex-column to query content properly regex:content:\"\".*win.*10\"\" !ext:log\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"regex:content:\"\".*win.*10\"\" !ext:log\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"TODO: SEARCH CONTENT: regex:content:\"\".*win.*10\"\" !ext:log column1:=regmatch0:  columns:dm;column1;size;ext;name;path;name-frequency;extension-frequency;run-count;\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"regex:content:\"\".*win.*10\"\" !ext:log column1:=regmatch0:  columns:dm;column1;size;ext;name;path;name-frequency;extension-frequency;run-count;\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: regex:content:\"\".* win.*10\"\" !ext:log column1:=regmatch0:  columns:dm;size;ext;name;column1;path;name-frequency;extension-frequency;run-count; \"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"regex:content:\"\".* win.*10\"\" !ext:log column1:=regmatch0:  columns:dm;size;ext;name;column1;path;name-frequency;extension-frequency;run-count; \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"regex:content:\"\"\\bif.* win.*\\d{2}\"\" !ext:log column1:=regmatch0:  columns:dm;size;ext;name;column1;path;name-frequency;extension-frequency;run-count; \"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"regex:content:\"\"\\bif.* win.*\\d{2}\"\" !ext:log column1:=regmatch0:  columns:dm;size;ext;name;column1;path;name-frequency;extension-frequency;run-count; \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: ext:gi* regex:content:\"\".*filte.*\"\" !ext:log column1:=regmatch0:  columns:dm;size;ext;name;column1;path;name-frequency;extension-frequency;run-count; \"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:gi* regex:content:\"\".*filte.*\"\" !ext:log column1:=regmatch0:  columns:dm;size;ext;name;column1;path;name-frequency;extension-frequency;run-count; \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"3dsmax:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"< nohighlight:path:\"\"%LOCALAPPDATA%\"\" nohighlight:path:\"\"3ds\"\" regex:parent-path:\"\"(202[1-9])\\b\"\" > !< path:AdskLicensingSDK|BackBurner|CachedThumbnails|OSLCache > !< childfoldercount:0 > !< size:<8bytes > !< ext:log|tmp|oso|dat|bak|xml > < sort:dm secondary-sort:!isfolder: >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"3dsmax\"\t\t\"\"\r\n\"log: group-colors:size: no-alternate-row-color: dir:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"group-colors:size no-alternate-row-color: dir:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: dupe:name: group-colors:size: no-alternate-row-color: \"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dupe:name: group-colors:size: no-alternate-row-color: \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log:macro:urldirs: (urldirs:) url: path:urls|path:3d focus: ee: dp:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"url: path:urls|path:3d focus: ee: dp:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"urldirs\"\t\t\"\"\r\n\"log: < regex:content:\"\"recent.*\"\" col-a:=regmatch0: a-label:=\"\"match1\"\"  addcolumn:a:2 > < regex:content:\"\".*ite.*\"\" col-b:=regmatch0: b-label:=\"\"match2\"\"  addcolumn:b:3 >\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"< regex:content:\"\"recent.*\"\" col-a:=regmatch0: a-label:=\"\"match1\"\"  addcolumn:a:2 > < regex:content:\"\".*ite.*\"\" col-b:=regmatch0: b-label:=\"\"match2\"\"  addcolumn:b:3 >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: < regex:content:\"\"recent.*\"\" match1: > < regex:content:\"\".*ite.*\"\" match2: >\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"< regex:content:\"\"recent.*\"\" match1: > < regex:content:\"\".*ite.*\"\" match2: >\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: regex:content:\"\".*recent.*\"\"  match1:   regex:content:\"\".*ite.*\"\" match2:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"regex:content:\"\".*recent.*\"\"  match1:   regex:content:\"\".*ite.*\"\" match2:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: file: !dm:last3years sort:size\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"file: !dm:last3years sort:size\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: dir: !dm:last1month sort:size size:>1gb\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dir: !dm:last1month sort:size size:>1gb\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: ee: descendant-count:>100 path:3d path:text\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ee: descendant-count:>100 path:3d path:text\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: !dupe: < img: OR zip: > size:>1mb nohighlight:path:3d|blender|textures|tileable|displacement  \"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!dupe: < img: OR zip: > size:>1mb nohighlight:path:3d|blender|textures|tileable|displacement  \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: !lnk: file:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28LOGS%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!lnk: file:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"🠊 Unsorted\t:: (TODO)    \"\t1\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"\"\r\n\"path:Workflow path:\"\"Cmd - \"\" file: !ext:lnk\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:Workflow path:\"\"Cmd - \"\" file: !ext:lnk\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"Type - Documents\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:c;cc;chm;cpp;cs;css;csv;cxx;doc;docm;docx;dot;dotm;dotx;epub;h;hpp;htm;html;hxx;ini;java;js;json;lua;md;mht;mhtml;mobi;odp;ods;odt;pdf;php;potx;potm;ppam;ppsm;ppsx;pps;ppt;pptm;pptx;pub;py;rtf;sldm;sldx;thmx;txt;vsd;wpd;wps;wri;xlam;xls;xlsb;xlsm;xlsx;xltm;xltx;xml;vb\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"custom--------------------------docs\"\t\t\"\"\r\n\"Category - Downloads\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext|child:ext:avi;divx;flv;hdmov;m1v;m2p;m2t;m2ts;m2v;m4v;mkv;mov;mp2v;mp4;mp4v;mpe;mpeg;mpg;mpls;mpv2;mpv4;ogm;ogv;webm;wm;wmp;wmv;7z;ace;arj;bz2;cab;gz;gzip;jar;r00;r01;r02;r03;r04;r05;r06;r07;r08;r09;r10;r11;r12;r13;r14;r15;r16;r17;r18;r19;r20;r21;r22;r23;r24;r25;r26;r27;r28;r29;rar;tar;tgz;z;zip\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"cat--------------------------downloads\"\t\t\"\"\r\n\"Changed last hour\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm:lasthour path:3ds AND path:2023 AND ext:clrx|mnumx|ini|bprx|mxp|qop|properties|cuix|mnux|workspace|maxsettings|xaml|json|mpl|config|max AND !ext:.tar.gz AND !dir:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"Inspirerende musikk\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"ext:mp3 path:musi distinct:name path:\"\"D:\\Data\\3D-Relatert\\Idefagskolen\\Inspirasjon\\Inspirerende Musikk\"\" sort:dm\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"Simulator Media 🠆 Unique Paths\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dir: \"\"D:\\Simulator Media\"\" depth:2 distinct:path\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\r\n\"distinct:ext AND doc:|pic:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"distinct:ext AND doc:|pic:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"distinct:ext AND doc:|pic:|custom_3d:|zip:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"distinct:ext AND doc:|pic:|custom_3d:|zip:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"distinct:ext AND pic:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"distinct:ext AND pic:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"(Separator)\"\t2\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\r\n\"WIP: 3dsMax-Config-Recent\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm:today: path:3ds AND path:2023 AND ext:clrx|mnumx|ini|bprx|mxp|qop|properties|cuix|mnux|workspace|maxsettings|xaml|json|mpl|xml|config\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"WIP: 3dsMax-Config-Recent 2\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm:today: path:3ds AND path:2023 AND ext:clrx|mnumx|ini|bprx|mxp|qop|properties|cuix|mnux|workspace|maxsettings|xaml|json|mpl|config|max AND !ext:.tar.gz AND !dir:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"WIP: 3dsMax-Config-Recent 3\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"file: AND !ext:.tar.gz AND !ext:xaml !ext:log AND !ext:json dm:lasthour path:3ds AND path:2023 AND ext:clrx|mnumx|ini|bprx|mxp|qop|properties|cuix|mnux|workspace|maxsettings|json|mpl|config|max\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"WIP: 3dsMax-Config-Recent 4\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"file: AND !ext:.tar.gz AND !ext:xaml !ext:log !ext:bak !ext:json !ext:dat AND dm:lasthour path:3ds AND path:2023 AND ext:clrx|mnumx|ini|bprx|mxp|qop|properties|cuix|mnux|workspace|maxsettings|json|mpl|config|max\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"WIP: Config-trash NOT(WIP: 3dsMax-Config-Recent 4)\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"\"\"C:\\Users\\<USER>\\AppData\\Local\\Autodesk\\3dsMax\\*2021*\\ENU\"\" AND file: AND !bookmark:\"\"WIP: 3dsMax-Config-Recent 4\"\";\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"Macro - W (work in progress)\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"((!EXT:\"\"\"\" AND !EXT:\"\"PYC\"\" AND !EXT:\"\"LNK\"\" AND !EXT:\"\"BIN\"\") (!PATH:\"\"%WINDIR%\"\" AND !PATH:\"\"%PROGRAMFILES%\"\" AND !PATH:\"\"%PROGRAMFILES(X86)%\"\" AND !PATH:\"\".GIT\"\") ) OR ((PATH:\"\"%APPDATA%\"\" OR PATH:\"\"%USERPROFILE%\\Desktop\"\" OR PATH:\"\"PRJ\\\"\" OR PATH:\"\"Project\\\"\") (SIZE:>24BYTES ) ) SORT:dm;path;ext;name;size;\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"w\"\t\t\"\"\r\n\"log: py: REGEX:CONTENT:\"\"{time:YYYY.*\"\" column1:=regmatch0: columns:dm;size;ext;name;column1;path;name-frequency;extension-frequency;run-count; \"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"py: REGEX:CONTENT:\"\"{time:YYYY.*\"\" column1:=regmatch0: columns:dm;size;ext;name;column1;path;name-frequency;extension-frequency;run-count; \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"PATH:\"\"APPS/exe_*\"\"|PATH:\"\"APPS/app_*\"\"\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"PATH:\"\"APPS/exe_*\"\"|PATH:\"\"APPS/app_*\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"【  APPS:  】\t:: PATH:\"\"APPS/exe_*\"\"|PATH:\"\"APPS/app_*\"\"\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"PATH:\"\"APPS/exe_*\"\"|PATH:\"\"APPS/app_*\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"apps\"\t\t\"\"\r\n\"log: !appdata: !path:cache :: silence\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!appdata: !path:cache\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: img: size:>1mb !psd: !path:textur path:backg\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"img: size:>1mb !psd: !path:textur path:backg\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: dupe:size;content: file: sort:path\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dupe:size;content: file: sort:path\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: dupe:size;content: file: sort:size dupe:size;\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dupe:size;content: file: sort:size dupe:size;\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: regex content match\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"regex:content:\"\".*ctrl\\+alt\\+s.*\"\" column1:=regmatch0:  columns:dm;size;ext;name;column1;path;name-frequency;extension-frequency;run-count; \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: dm:>52weeks dir: size:>1mb sfda: !path:datafiles|bin\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"dm:>52weeks dir: size:>1mb sfda: !path:datafiles|bin\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: bat: py_venv_ sda: dn:\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"bat: py_venv_ sda: dn:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: chatgpt prompts\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!ext:lnk hide: ex1:ex2:ex3:ex4:  sfda: !path:\"\"EXT_GIT\"\" path:gpt|prompt\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: searchfilter-testing\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"!ext:lnk hide: ex1:ex2:ex3:ex4:  !path:\"\"EXT_GIT\"\" !path:\"\".backup\"\" ii: text: !stw: path:gpt|prompt grpdepth:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: chatgpt2\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"(< !ext:lnk hide: ex1:ex2:ex3:ex4:  !path:\"\"EXT_GIT\"\" !ext:url !path:\"\".backup\"\" ii: text: !stw: path:gpt|prompt grpdepth: > OR ext:url)\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: chatgpt3\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"file:ext:htm|html content:\"\"chatgpt\"\" sort:dm \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: chatgpt4\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"prompt  content:\"\"chatgpt\"\"  content:\"\"rafting your responses, you showcase exceptional ability \"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: find text files\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"focus: text: datepatterns: content:\"\"conversation\"\"  \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: conversations (datepattern in filename)\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"focus: !path:\"\".backups\"\" nohighlight:text: nohighlight:datepatterns: \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: files by content\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"focus: !path:\"\".backups\"\" nohighlight:text: nohighlight:content-datepatterns:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: filest by content2\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"focus: !path:\"\".backups\"\" nohighlight:text: !csv: !ini: hsys: nohighlight:content-datepatterns: \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: filest by content3\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"focus: !path:\"\".backups\"\" nohighlight:text: !csv: !ini: hsys: !stw: nohighlight:content-datepatterns: \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: chatgpt5\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"focus: !path:\"\".backups\"\" nohighlight:text: !csv: !ini: hsys: !stw:|stc: content:chatgpt nohighlight:content-datepatterns: \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: only list duplicates\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"file: !distinct:size;sha256\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: blender:content\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:blender|blende py: content:\"\"=========================================\"\" regex:content: REGEX:CONTENT:\"\"((19\\d{2}|20\\d{2})([\\.\\-\\\\/ ]?)(0[1-9]|1[0-2])\\3(0[1-9]|[12][0-9]|3[01]))|((0[1-9]|[12][0-9]|3[01])([\\.\\-\\\\/ ]?)(0[1-9]|1[0-2])\\8(19\\d{2}|20\\d{2}))\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: blender:py\"\t0\t\"Bookmarks/🠊%20Unsorted%09%3A%3A%20%28TODO%29%20%20%20%20\"\t\t\t\t\t\t\t\t\t\t\"path:blender|blende py: content:\"\"import bpy\"\" content:\"\"=========================================\"\" \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: stp: path:\"\"PRJ\\GIT_WORKFLOW\\__SHELL__\"\"\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"stp: path:\"\"PRJ\\GIT_WORKFLOW\\__SHELL__\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: htm: content:\"\"SingleFile\"\" !dup:\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"htm: content:\"\"SingleFile\"\" !dup:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: !path:\"\".workflow\"\" file:   regex:content:\"\".*URL=.*\"\" col-c:=regmatch0: c-label:=\"\"match1\"\"  addcolumn:c:4 group-colors:col-c|dir:|file:\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"!path:\"\".workflow\"\" file:   regex:content:\"\".*URL=.*\"\" col-c:=regmatch0: c-label:=\"\"match1\"\"  addcolumn:c:4 group-colors:col-c|dir:|file:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: coldepth:    sort:;col-a;path;size;  file:\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"coldepth:    sort:;col-a;path;size;  file:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"bat: content:\"\"robocopy\"\" content:\"\"::\"\"\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"bat: content:\"\"robocopy\"\" content:\"\"::\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\"robocopy\"\t\t\"\"\r\n\"log: prompts - py: content:\"\"chatgpt\"\" content-datepatterns: !path:\"\".gpt\"\" \"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"py: content:\"\"chatgpt\"\" content-datepatterns: !path:\"\".gpt\"\" \"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: md: !dup:path:_v !readme !changelog content:\"\"chatg\"\"\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"md: !dup:path:_v !readme !changelog content:\"\"chatg\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: dupc file: !distinct:size;sha256\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"file: !distinct:size;sha256\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: < content:\"\"incrementalsa\"\"|content:\"\"increment\"\" >  ext:*sub*|py: !path:unsaved regex:content:\"\".*incremental.*\"\" match1:\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"< content:\"\"incrementalsa\"\"|content:\"\"increment\"\" >  ext:*sub*|py: !path:unsaved regex:content:\"\".*incremental.*\"\" match1:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: [blender scripts] py: content:bpy !dup:\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"py: content:bpy !dup:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: clean up space - < depth:<6  size:>2500mb sort:parent-size;path;folder-depth-ascending;size > file:\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"< depth:<6  size:>2500mb sort:parent-size;path;folder-depth-ascending;size > file:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"cursor logs path:\"\".SpecStory\"\"\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"path:\"\".SpecStory\"\"\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"sort mixed sort:dm;folder-depth-ascending:isfolder:\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"sort:dm;folder-depth-ascending:isfolder:\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"common <parent-date-modified:month|date-modified:month|date-created:currentmonth> ext:bat;cmd;json;ms;mzp;nss;ps1;py;ts;tsx;txt;vb;vbs;xml;css;|ext:jinja-*;|<dir: child-file:ext:bat;cmd;json;ms;mzp;nss;ps1;py;ts;tsx;txt;vb;vbs;xml;css;|ext:jinja-*;> <sort:path; secondary-sort:!isfolder:>  file: sort:dm\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"<parent-date-modified:month|date-modified:month|date-created:currentmonth> ext:bat;cmd;json;ms;mzp;nss;ps1;py;ts;tsx;txt;vb;vbs;xml;css;|ext:jinja-*;|<dir: child-file:ext:bat;cmd;json;ms;mzp;nss;ps1;py;ts;tsx;txt;vb;vbs;xml;css;|ext:jinja-*;> <sort:path; secondary-sort:!isfolder:>  file: sort:dm\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n\"log: <!ext:url;lnk sort:dm> <nohighlight:datepatterns:> td: sort:dm\"\t0\t\"Bookmarks\"\t\t\t\t\t\t\t\t\t\t\"<!ext:url;lnk sort:dm> <nohighlight:datepatterns:> td: sort:dm\"\t\t\"\"\t\"\"\t0\t\t\t\t\t0\t\t\t\"\"\r\n"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\nbookmarks_reference.csv\n\n# VSCode Open Tabs\nbookmarks_reference.csv\n\n# Current Time\n4/3/2025, 6:44:56 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<plan_mode_respond>\n<response>\n# Analysis of bookmarks_reference.csv\n\nI can now see the newly added `bookmarks_reference.csv` file, which is a comprehensive collection of bookmark definitions and search patterns for the Everything search utility. This file represents a wealth of practical knowledge about how to effectively use the search syntax in real-world scenarios.\n\n## Structure & Organization\n\nThe bookmarks_reference.csv is organized as a hierarchical export with:\n- Main categories (folders) such as \"Search Reference\", \"Extensions\", \"Foldernames\", etc.\n- Sub-categories that group related search patterns\n- Individual bookmarks with descriptive names and sophisticated search queries\n- Separators to visually organize related items\n\n## Key Pattern Categories\n\n1. **Date Pattern Searches**\n   - Complex regex patterns for matching various date formats\n   - Patterns for filtering by date ranges and relative time periods\n\n2. **Path Manipulation Patterns**\n   - Parent/child relationship filters (e.g., `exact:ancestor-name:`, `child-file:ext:`)\n   - Directory depth constraints (e.g., `depth:<6`)\n   - Empty folder detection (`descendantcount:0`)\n\n3. **Filter Groups**\n   - Modular exclusion patterns with prefixes like `grp-paths-ex:`, `grp-exts-ex:`, etc.\n   - Combined filter groups for sophisticated filtering (`grp-ex:`)\n   - Environment-specific filters (`env-priv:`, `env-jobb:`)\n\n4. **Regex Patterns**\n   - Identifier patterns (GUIDs, hashes, SIDs)\n   - Content search patterns with complex regex\n   - Extension and filename pattern matching\n\n5. **Column & Sorting Techniques**\n   - Custom calculated columns (e.g., time since modified, path depth)\n   - Multi-level sorting strategies\n   - Group coloring by various attributes\n\n6. **Specialized Macros**\n   - File type groupings (e.g., `MYTYPES:`)\n   - Time-based filters (`TMN:`, `THR:`, `TD:`)\n   - Application-specific searches (e.g., Sublime Text: `ST:`, `STP:`, etc.)\n\n## Advanced Techniques\n\nSome of the most sophisticated patterns demonstrate:\n\n1. **Group Exclusion Chaining**\n   ```\n   grp-paths-ex: grp-exts-ex: grp-parents-ex: grp-names-ex: grp-strings-ex: grp-meta-ex: grp-regex-ex: grp-patterns-ex:\n   ```\n   This incrementally builds complex exclusion filters by combining modular components.\n\n2. **Dynamic Column Creation**\n   ```\n   a-label:=\"Last Modified\" addcolumn:a a:=if(now()-$dm:>315576000000000,\"f.  : \"(now()-$dm:)/315576000000000..\" years\",if(now()-$dm:>864000000000,\"c.  : \"(now()-$dm:)/864000000000..\" days [ D ]\",if(now()-$dm:>36000000000,\"b.  : \"(now()-$dm:)/36000000000..\" hours [ + ]\",\"a.  : \"(now()-$dm:)/600000000..\" minutes [ * ]\"))) sort:dm group-colors: column-a:minutes\n   ```\n   Creates a formatted time-since-modified column with conditional formatting.\n\n3. **Path Relationship Queries**\n   ```\n   REGEX:^(.*)\\\\([^\\\\]*)$ fileexists:\\1\\\\backup\\\\\\2\n   ```\n   Checks if a file exists in a backup subfolder relative to its current location.\n\n4. **Content Pattern Extraction**\n   ```\n   regex:content:\".*URL=.*\" col-c:=regmatch0: c-label:=\"match1\" addcolumn:c:4 group-colors:col-c|dir:|file:\n   ```\n   Extracts URL patterns from file content into a sortable column.\n\n## Integration with Previous Files\n\nThis bookmarks collection complements the other files we've examined:\n- It builds on the syntax documented in `notes_search_syntax.cpp`\n- It provides practical examples of the regex patterns from `gpt_interaction_notes.MD`\n- It demonstrates organized usage of the configuration options in `notes_ini.ini`\n\n## Practical Usage Value\n\nThe bookmark structure reveals a workflow-oriented approach with:\n1. Quick access to frequently used pattern types\n2. Reference examples for complex query construction \n3. Task-oriented groups (space cleanup, duplicate finding, etc.)\n4. Sophisticated filtering to handle large file systems efficiently\n\nThis file represents a culmination of practical experience with Everything's search capabilities, organized into a reusable structure that enhances productivity when working with file system searches.\n\nWould you like me to focus on any particular aspect of these bookmark patterns in more detail?\n</response>\n<options>\n[\"Analyze the modular filtering approach with group exclusions\", \"Examine the date and time pattern implementations\", \"Focus on the practical workspace organization techniques\", \"Explore the duplicate file detection strategies\", \"Compare regex patterns with those in the other reference files\"]\n</options>\n</plan_mode_respond>"}]}, {"role": "user", "content": [{"type": "text", "text": "[plan_mode_respond] Result:"}, {"type": "text", "text": "<user_message>\nAnalyze the modular filtering approach with group exclusions\n</user_message>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\nbookmarks_reference.csv\n\n# VSCode Open Tabs\nbookmarks_reference.csv\n\n# Current Time\n4/3/2025, 6:45:52 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "\n\n[Response interrupted by API Error]"}]}, {"role": "user", "content": [{"type": "text", "text": "[TASK RESUMPTION] This task was interrupted 4 minutes ago. The conversation may have been incomplete. Be aware that the project state may have changed since then. The current working directory is now 'c:/Users/<USER>/Desktop/SCRATCH/app_everything2/user/_notes'.\n\nNote: If you previously attempted a tool use that the user did not provide a result for, you should assume the tool use was not successful. However you are in PLAN MODE, so rather than continuing the task, you must respond to the user's message.\n\nNew message to respond to with plan_mode_respond tool (be sure to provide your response in the <response> parameter):\n<user_message>\ncreate a markdown containing the most valuable patterns orginalized in a multidimensional nested hierarchy based on their inherent relationships, save the as `final_everything_referencesheet.md`\n</user_message>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\nbookmarks_reference.csv\n\n# VSCode Open Tabs\nbookmarks_reference.csv\n\n# Current Time\n4/3/2025, 6:50:54 PM (Europe/Oslo, UTC+2:00)\n\n# Current Working Directory (c:/Users/<USER>/Desktop/SCRATCH/app_everything2/user/_notes) Files\nbookmarks_reference.csv\ndebug-grp-search-ex.txt.py\ndiscarded_snippets.py\nEverything.Unicode.Tables.txt\nevr_scratchpad.py\ngpt_interaction_notes.MD\nNotes_ALL.txt.py\nnotes_column_snippets.py\nnotes_exclusions.py.csv\nnotes_ini.ini\nnotes_regex_snippets.py\nnotes_search_syntax.cpp\nnotes_unsorted_snippets.py\nplay.py\nsearch_ux.png\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<plan_mode_respond>\n<response>\n# Plan for Creating the Everything Reference Sheet\n\nI'll create a comprehensive markdown reference sheet that organizes the most valuable search patterns from Everything into a logical, nested hierarchy. This will synthesize information from all the files we've examined (bookmarks_reference.csv, notes_search_syntax.cpp, gpt_interaction_notes.MD, etc.) into a single, well-structured reference document.\n\n## Proposed Organization Structure\n\n```\n# Everything Ultimate Reference Sheet\n\n## 1. Core Search Fundamentals\n   - Basic Syntax and Operators\n   - Wildcards and Pattern Matching\n   - Boolean Logic and Grouping\n\n## 2. Path & Location Patterns\n   - Hierarchical Relationships\n   - Directory Structure Navigation\n   - Path Inclusion/Exclusion Strategies\n\n## 3. File Attribute Patterns\n   - Size-Based Filtering\n   - Date & Time Filtering\n   - File Property Manipulation\n\n## 4. Content-Based Search\n   - Text Content Patterns\n   - Binary Content Patterns\n   - Metadata Extraction\n\n## 5. Regular Expression Patterns\n   - Identifier Patterns (GUIDs, Hashes, etc.)\n   - Format Validation Patterns\n   - Text Extraction Patterns\n\n## 6. Modular Filtering System\n   - Group Exclusion Architecture\n   - Filter Component Design\n   - Composability Strategies\n\n## 7. Advanced Output Control\n   - Column Customization\n   - Sorting Techniques\n   - Visual Grouping Strategies\n\n## 8. Task-Oriented Pattern Collections\n   - Disk Cleanup Workflows\n   - File Organization Patterns\n   - Duplicate Detection Strategies\n\n## 9. Application-Specific Patterns\n   - Development Environment Patterns\n   - Media Management Patterns\n   - Document Management Patterns\n```\n\nEach section will include:\n- Concise explanations of concepts\n- Real-world examples from the analyzed files\n- Practical use cases\n- Syntax diagrams where helpful\n\n## Implementation Approach\n\nI'll extract the most valuable patterns from each source file, deduplicate them, and organize them according to this hierarchy. I'll prioritize:\n- Patterns that demonstrate key concepts clearly\n- Patterns with broad applicability\n- Patterns that showcase advanced techniques\n- Patterns that solve common real-world problems\n\nTo toggle to ACT MODE so I can implement this plan and create the markdown file, please click the toggle button below. Once in ACT MODE, I'll create the `final_everything_referencesheet.md` file according to this structure.\n\nDoes this structure look good to you, or would you like me to adjust the organization before proceeding?\n</response>\n<options>\n[\"Proceed with this structure and toggle to Act Mode\", \"Add more focus on the modular filtering system\", \"Include more emphasis on regex pattern examples\", \"Add a section specifically for bookmark organization strategies\", \"Simplify the structure to focus on fewer, more essential categories\"]\n</options>\n</plan_mode_respond>"}]}, {"role": "user", "content": [{"type": "text", "text": "[plan_mode_respond] Result:"}, {"type": "text", "text": "<user_message>\nno, i meant a list with high-value examples to yeld the most optimal efficiency while simoultaneously being generalizeable through their interdependency. as an example, the following search shows an example that follows the core philosophy/concept of starting broad and filtering in; `<dm:last100hours mytypes:|grp-ex: hhash:> <py:|md:|json:><path:\"__GOTO__\"> content:\"=================\" <sort:dm>`\n</user_message>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\nbookmarks_reference.csv\n\n# VSCode Open Tabs\nbookmarks_reference.csv\n\n# Current Time\n4/3/2025, 7:00:02 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<plan_mode_respond>\n<response>\nI understand now - you're looking for a curated collection of high-value search pattern examples that follow the \"start broad, filter in\" philosophy and showcase interdependent techniques. These should be patterns that can be generalized and adapted across different search contexts.\n\nLet me plan a markdown file focused on powerful, practical patterns rather than a comprehensive reference. When we toggle to Act Mode, I'll create `final_everything_referencesheet.md` with a structure like this:\n\n# High-Value Everything Search Patterns\n\n## Core Pattern Philosophy: Start Broad, Filter In\n\n```\n# Base pattern architecture\n<[time filter] [type filter]|[exclusion group]> <[file type filter]><[path filter]> [content filter] <[sort method]>\n```\n\n## Essential Pattern Components\n\n### 1. Time-Based Anchors\n- `<dm:last100hours ...>` - Recent modifications anchor\n- `<dm:lastweek|dm:lastmonth ...>` - Timespan alternatives\n- `<date-modified:currentday|date-created:currentday>` - Today's changes\n\n### 2. Type Filter Combinations\n- `<mytypes:|grp-ex: hhash:>` - Standard type filter with hash exclusion\n- `<ext:py;md;json;|<dir: child-file:ext:py;md;json;>>` - File types with parent dirs\n- `<file: !ext: size:>24bytes>` - Non-empty files with any extension\n\n### 3. Path Navigation Patterns\n- `<...><path:\"__GOTO__\">` - Path constraints after type filtering\n- `<...><!path:\"%WINDIR%\" !path:\"%PROGRAMFILES%\">` - System exclusions\n- `<...><exact:ancestor-name:\"project\">` - Ancestry filtering\n\n### 4. Content + Context Chains\n- `<...> content:\"=================\" <sort:dm>` - Content markers with sorting\n- `<...> regex:content:\".*pattern.*\" column1:=regmatch0:` - Content extraction\n- `<...> dupe:size;sha256 <sort:path>` - Duplicate finding with path ordering\n\n## Practical Composite Patterns\n\n### Pattern 1: Project File Discovery\n```\n<dm:lastmonth mytypes:|grp-ex:> <py:|md:|json:><path:\"Project\"> content:\"import\" <sort:dm>\n```\n\n### Pattern 2: System Cleanup\n```\n<parent-size:>1GB grp-paths-ex: grp-exts-ex:> <dir: !child-count:0> size:>100mb <sort:size-descending>\n```\n\n### Pattern 3: Code Search\n```\n<ii: ee:> <py:|js:|ts:><exact:ancestor-name:src> regex:content:\"function\\s+(\\w+)\" col-a:=regmatch1: <sort:a>\n```\n\nI'll extract more patterns like these from the files we've analyzed, focusing on those that best demonstrate the \"start broad, filter in\" approach and offer generalizability across different search contexts.\n\nWould you like me to proceed with creating the markdown file based on this approach? If so, please toggle to Act Mode.\n</response>\n<options>\n[\"Add more examples of composite patterns\", \"Focus more on grouping syntax variations\", \"Include more content-based patterns\", \"Proceed with this approach (toggle to Act Mode)\", \"Adjust the structure to prioritize different pattern types\"]\n</options>\n</plan_mode_respond>"}]}, {"role": "user", "content": [{"type": "text", "text": "[plan_mode_respond] Result:"}, {"type": "text", "text": "<user_message>\nbuilding on the concepts we've seen, take an abstract meta perspective role and try to write a highly utilized search-query to effectively locate files that have high filesize vs ratio in importance  (a much improved extension of the previous \"system cleanup\" pattern)\n</user_message>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\nbookmarks_reference.csv\n\n# VSCode Open Tabs\nbookmarks_reference.csv\n\n# Current Time\n4/3/2025, 7:03:29 PM (Europe/Oslo, UTC+2:00)\n\n# Current Mode\nPLAN MODE\nIn this mode you should focus on information gathering, asking questions, and architecting a solution. Once you have a plan, use the plan_mode_respond tool to engage in a conversational back and forth with the user. Do not use the plan_mode_respond tool until you've gathered all the information you need e.g. with read_file or ask_followup_question.\n(Remember: If it seems the user wants you to use tools only available in Act Mode, you should ask the user to \"toggle to Act mode\" (use those words) - they will have to manually do this themselves with the Plan/Act toggle button below. You do not have the ability to switch to Act Mode yourself, and must wait for the user to do it themselves once they are satisfied with the plan. You also cannot present an option to toggle to Act mode, as this will be something you need to direct the user to do manually themselves.)\n</environment_details>"}]}, {"role": "assistant", "content": [{"type": "text", "text": "<plan_mode_respond>\n<response>\n# Advanced System Space Analysis Pattern\n\nTaking a meta perspective on the patterns we've analyzed, I can construct a more sophisticated search query for identifying high filesize-to-importance ratio files. This goes beyond simple \"large files\" searches to intelligently identify candidates for cleanup based on multiple importance indicators.\n\n## Core Concept: The Expendability Quotient\n\nThe key insight is that a file's expendability is determined by multiple factors beyond just size:\n- Large size + low recent usage = high cleanup value\n- Location in non-critical paths increases expendability\n- Certain file types commonly contain redundant or temporary data\n- Files that exist in multiple versions/locations are more expendable\n\n## The Pattern:\n\n```\n< file: size:>100mb \n  !dm:last90days\n  !date-accessed:last60days \n  !runcount:>0 \n  grp-paths-ex: \n  !path:\"Project*\"|!path:\"Source*\"|!path:\"Git*\" > \n< !ext:\"exe|dll|sys\" \n  !REGEX:NAME:\"^backup|^archive|^important\" \n  dupe-candidates: > \n< addcolumn:a a:=$size:/1000000 \n  a-label:=\"Size MB\" \n  addcolumn:b b:=if($date-accessed:==0,\"Never\",formatfiletimeutc($date-accessed:)) \n  b-label:=\"Last Access\" \n  sort:size-descending;date-accessed-ascending > \ngroup-colors:parent-path\n```\n\n### Breaking it Down:\n\n1. **Primary Size + Usage Filter**:\n   - `file: size:>100mb` - Target only large files (100MB+)\n   - `!dm:last90days !date-accessed:last60days !runcount:>0` - Exclude recently modified, accessed, or executed files\n\n2. **Path Intelligence**:\n   - `grp-paths-ex:` - Apply standard system path exclusions from the group filter\n   - `!path:\"Project*\"|!path:\"Source*\"|!path:\"Git*\"` - Skip likely important development directories\n\n3. **Content Type Analysis**:\n   - `!ext:\"exe|dll|sys\"` - Exclude system executables and libraries\n   - `!REGEX:NAME:\"^backup|^archive|^important\"` - Skip explicitly labeled important files\n\n4. **Redundancy Detection**:\n   - `dupe-candidates:` - (This would be a custom macro identifying near-duplicates by size clusters)\n\n5. **Presentation Optimization**:\n   - Add file size in MB for easier reading\n   - Add formatted last access date\n   - Sort by size (descending) with secondary sort by last access (ascending)\n   - Group visually by parent path to identify folder clusters\n\nThis pattern goes beyond simply finding large files - it applies multiple heuristics to quantify \"importance\" and finds files with the highest cleanup value (size vs. importance). It also presents the results in a way that makes it easy to make informed decisions about what to clean up.\n\nYou could extend this further with:\n- A custom formula column that calculates an actual \"expendability score\"\n- Additional search components that look for file types specific to your system\n- Pre-filters that target specific directories known to accumulate expendable content\n\nWould you like me to expand this pattern in any specific direction, or would you prefer to see other advanced pattern concepts?\n</response>\n<options>\n[\"Expand on the duplication detection aspects\", \"Show how to customize for media files specifically\", \"Add more granular age-based filtering techniques\", \"Explain how to integrate with disk analysis tools\", \"Develop the expendability score formula further\"]\n</options>\n</plan_mode_respond>"}]}]