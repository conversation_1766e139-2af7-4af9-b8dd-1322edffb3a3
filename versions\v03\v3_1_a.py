# 'ChatGPT o1-mini'
# 'https://chatgpt.com/c/6742eec4-e1e0-8008-838c-c83a6b6c95b2'

import argparse
import hashlib
import json
import os
import pathlib
import subprocess
import sys
from typing import List, Tuple

from rich import print
from rich.filesize import decimal
from rich.markup import escape
from rich.prompt import Confirm
from rich.text import Text
from rich.tree import Tree


class HashManager:
    """Handles SHA256 hash computations and validations."""

    @staticmethod
    def compute_hash(file_path: pathlib.Path) -> str:
        """Compute SHA256 hash of a file."""
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(4096), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            print(f"[red]Error reading {file_path}: {error}[/red]")
            return ""


class FileRenamer:
    """Manages the batch renaming process."""

    def __init__(self, directory: pathlib.Path, include_subdirs: bool = True):
        self.directory = directory
        self.include_subdirs = include_subdirs

    def gather_file_hashes(self) -> List[Tuple[str, str]]:
        """Gather SHA256 hashes and relative file paths."""
        hashes = []
        for root, _, files in os.walk(self.directory):
            for filename in files:
                full_path = pathlib.Path(root) / filename
                if full_path.is_file() and full_path.suffix != '.hash' and os.access(full_path, os.R_OK):
                    relative_path = full_path.relative_to(self.directory).as_posix()
                    file_hash = HashManager.compute_hash(full_path)
                    if file_hash:
                        hashes.append((file_hash, relative_path))
                        print(f"Processed: {relative_path}")
            if not self.include_subdirs:
                break
        return hashes

    def write_hashes(self, hashes: List[Tuple[str, str]], output_file: pathlib.Path) -> None:
        """Write hashes and filenames to a text file."""
        try:
            with output_file.open("w", encoding='utf-8') as file:
                for file_hash, filename in hashes:
                    file.write(f"{file_hash}|{filename}\n")
            print(f"[green]Hashes written to {output_file}[/green]")
        except IOError as error:
            print(f"[red]Failed to write to {output_file}: {error}[/red]")

    def read_hashes(self, file_path: pathlib.Path) -> List[Tuple[str, str]]:
        """Read hashes and filenames from a text file."""
        hashes = []
        try:
            with file_path.open("r", encoding='utf-8') as file:
                for line in file:
                    parts = line.strip().split('|')
                    if len(parts) == 2:
                        hashes.append((parts[0], parts[1]))
        except IOError as error:
            print(f"[red]Failed to read {file_path}: {error}[/red]")
        return hashes

    def execute_renaming(self, org_file: pathlib.Path, new_file: pathlib.Path) -> None:
        """Execute renaming based on the edited new hash file."""
        original_hashes = self.read_hashes(org_file)
        new_hashes = self.read_hashes(new_file)

        if len(original_hashes) != len(new_hashes):
            print("[yellow]Warning: Mismatch in number of entries between original and new hash files.[/yellow]")

        hash_to_new = {hash_val: new_name for hash_val, new_name in new_hashes}

        for hash_val, original_name in original_hashes:
            new_name = hash_to_new.get(hash_val)
            if new_name:
                original_path = self.directory / original_name
                new_path = self.directory / new_name
                if original_path.exists():
                    if not new_path.exists():
                        try:
                            original_path.rename(new_path)
                            print(f"[green]Renamed: {original_name} -> {new_name}[/green]")
                        except OSError as error:
                            print(f"[red]Failed to rename {original_name} to {new_name}: {error}[/red]")
                    else:
                        print(f"[yellow]Conflict: {new_name} already exists. Skipping {original_name}.[/yellow]")
            else:
                print(f"[red]No new filename found for hash: {hash_val} (File: {original_name})[/red]")

        # Clean up hash files if needed
        # Uncomment the following lines if you want to remove hash files after renaming
        # for file in self.directory.glob('*.hash'):
        #     file.unlink()


class DirectoryVisualizer:
    """Generates a visual representation of the directory structure."""

    @staticmethod
    def create_visual_text(path: pathlib.Path) -> Text:
        """Create formatted text for directory visualization."""
        filename = path.name
        text = Text(filename, "green").highlight_regex(r"\..*$", "bold red")
        text.stylize(f"link file://{path.resolve()}")
        try:
            size = path.stat().st_size
        except OSError:
            size = 0
        text.append(f" ({decimal(size)})", "blue")
        icon = "📁 " if path.is_dir() else "📄 "
        return Text(icon) + text

    @staticmethod
    def build_directory_tree(directory: pathlib.Path, tree: Tree) -> None:
        """Recursively build a tree structure of the directory."""
        try:
            entries = sorted(
                directory.iterdir(),
                key=lambda p: (p.is_file(), p.name.lower()),
            )
        except PermissionError as error:
            print(f"[red]Permission denied: {error}[/red]")
            return

        for entry in entries:
            if entry.name.startswith("."):
                continue
            if entry.is_dir():
                branch = tree.add(f"[bold magenta]{escape(entry.name)}")
                DirectoryVisualizer.build_directory_tree(entry, branch)
            else:
                tree.add(DirectoryVisualizer.create_visual_text(entry))

    @staticmethod
    def visualize(directory: pathlib.Path) -> None:
        """Display the directory tree using Rich."""
        if not directory.exists():
            print(f"[red]Error: Directory '{directory}' does not exist.[/red]")
            return

        tree = Tree(f"[bold blue]{directory}[/bold blue]", guide_style="bold bright_blue")
        DirectoryVisualizer.build_directory_tree(directory, tree)
        print(tree)


def open_in_editor(file_path: pathlib.Path) -> None:
    """Open a file in the default text editor."""
    try:
        if sys.platform.startswith('darwin'):
            subprocess.call(('open', str(file_path)))
        elif os.name == 'nt':
            os.startfile(str(file_path))
        elif os.name == 'posix':
            subprocess.call(('xdg-open', str(file_path)))
        else:
            print(f"[yellow]Unsupported OS for opening files: {sys.platform}[/yellow]")
    except Exception as error:
        print(f"[red]Failed to open {file_path}: {error}[/red]")


def add_context_menu(executable_path: str) -> None:
    """Add the utility to the Windows Explorer context menu."""
    reg_content = f"""
Windows Registry Editor Version 5.00

[HKEY_CLASSES_ROOT\\Directory\\Background\\shell\\BatchRenameUtility]
@="Batch Rename Utility"

[HKEY_CLASSES_ROOT\\Directory\\Background\\shell\\BatchRenameUtility\\command]
@="\"{executable_path}\" \"%V\""
"""
    reg_file = pathlib.Path("add_batch_rename_utility.reg")
    try:
        with reg_file.open("w", encoding='utf-8') as f:
            f.write(reg_content.strip())
        print(f"[green]Registry file '{reg_file}' created successfully.[/green]")
        print("[cyan]To add the context menu, double-click the .reg file and confirm the prompts.[/cyan]")
    except IOError as error:
        print(f"[red]Failed to create registry file: {error}[/red]")


def parse_args() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Batch Rename Utility with SHA256 Verification")
    subparsers = parser.add_subparsers(dest="command", required=True, help="Available commands")

    # Process Command
    process_parser = subparsers.add_parser("process", help="Generate hash files and rename files")
    process_parser.add_argument("directory", type=str, help="Target directory for processing")
    process_parser.add_argument("--include-subdirectories", action="store_true", help="Include subdirectories in processing")
    process_parser.add_argument("--org-output", type=str, default="rename_map_org.txt", help="Output file for original filenames and hashes")
    process_parser.add_argument("--new-output", type=str, default="rename_map_new.txt", help="Output file for new filenames and hashes")

    # Visualize Command
    visualize_parser = subparsers.add_parser("visualize", help="Display directory tree")
    visualize_parser.add_argument("directory", type=str, help="Directory to visualize")

    # Add Context Menu Command
    context_menu_parser = subparsers.add_parser("add-context-menu", help="Add the utility to Windows Explorer context menu")
    context_menu_parser.add_argument("executable_path", type=str, help="Path to the compiled executable")

    return parser.parse_args()


def main() -> None:
    """Main function to execute based on user command."""
    args = parse_args()

    if args.command == "process":
        target_dir = pathlib.Path(args.directory).resolve()
        org_file = pathlib.Path(args.org_output).resolve()
        new_file = pathlib.Path(args.new_output).resolve()

        if not target_dir.is_dir():
            print(f"[red]Error: '{target_dir}' is not a valid directory.[/red]")
            sys.exit(1)

        renamer = FileRenamer(target_dir, include_subdirs=args.include_subdirectories)

        # Operation A: Generate hash files
        print(f"[cyan]Generating hash files for '{target_dir}'...[/cyan]")
        hashes = renamer.gather_file_hashes()
        renamer.write_hashes(hashes, org_file)
        renamer.write_hashes(hashes, new_file)
        print("[cyan]Hash files generated successfully.[/cyan]")

        # Open the NEW hash file in the default editor
        print(f"[cyan]Opening '{new_file}' for editing...[/cyan]")
        open_in_editor(new_file)

        # Wait for user confirmation
        if Confirm.ask("[yellow]Have you updated the new filenames in the hash file? Proceed with renaming.[/yellow]"):
            # Operation B: Rename files
            print(f"[cyan]Renaming files based on '{new_file}'...[/cyan]")
            renamer.execute_renaming(org_file, new_file)
            print("[green]File renaming completed successfully.[/green]")
        else:
            print("[red]Operation aborted by user.[/red]")

    elif args.command == "visualize":
        target_dir = pathlib.Path(args.directory).resolve()
        DirectoryVisualizer.visualize(target_dir)

    elif args.command == "add-context-menu":
        executable_path = args.executable_path
        if not pathlib.Path(executable_path).is_file():
            print(f"[red]Error: Executable '{executable_path}' does not exist.[/red]")
            sys.exit(1)
        add_context_menu(executable_path)

    else:
        print("[red]Unknown command.[/red]")


if __name__ == "__main__":
    main()
