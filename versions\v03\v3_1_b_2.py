# 'ChatGPT o1-mini'
# 'https://chatgpt.com/c/6742eec4-e1e0-8008-838c-c83a6b6c95b2'

import argparse
import hashlib
import os
import pathlib
import shutil
import sys
from typing import List, Tuple

from rich import print
from rich.filesize import decimal
from rich.prompt import Confirm
from rich.table import Table
from rich.text import Text
from rich.tree import Tree


class HashManager:
    """Handles SHA256 hash computations and validations."""

    @staticmethod
    def compute_hash(file_path: pathlib.Path) -> str:
        """Compute SHA256 hash of a file."""
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(4096), b''):
                    sha256.update(chunk)
                return sha256.hexdigest()
        except IOError as error:
            print(f"[red]Error reading {file_path}: {error}[/red]")
            return ""


class FileRenamer:
    """Manages the batch renaming process, including moving files."""

    def __init__(self, directory: pathlib.Path, include_subdirs: bool = True):
        self.directory = directory
        self.include_subdirs = include_subdirs

    def gather_file_hashes(self) -> List[Tuple[str, str]]:
        """Gather SHA256 hashes and relative file paths."""
        hashes = []
        for root, _, files in os.walk(self.directory):
            for filename in files:
                full_path = pathlib.Path(root) / filename
                if full_path.is_file() and os.access(full_path, os.R_OK):
                    relative_path = full_path.relative_to(self.directory).as_posix()
                    file_hash = HashManager.compute_hash(full_path)
                    if file_hash:
                        hashes.append((file_hash, relative_path))
                        print(f"Processed: {relative_path}")
                else:
                    print(f"[yellow]Skipped: {full_path} (Not accessible or not a file)[/yellow]")
            if not self.include_subdirs:
                break
        return hashes

    def write_hashes(self, hashes: List[Tuple[str, str]], output_file: pathlib.Path) -> None:
        """Write hashes and filenames to a text file."""
        try:
            with output_file.open("w", encoding='utf-8') as file:
                for file_hash, filename in hashes:
                    file.write(f"{file_hash}|{filename}\n")
            print(f"[green]Hashes written to {output_file}[/green]")
        except IOError as error:
            print(f"[red]Failed to write to {output_file}: {error}[/red]")

    def read_hashes(self, file_path: pathlib.Path) -> List[Tuple[str, str]]:
        """Read hashes and filenames from a text file."""
        hashes = []
        try:
            with file_path.open("r", encoding='utf-8') as file:
                for line in file:
                    parts = line.strip().split('|', 1)
                    if len(parts) == 2:
                        hashes.append((parts[0], parts[1]))
        except IOError as error:
            print(f"[red]Failed to read {file_path}: {error}[/red]")
        return hashes

    def execute_renaming(self, org_hashes: List[Tuple[str, str]], new_hashes: List[Tuple[str, str]], dry_run: bool = False) -> None:
        """Execute renaming based on the new hash file, handling directory changes."""
        original_map = {hash_val: original_name for hash_val, original_name in org_hashes}
        new_map = {hash_val: new_name for hash_val, new_name in new_hashes}

        all_hashes = set(original_map.keys()) | set(new_map.keys())

        conflicts = False

        for hash_val in all_hashes:
            original_name = original_map.get(hash_val)
            new_name = new_map.get(hash_val)

            if not original_name:
                print(f"[yellow]Hash {hash_val} not found in original hashes. Skipping.[/yellow]")
                continue

            if not new_name:
                print(f"[yellow]Hash {hash_val} not found in new hashes. Skipping.[/yellow]")
                continue

            original_path = self.directory / original_name
            new_path = self.directory / new_name

            if not original_path.exists():
                print(f"[red]Original file {original_path} does not exist. Skipping.[/red]")
                continue

            if original_path.resolve() == new_path.resolve():
                print(f"[green]No change for {original_name}. Skipping.[/green]")
                continue

            if new_path.exists():
                print(f"[red]Target file {new_path} already exists. Conflict detected.[/red]")
                conflicts = True
                continue

            if dry_run:
                print(f"[cyan]Would rename/move: {original_path} -> {new_path}[/cyan]")
            else:
                try:
                    new_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.move(str(original_path), str(new_path))
                    print(f"[green]Renamed/Moved: {original_path} -> {new_path}[/green]")
                except OSError as error:
                    print(f"[red]Failed to rename/move {original_path} to {new_path}: {error}[/red]")

        if conflicts:
            print("[red]Conflicts detected during renaming. Some files were not processed.[/red]")

    def perform_renaming(self, org_file: pathlib.Path, new_file: pathlib.Path) -> None:
        """Coordinate the renaming process with a dry-run option."""
        org_hashes = self.read_hashes(org_file)
        new_hashes = self.read_hashes(new_file)

        # Dry run to preview changes
        print("[cyan]Performing dry run to preview changes...[/cyan]")
        self.execute_renaming(org_hashes, new_hashes, dry_run=True)

        if Confirm.ask("[yellow]Do you want to proceed with these changes?[/yellow]"):
            print("[cyan]Executing renaming...[/cyan]")
            self.execute_renaming(org_hashes, new_hashes, dry_run=False)
            print("[green]File renaming completed successfully.[/green]")
        else:
            print("[red]Operation aborted by user.[/red]")


class DirectoryVisualizer:
    """Generates a visual representation of the directory structure."""

    @staticmethod
    def build_directory_tree(directory: pathlib.Path, tree: Tree) -> None:
        """Recursively build a tree structure of the directory."""
        try:
            entries = sorted(
                directory.iterdir(),
                key=lambda p: (p.is_file(), p.name.lower()),
            )
        except PermissionError as error:
            print(f"[red]Permission denied: {error}[/red]")
            return

        for entry in entries:
            if entry.name.startswith("."):
                continue
            if entry.is_dir():
                branch = tree.add(f"[bold magenta]{entry.name}/")
                DirectoryVisualizer.build_directory_tree(entry, branch)
            else:
                tree.add(f"[green]{entry.name}")

    @staticmethod
    def visualize(directory: pathlib.Path) -> None:
        """Display the directory tree."""
        if not directory.exists():
            print(f"[red]Error: Directory '{directory}' does not exist.[/red]")
            return

        tree = Tree(f"[bold blue]{directory}[/bold blue]", guide_style="bold bright_blue")
        DirectoryVisualizer.build_directory_tree(directory, tree)
        print(tree)


def open_in_editor(file_path: pathlib.Path) -> None:
    """Open a file in the default text editor."""
    try:
        if sys.platform.startswith('darwin'):
            subprocess.call(('open', str(file_path)))
        elif os.name == 'nt':
            os.startfile(str(file_path))
        elif os.name == 'posix':
            subprocess.call(('xdg-open', str(file_path)))
        else:
            print(f"[yellow]Unsupported OS for opening files: {sys.platform}[/yellow]")
    except Exception as error:
        print(f"[red]Failed to open {file_path}: {error}[/red]")


def parse_args() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Batch Rename Utility with SHA256 Verification")
    subparsers = parser.add_subparsers(dest="command", required=True, help="Available commands")

    # Process Command
    process_parser = subparsers.add_parser("process", help="Generate hash files and rename files")
    process_parser.add_argument("directory", type=str, help="Target directory for processing")
    process_parser.add_argument("--include-subdirectories", action="store_true", help="Include subdirectories in processing")
    process_parser.add_argument("--org-output", type=str, default="FilesToText__filenames_ORG.txt", help="Output file for original filenames and hashes")
    process_parser.add_argument("--new-output", type=str, default="FilesToText__filenames_NEW.txt", help="Output file for new filenames and hashes")
    process_parser.add_argument("--visualize", action="store_true", help="Visualize directory structure before and after renaming")

    # Visualize Command
    visualize_parser = subparsers.add_parser("visualize", help="Display directory tree")
    visualize_parser.add_argument("directory", type=str, help="Directory to visualize")

    return parser.parse_args()


def main() -> None:
    """Main function to execute based on user command."""
    args = parse_args()

    if args.command == "process":
        target_dir = pathlib.Path(args.directory).resolve()
        org_file = pathlib.Path(args.org_output).resolve()
        new_file = pathlib.Path(args.new_output).resolve()

        if not target_dir.is_dir():
            print(f"[red]Error: '{target_dir}' is not a valid directory.[/red]")
            sys.exit(1)

        renamer = FileRenamer(target_dir, include_subdirs=args.include_subdirectories)

        # Generate initial hash mappings
        print(f"[cyan]Generating hash files for '{target_dir}'...[/cyan]")
        initial_hashes = renamer.gather_file_hashes()
        renamer.write_hashes(initial_hashes, org_file)
        renamer.write_hashes(initial_hashes, new_file)
        print("[cyan]Hash files generated successfully.[/cyan]")

        # Open the NEW hash file in the default editor
        print(f"[cyan]Opening '{new_file}' for editing...[/cyan]")
        open_in_editor(new_file)

        # Wait for user confirmation
        if Confirm.ask("[yellow]Have you updated the new filenames in the hash file? Proceed to dry run and renaming.[/yellow]"):
            # Regenerate the hash table before renaming
            print(f"[cyan]Regenerating hash files to get the current file structure...[/cyan]")
            updated_hashes = renamer.gather_file_hashes()
            renamer.write_hashes(updated_hashes, org_file)
            print("[cyan]Updated hash files generated successfully.[/cyan]")

            # Perform renaming with dry run
            renamer.perform_renaming(org_file, new_file)

            if args.visualize:
                print("[cyan]Visualizing directory structure after renaming...[/cyan]")
                DirectoryVisualizer.visualize(target_dir)
        else:
            print("[red]Operation aborted by user.[/red]")

    elif args.command == "visualize":
        target_dir = pathlib.Path(args.directory).resolve()
        DirectoryVisualizer.visualize(target_dir)

    else:
        print("[red]Unknown command.[/red]")


if __name__ == "__main__":
    main()
