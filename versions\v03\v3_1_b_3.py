# 'ChatGPT o1-mini'
# 'https://chatgpt.com/c/6742eec4-e1e0-8008-838c-c83a6b6c95b2'

import argparse
import hashlib
import json
import os
import pathlib
import shutil
import subprocess
import sys
from typing import List, Tuple

from rich import print
from rich.prompt import Confirm
from rich.tree import Tree


class HashManager:
    """Handles SHA256 hash computations and validations."""

    @staticmethod
    def compute_hash(file_path: pathlib.Path) -> str:
        """Compute SHA256 hash of a file."""
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(4096), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except I<PERSON><PERSON>r as error:
            return ""  # Error handled in FileRenamer


class FileRenamer:
    """Manages the batch renaming process, including moving files."""

    def __init__(self, directory: pathlib.Path, include_subdirs: bool = True, logger=None):
        self.directory = directory
        self.include_subdirs = include_subdirs
        self.logger = logger

    def gather_file_hashes(self) -> List[Tuple[str, str]]:
        """Gather SHA256 hashes and relative file paths."""
        hashes = []
        for root, _, files in os.walk(self.directory):
            for filename in files:
                full_path = pathlib.Path(root) / filename
                if full_path.is_file() and os.access(full_path, os.R_OK):
                    relative_path = full_path.relative_to(self.directory).as_posix()
                    file_hash = HashManager.compute_hash(full_path)
                    if file_hash:
                        hashes.append((file_hash, relative_path))
                        if self.logger:
                            self.logger.log_processed_file(relative_path)
                else:
                    if self.logger:
                        self.logger.log_skipped_file(full_path)
            if not self.include_subdirs:
                break
        return hashes

    def write_hashes(self, hashes: List[Tuple[str, str]], output_file: pathlib.Path) -> None:
        """Write hashes and filenames to a text file."""
        try:
            with output_file.open("w", encoding='utf-8') as file:
                for file_hash, filename in hashes:
                    file.write(f"{file_hash}|{filename}\n")
            if self.logger:
                self.logger.log_hash_written(output_file)
        except IOError as error:
            if self.logger:
                self.logger.log_error(f"Failed to write to {output_file}: {error}")

    def read_hashes(self, file_path: pathlib.Path) -> List[Tuple[str, str]]:
        """Read hashes and filenames from a text file."""
        hashes = []
        try:
            with file_path.open("r", encoding='utf-8') as file:
                for line in file:
                    parts = line.strip().split('|', 1)
                    if len(parts) == 2:
                        hashes.append((parts[0], parts[1]))
        except IOError as error:
            if self.logger:
                self.logger.log_error(f"Failed to read {file_path}: {error}")
        return hashes

    def execute_renaming(self, org_hashes: List[Tuple[str, str]], new_hashes: List[Tuple[str, str]], dry_run: bool = False) -> None:
        """Execute renaming based on the new hash file, handling directory changes."""
        original_map = {hash_val: original_name for hash_val, original_name in org_hashes}
        new_map = {hash_val: new_name for hash_val, new_name in new_hashes}

        all_hashes = set(original_map.keys()) | set(new_map.keys())

        conflicts = False

        for hash_val in all_hashes:
            original_name = original_map.get(hash_val)
            new_name = new_map.get(hash_val)

            if not original_name:
                if self.logger:
                    self.logger.log_warning(f"Hash {hash_val} not found in original hashes. Skipping.")
                continue

            if not new_name:
                if self.logger:
                    self.logger.log_warning(f"Hash {hash_val} not found in new hashes. Skipping.")
                continue

            original_path = self.directory / original_name
            new_path = self.directory / new_name

            if not original_path.exists():
                if self.logger:
                    self.logger.log_error(f"Original file {original_path} does not exist. Skipping.")
                continue

            if original_path.resolve() == new_path.resolve():
                if self.logger:
                    self.logger.log_info(f"No change for {original_name}. Skipping.")
                continue

            if new_path.exists():
                if self.logger:
                    self.logger.log_error(f"Target file {new_path} already exists. Conflict detected.")
                conflicts = True
                continue

            if dry_run:
                if self.logger:
                    self.logger.log_dry_run(original_path, new_path)
            else:
                try:
                    new_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.move(str(original_path), str(new_path))
                    if self.logger:
                        self.logger.log_renamed(original_path, new_path)
                except OSError as error:
                    if self.logger:
                        self.logger.log_error(f"Failed to rename/move {original_path} to {new_path}: {error}")

        if conflicts and self.logger:
            self.logger.log_warning("Conflicts detected during renaming. Some files were not processed.")

    def perform_renaming(self, org_file: pathlib.Path, new_file: pathlib.Path) -> None:
        """Coordinate the renaming process with a dry-run option."""
        org_hashes = self.read_hashes(org_file)
        new_hashes = self.read_hashes(new_file)

        # Dry run to preview changes
        if self.logger:
            self.logger.log_section("Performing dry run to preview changes...")
        self.execute_renaming(org_hashes, new_hashes, dry_run=True)

        if Confirm.ask("[yellow]Do you want to proceed with these changes?[/yellow]"):
            if self.logger:
                self.logger.log_section("Executing renaming...")
            self.execute_renaming(org_hashes, new_hashes, dry_run=False)
            if self.logger:
                self.logger.log_info("File renaming completed successfully.")
        else:
            if self.logger:
                self.logger.log_info("Operation aborted by user.")


class DirectoryVisualizer:
    """Generates a visual representation of the directory structure."""

    def __init__(self, logger=None):
        self.logger = logger

    @staticmethod
    def create_visual_text(path: pathlib.Path) -> str:
        """Create formatted text for directory visualization."""
        filename = path.name
        try:
            size = path.stat().st_size
        except OSError:
            size = 0
        icon = "📁 " if path.is_dir() else "📄 "
        return f"{icon}`{filename}` ({size} bytes)"

    def build_directory_tree(self, directory: pathlib.Path, tree: Tree) -> None:
        """Recursively build a tree structure of the directory."""
        try:
            entries = sorted(
                directory.iterdir(),
                key=lambda p: (p.is_file(), p.name.lower()),
            )
        except PermissionError as error:
            if self.logger:
                self.logger.log_error(f"Permission denied: {error}")
            return

        for entry in entries:
            if entry.name.startswith("."):
                continue
            if entry.is_dir():
                branch = tree.add(f"[bold magenta]{entry.name}/")
                self.build_directory_tree(entry, branch)
            else:
                tree.add(self.create_visual_text(entry))

    def visualize(self, directory: pathlib.Path) -> None:
        """Display the directory tree using Rich."""
        if not directory.exists():
            if self.logger:
                self.logger.log_error(f"Error: Directory '{directory}' does not exist.")
            return

        tree = Tree(f"[bold blue]{directory}[/bold blue]", guide_style="bold bright_blue")
        self.build_directory_tree(directory, tree)
        print(tree)


class MarkdownLogger:
    """Handles logging in Markdown format."""

    def __init__(self, log_file: pathlib.Path):
        self.log_file = log_file
        self.initialize_log()

    def initialize_log(self):
        """Initialize the markdown log file with a header."""
        with self.log_file.open("w", encoding='utf-8') as f:
            f.write("<!-- Begin Log -->\n\n")

    def log_section(self, title: str):
        """Log a new section."""
        with self.log_file.open("a", encoding='utf-8') as f:
            f.write(f"### {title}\n\n")

    def log_processed_file(self, filename: str):
        """Log a processed file."""
        with self.log_file.open("a", encoding='utf-8') as f:
            f.write(f"- Processed: `{filename}`\n")

    def log_skipped_file(self, filepath: pathlib.Path):
        """Log a skipped file."""
        relative_path = filepath.relative_to(self.log_file.parent)
        with self.log_file.open("a", encoding='utf-8') as f:
            f.write(f"- Skipped: `{relative_path}` (Not accessible or not a file)\n")

    def log_hash_written(self, output_file: pathlib.Path):
        """Log that hashes have been written."""
        with self.log_file.open("a", encoding='utf-8') as f:
            f.write("\nHashes written to\n")
            f.write(f"`{output_file}`\n\n")

    def log_error(self, message: str):
        """Log an error message."""
        with self.log_file.open("a", encoding='utf-8') as f:
            f.write(f"- **Error:** {message}\n")

    def log_warning(self, message: str):
        """Log a warning message."""
        with self.log_file.open("a", encoding='utf-8') as f:
            f.write(f"- **Warning:** {message}\n")

    def log_info(self, message: str):
        """Log an informational message."""
        with self.log_file.open("a", encoding='utf-8') as f:
            f.write(f"- **Info:** {message}\n")

    def log_dry_run(self, original_path: pathlib.Path, new_path: pathlib.Path):
        """Log a dry run rename/move."""
        with self.log_file.open("a", encoding='utf-8') as f:
            f.write(f"- Would rename/move:\n  `{original_path}` -> `{new_path}`\n")

    def log_renamed(self, original_path: pathlib.Path, new_path: pathlib.Path):
        """Log a successful rename/move."""
        with self.log_file.open("a", encoding='utf-8') as f:
            f.write(f"- Renamed/Moved:\n  `{original_path}` -> `{new_path}`\n")


def open_in_editor(file_path: pathlib.Path) -> None:
    """Open a file in the default text editor."""
    try:
        if sys.platform.startswith('darwin'):
            subprocess.call(('open', str(file_path)))
        elif os.name == 'nt':
            os.startfile(str(file_path))
        elif os.name == 'posix':
            subprocess.call(('xdg-open', str(file_path)))
        else:
            print(f"[yellow]Unsupported OS for opening files: {sys.platform}[/yellow]")
    except Exception as error:
        print(f"[red]Failed to open {file_path}: {error}[/red]")


def add_context_menu(executable_path: str) -> None:
    """Add the utility to the Windows Explorer context menu."""
    reg_content = f"""
Windows Registry Editor Version 5.00

[HKEY_CLASSES_ROOT\\Directory\\Background\\shell\\BatchRenameUtility]
@="Batch Rename Utility"

[HKEY_CLASSES_ROOT\\Directory\\Background\\shell\\BatchRenameUtility\\command]
@="\"{executable_path}\" process \"%V\""
"""
    reg_file = pathlib.Path("add_batch_rename_utility.reg")
    try:
        with reg_file.open("w", encoding='utf-8') as f:
            f.write(reg_content.strip())
        print(f"[green]Registry file '{reg_file}' created successfully.[/green]")
        print("[cyan]To add the context menu, double-click the .reg file and confirm the prompts.[/cyan]")
    except IOError as error:
        print(f"[red]Failed to create registry file: {error}[/red]")


def parse_args() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Batch Rename Utility with SHA256 Verification and Markdown Logging")
    subparsers = parser.add_subparsers(dest="command", required=True, help="Available commands")

    # Process Command
    process_parser = subparsers.add_parser("process", help="Generate hash files and rename files")
    process_parser.add_argument("directory", type=str, help="Target directory for processing")
    process_parser.add_argument("--include-subdirectories", action="store_true", help="Include subdirectories in processing")
    process_parser.add_argument("--org-output", type=str, default="FilesToText__filenames_ORG.txt", help="Output file for original filenames and hashes")
    process_parser.add_argument("--new-output", type=str, default="FilesToText__filenames_NEW.txt", help="Output file for new filenames and hashes")
    process_parser.add_argument("--visualize", action="store_true", help="Visualize directory structure after renaming")
    process_parser.add_argument("--log-file", type=str, default=None, help="Path to the Markdown log file")
    process_parser.add_argument("--verbosity", type=str, choices=['quiet', 'normal', 'verbose'], default='normal', help="Set the verbosity level of console output")

    # Visualize Command
    visualize_parser = subparsers.add_parser("visualize", help="Display directory tree")
    visualize_parser.add_argument("directory", type=str, help="Directory to visualize")

    # Add Context Menu Command
    context_menu_parser = subparsers.add_parser("add-context-menu", help="Add the utility to Windows Explorer context menu")
    context_menu_parser.add_argument("executable_path", type=str, help="Path to the compiled executable")

    return parser.parse_args()


def main() -> None:
    """Main function to execute based on user command."""
    args = parse_args()

    logger = None
    if args.command == "process" and args.log_file:
        logger = MarkdownLogger(pathlib.Path(args.log_file).resolve())

    if args.command == "process":
        target_dir = pathlib.Path(args.directory).resolve()
        org_file = pathlib.Path(args.org_output).resolve()
        new_file = pathlib.Path(args.new_output).resolve()

        if not target_dir.is_dir():
            if logger:
                logger.log_error(f"Error: '{target_dir}' is not a valid directory.")
            print(f"[red]Error: '{target_dir}' is not a valid directory.[/red]")
            sys.exit(1)

        renamer = FileRenamer(
            target_dir,
            include_subdirs=args.include_subdirectories,
            logger=logger
        )

        # Generate initial hash mappings
        if logger:
            logger.log_section("Generating hash files")
            logger.log_info(f"Generating hash files for `{target_dir}`...")
        print(f"[cyan]Generating hash files for '{target_dir}'...[/cyan]")
        initial_hashes = renamer.gather_file_hashes()
        renamer.write_hashes(initial_hashes, org_file)
        renamer.write_hashes(initial_hashes, new_file)
        print("[cyan]Hash files generated successfully.[/cyan]")

        if logger:
            logger.log_section("Hashes Written")
            logger.log_info(f"Hashes written to `{org_file}`")
            logger.log_info(f"Hashes written to `{new_file}`")
            logger.log_info("Hash files generated successfully.")

        # Open the NEW hash file in the default editor
        if logger:
            logger.log_section("Opening Hash File for Editing")
            logger.log_info(f"Opening `{new_file}` for editing...")
        print(f"[cyan]Opening '{new_file}' for editing...[/cyan]")
        open_in_editor(new_file)

        # Wait for user confirmation
        if Confirm.ask("[yellow]Have you updated the new filenames in the hash file? Proceed to dry run and renaming.[/yellow]"):
            # Regenerate the hash table before renaming
            if logger:
                logger.log_section("Regenerating Hash Files")
                logger.log_info("Regenerating hash files to get the current file structure...")
            print(f"[cyan]Regenerating hash files to get the current file structure...[/cyan]")
            updated_hashes = renamer.gather_file_hashes()
            renamer.write_hashes(updated_hashes, org_file)
            print("[cyan]Updated hash files generated successfully.[/cyan]")
            if logger:
                logger.log_info("Updated hash files generated successfully.")

            # Perform renaming with dry run
            renamer.perform_renaming(org_file, new_file)

            # Visualization after renaming
            if args.visualize:
                if logger:
                    logger.log_section("Post-Renaming Directory Structure")
                print("[cyan]Visualizing directory structure after renaming...[/cyan]")
                visualizer = DirectoryVisualizer(logger=logger)
                visualizer.visualize(target_dir)
        else:
            if logger:
                logger.log_info("Operation aborted by user.")
            print("[red]Operation aborted by user.[/red]")

    elif args.command == "visualize":
        target_dir = pathlib.Path(args.directory).resolve()
        visualizer = DirectoryVisualizer()
        visualizer.visualize(target_dir)

    elif args.command == "add-context-menu":
        executable_path = args.executable_path
        if not pathlib.Path(executable_path).is_file():
            print(f"[red]Error: Executable '{executable_path}' does not exist.[/red]")
            sys.exit(1)
        add_context_menu(executable_path)

    else:
        print("[red]Unknown command.[/red]")


if __name__ == "__main__":
    main()
