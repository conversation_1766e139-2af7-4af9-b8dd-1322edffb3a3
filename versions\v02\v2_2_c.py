# 'ChatGPT o1-mini'
# 'https://chatgpt.com/c/6742dda3-d600-8008-bc70-b303a82d1588'

#!/usr/bin/env python3
"""
py_RenameWithTextEditor.py

A self-contained script to compute SHA256 hashes of files, write hashes to files,
rename files based on hash matching, and visualize directory structures.

Usage:
    python py_RenameWithTextEditor.py
"""

import argparse
import hashlib
import os
import pathlib
import subprocess
import sys
import time
from typing import List, Tuple

from rich import print
from rich.filesize import decimal
from rich.markup import escape
from rich.prompt import Prompt, Confirm
from rich.text import Text
from rich.tree import Tree


def sha256_checksum(file_path: str) -> str:
    """
    Compute the SHA256 hash of a file.

    Args:
        file_path (str): Path to the file.

    Returns:
        str: SHA256 hash hexadecimal string.
    """
    sha256 = hashlib.sha256()
    try:
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b''):
                sha256.update(chunk)
    except IOError as e:
        print(f"[red]Error reading file {file_path}: {e}[/red]")
        return ""
    return sha256.hexdigest()


def get_file_hashes(input_directory: str, include_subdirectories: bool = True) -> List[Tuple[str, str]]:
    """
    Get the SHA256 hash and relative filename for each file in a directory.

    Args:
        input_directory (str): Path to the input directory.
        include_subdirectories (bool): Whether to include subdirectories.

    Returns:
        List[Tuple[str, str]]: List of tuples containing (hash, relative_file_path).
    """
    file_hashes = []
    for root, dirs, files in os.walk(input_directory):
        for filename in files:
            file_path = os.path.join(root, filename)
            if os.path.isfile(file_path) and os.access(file_path, os.R_OK):
                relative_path = os.path.relpath(file_path, input_directory)
                file_hash = sha256_checksum(file_path)
                if file_hash:
                    file_hashes.append((file_hash, relative_path))
                    print(f"Processed: {relative_path}")
        if not include_subdirectories:
            break
    return file_hashes


def write_hashes_to_file(file_hashes: List[Tuple[str, str]], output_file: str) -> None:
    """
    Write the list of file hashes and filenames to a text file.

    Args:
        file_hashes (List[Tuple[str, str]]): List of tuples containing (hash, filename).
        output_file (str): Path to the output text file.
    """
    try:
        with open(output_file, "w", encoding='utf-8') as f:
            for file_hash, filename in file_hashes:
                f.write(f"{file_hash}|{filename}\n")
        print(f"[green]Hashes written to {output_file}[/green]")
    except IOError as e:
        print(f"[red]Error writing to file {output_file}: {e}[/red]")


def read_hashes_from_file(input_file: str) -> List[Tuple[str, str]]:
    """
    Read hashes and filenames from a text file.

    Args:
        input_file (str): Path to the input text file.

    Returns:
        List[Tuple[str, str]]: List of tuples containing (hash, filename).
    """
    hashes = []
    try:
        with open(input_file, "r", encoding='utf-8') as f:
            for line in f:
                parts = line.strip().split('|')
                if len(parts) == 2:
                    hashes.append((parts[0], parts[1]))
    except IOError as e:
        print(f"[red]Error reading file {input_file}: {e}[/red]")
    return hashes


def open_file_in_editor(file_path: str) -> None:
    """
    Open a file in the default text editor.

    Args:
        file_path (str): Path to the file to open.
    """
    try:
        if sys.platform.startswith('darwin'):
            subprocess.call(('open', file_path))
        elif os.name == 'nt':
            os.startfile(file_path)
        elif os.name == 'posix':
            subprocess.call(('xdg-open', file_path))
        else:
            print(f"[yellow]Cannot determine the OS to open the file {file_path}.[/yellow]")
    except Exception as e:
        print(f"[red]Failed to open file {file_path} in editor: {e}[/red]")


def rename_files(
    input_directory: str,
    original_hash_file: str,
    new_hash_file: str
) -> None:
    """
    Rename files in the input directory based on hash matching between original and new hash files.

    Args:
        input_directory (str): Path to the input directory.
        original_hash_file (str): Path to the original hashes file.
        new_hash_file (str): Path to the new hashes file.
    """
    original_hashes = read_hashes_from_file(original_hash_file)
    new_hashes = read_hashes_from_file(new_hash_file)

    if len(original_hashes) != len(new_hashes):
        print("[yellow]Warning: The number of entries in original and new hash files do not match.[/yellow]")

    # Create a mapping from hash to new filename
    hash_to_new_filename = {hash_val: new_name for hash_val, new_name in new_hashes}

    for original_hash, original_filename in original_hashes:
        if original_hash in hash_to_new_filename:
            new_filename = hash_to_new_filename[original_hash]
            original_file_path = os.path.join(input_directory, original_filename)
            new_file_path = os.path.join(input_directory, new_filename)

            # Ensure the original file exists and the new filename does not cause a conflict
            if os.path.exists(original_file_path):
                if not os.path.exists(new_file_path):
                    try:
                        os.rename(original_file_path, new_file_path)
                        print(f"[green]Renamed: {original_filename} -> {new_filename}[/green]")
                    except OSError as e:
                        print(f"[red]Error renaming {original_filename} to {new_filename}: {e}[/red]")
                else:
                    print(f"[yellow]Conflict: {new_filename} already exists. Skipping rename for {original_filename}.[/yellow]")
        else:
            print(f"[red]No matching new filename found for hash: {original_hash} (File: {original_filename})[/red]")


def create_file_text(path: pathlib.Path) -> Text:
    """
    Creates a formatted Text object for a file.

    Args:
        path (pathlib.Path): Path object of the file.

    Returns:
        Text: Formatted Rich Text object.
    """
    text_filename = Text(path.name, "green")
    text_filename.highlight_regex(r"\..*$", "bold red")
    text_filename.stylize(f"link file://{path}")
    try:
        file_size = path.stat().st_size
    except OSError:
        file_size = 0
    text_filename.append(f" ({decimal(file_size)})", "blue")
    icon = "🐍 " if path.suffix == ".py" else "📄 "
    return Text(icon) + text_filename


def walk_directory(directory: pathlib.Path, tree: Tree) -> None:
    """
    Recursively builds a Tree with directory contents.

    Args:
        directory (pathlib.Path): Path object of the directory.
        tree (Tree): Rich Tree object to build upon.
    """
    try:
        paths = sorted(
            pathlib.Path(directory).iterdir(),
            key=lambda path: (path.is_file(), path.name.lower()),
        )
    except PermissionError as e:
        print(f"[red]Permission denied: {e}[/red]")
        return

    for path in paths:
        if path.name.startswith("."):
            continue  # Skip hidden files and directories

        if path.is_dir():
            style = "dim" if path.name.startswith("__") else ""
            branch = tree.add(
                f"[bold magenta]:open_file_folder: [link file://{path}]{escape(path.name)}",
                style=style,
                guide_style=style,
            )
            walk_directory(path, branch)
        else:
            tree.add(create_file_text(path))


def display_directory_tree(directory: str) -> None:
    """
    Display the directory tree using Rich.

    Args:
        directory (str): Path to the directory to visualize.
    """
    path = pathlib.Path(directory)
    if not path.exists():
        print(f"[red]Error: The directory '{directory}' does not exist.[/red]")
        return

    tree = Tree(
        f":open_file_folder: [link file://{directory}]{directory}",
        guide_style="bold bright_blue",
    )
    walk_directory(path, tree)
    print(tree)


def parse_arguments() -> argparse.Namespace:
    """
    Parse command-line arguments.

    Returns:
        argparse.Namespace: Parsed arguments.
    """
    parser = argparse.ArgumentParser(
        description="py_RenameWithTextEditor: Hashing and Renaming Utility"
    )

    parser.add_argument(
        "operation",
        choices=["process", "visualize"],
        help="Operation to perform: 'process' to generate hashes and rename files, 'visualize' to display directory tree.",
    )
    parser.add_argument(
        "input_directory",
        type=str,
        nargs="?",
        default=".",
        help="Directory to process or visualize. Defaults to current directory.",
    )
    parser.add_argument(
        "--include-subdirectories",
        action="store_true",
        help="Include subdirectories in processing. Relevant for 'process' operation.",
    )
    parser.add_argument(
        "--original-output",
        type=str,
        default="FilesToText__filenames_ORG.txt",
        help="Output file for original filenames and hashes.",
    )
    parser.add_argument(
        "--new-output",
        type=str,
        default="FilesToText__filenames_NEW.txt",
        help="Output file for new filenames and hashes.",
    )

    return parser.parse_args()


def main():
    args = parse_arguments()

    if args.operation == "process":
        input_dir = os.path.abspath(args.input_directory)
        original_output = os.path.abspath(args.original_output)
        new_output = os.path.abspath(args.new_output)

        if not pathlib.Path(input_dir).exists():
            print(f"[red]Error: The directory '{input_dir}' does not exist.[/red]")
            sys.exit(1)

        print(f"[cyan]Generating hashes for directory: {input_dir}[/cyan]")
        file_hashes = get_file_hashes(input_dir, include_subdirectories=args.include_subdirectories)
        write_hashes_to_file(file_hashes, original_output)
        write_hashes_to_file(file_hashes, new_output)
        print("[cyan]Hash files generated successfully.[/cyan]")

        # Open the new filenames file in the default editor
        print(f"[cyan]Opening '{new_output}' for editing...[/cyan]")
        open_file_in_editor(new_output)

        # Wait for user confirmation
        print(f"[yellow]Please edit '{new_output}' with the new filenames and save the file.[/yellow]")
        Confirm.ask("Press Enter to continue with renaming after you have saved the new filenames file.")

        # Proceed to rename
        print(f"[cyan]Renaming files based on '{new_output}'...[/cyan]")
        rename_files(input_dir, original_output, new_output)
        print("[green]File renaming process completed.[/green]")

    elif args.operation == "visualize":
        directory = os.path.abspath(args.input_directory)
        display_directory_tree(directory)


if __name__ == "__main__":
    main()
