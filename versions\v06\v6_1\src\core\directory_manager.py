import os
import pathlib
from typing import List, Optional

from src.utils.logging import Logger, LogLevel

class DirectoryManager:
    """Manages directory operations and structure visualization."""

    def __init__(self, root_dir: pathlib.Path, logger: Logger):
        self.root_dir = root_dir
        self.logger = logger

    def ensure_directory(self, path: pathlib.Path) -> bool:
        """Creates directory if it doesn't exist."""
        try:
            path.mkdir(parents=True, exist_ok=True)
            return True
        except OSError as error:
            self.logger.log(f"Failed to create directory {path}: {error}", LogLevel.ERROR)
            return False

    def get_directory_structure(self, max_depth: Optional[int] = None) -> List[str]:
        """Returns a formatted list representing the directory structure."""
        if not self.root_dir.exists():
            self.logger.log(f"Directory not found: {self.root_dir}", LogLevel.ERROR)
            return []

        structure = []
        try:
            self._build_structure(self.root_dir, structure, "", 0, max_depth)
        except PermissionError as error:
            self.logger.log(f"Access denied: {error}", LogLevel.ERROR)
        
        return structure

    def _build_structure(
        self,
        directory: pathlib.Path,
        structure: List[str],
        indent: str,
        current_depth: int,
        max_depth: Optional[int]
    ) -> None:
        """Recursively builds the directory structure."""
        if max_depth is not None and current_depth > max_depth:
            return

        try:
            entries = sorted(
                directory.iterdir(),
                key=lambda p: (p.is_file(), p.name.lower())
            )

            for entry in entries:
                if entry.name.startswith('.'):
                    continue

                rel_path = entry.relative_to(self.root_dir)
                if entry.is_dir():
                    structure.append(f"{indent}📁 {entry.name}/")
                    self._build_structure(
                        entry,
                        structure,
                        indent + "  ",
                        current_depth + 1,
                        max_depth
                    )
                else:
                    structure.append(f"{indent}📄 {entry.name}")

        except PermissionError as error:
            structure.append(f"{indent}🚫 Access Denied: {directory.name}")
