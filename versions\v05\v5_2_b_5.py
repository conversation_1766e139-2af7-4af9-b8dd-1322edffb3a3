#!/usr/bin/env python3
"""
Batch Rename Utility with SHA256 Verification
Provides batch file renaming capabilities with hash verification and text editor integration.
"""

import argparse
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
from dataclasses import dataclass
from enum import Enum, auto
from typing import Dict, List, Optional, Set, Tuple

import re
import unicodedata
from pathlib import Path

from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.box import ROUNDED


class VerbosityLevel(Enum):
    QUIET = auto()
    NORMAL = auto()
    VERBOSE = auto()
    DEBUG = auto()


class LogLevel(Enum):
    PROCESSED = auto()
    WARNING = auto()
    ERROR = auto()
    ACTION = auto()
    SUMMARY = auto()
    CHANGE = auto()
    SKIP = auto()


@dataclass
class LogConfig:
    verbosity: VerbosityLevel
    show_skipped: bool = False
    show_unchanged: bool = False



class Logger:
    """Handles logging with different verbosity levels and styles."""

    def __init__(self, config: LogConfig):
        self.config = config
        self.console = Console(highlight=False)
        self.changes = 0
        self.skips = 0
        self.errors = 0

        self.level_mapping = {
            VerbosityLevel.QUIET: {LogLevel.ERROR, LogLevel.SUMMARY, LogLevel.CHANGE},
            VerbosityLevel.NORMAL: {
                LogLevel.ERROR,
                LogLevel.SUMMARY,
                LogLevel.CHANGE,
                LogLevel.WARNING,
            },
            VerbosityLevel.VERBOSE: {
                LogLevel.ERROR,
                LogLevel.SUMMARY,
                LogLevel.CHANGE,
                LogLevel.WARNING,
                LogLevel.PROCESSED,
                LogLevel.ACTION,
            },
            VerbosityLevel.DEBUG: set(LogLevel),
        }

    def should_log(self, level: LogLevel) -> bool:
        return level in self.level_mapping[self.config.verbosity]

    def log(self, message: str, level: LogLevel, details: Optional[str] = None) -> None:
        if not self.should_log(level):
            return

        if level == LogLevel.SKIP and not self.config.show_skipped:
            self.skips += 1
            return

        styles = {
            LogLevel.ERROR: "bold red",
            LogLevel.WARNING: "yellow",
            LogLevel.CHANGE: "bold green",
            LogLevel.SUMMARY: "bold blue",
            LogLevel.ACTION: "blue",
            LogLevel.PROCESSED: "bright_black",
            LogLevel.SKIP: "bright_black",
        }

        prefixes = {
            LogLevel.ERROR: "❌",
            LogLevel.WARNING: "⚠️ ",
            LogLevel.CHANGE: "✨",
            LogLevel.SUMMARY: "📋",
            LogLevel.ACTION: "🔄",
            LogLevel.PROCESSED: "📝",
            LogLevel.SKIP: "⏭️ ",
        }

        # Update counters based on log level
        if level == LogLevel.ERROR:
            self.errors += 1
        elif level == LogLevel.CHANGE:
            self.changes += 1

        styled_message = f"[{styles[level]}]{prefixes[level]} {message}[/]"

        if details and self.config.verbosity == VerbosityLevel.DEBUG:
            styled_message += f"\n  [{styles[level]}]{details}[/]"

        self.console.print(styled_message)

    def print_summary(self) -> None:
        if not self.should_log(LogLevel.SUMMARY):
            return

        table = Table(title="Operation Summary", show_header=False, box=None)
        table.add_column("Type", style="bold")
        table.add_column("Count", justify="right")

        table.add_row("Changes made", str(self.changes))
        if self.config.show_skipped:
            table.add_row("Items skipped", str(self.skips))
        if self.errors > 0:
            table.add_row("Errors encountered", f"[red]{self.errors}[/]")

        self.console.print("\n")
        self.console.print(Panel(table, border_style="blue"))



class FileHasher:
    """Computes SHA256 hashes for files."""

    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path, logger: Logger) -> Optional[str]:
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.log(f"Error reading `{file_path}`: {error}", LogLevel.ERROR)
            return None


class FileProcessor:
    """Processes files to collect their hashes and sizes."""

    def __init__(self, root_dir: pathlib.Path, include_subdirs: bool, logger: Logger):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs
        self.logger = logger

    def collect_file_hashes(self) -> List[Tuple[str, int, str]]:
        """
        Collects SHA256 hashes and file sizes for all accessible files.

        Returns:
            List of tuples containing (hash, size, relative_path).
        """
        hash_entries = []
        for root, _, files in os.walk(self.root_dir):
            for filename in files:
                file_path = pathlib.Path(root) / filename
                if not self._is_accessible_file(file_path):
                    continue

                relative_path = file_path.relative_to(self.root_dir).as_posix()
                file_hash = FileHasher.compute_sha256(file_path, self.logger)
                file_size = file_path.stat().st_size

                if file_hash:
                    hash_entries.append((file_hash, file_size, relative_path))
                    self.logger.log(f"Processed: {relative_path}", LogLevel.PROCESSED)

            if not self.include_subdirs:
                break
        return hash_entries

    def _is_accessible_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            self.logger.log(f"`{path}` is not accessible or not a file", LogLevel.WARNING)
            return False
        return True





class HashFileManager:
    """Manages reading and writing hash files with file sizes."""

    def __init__(self, file_path: pathlib.Path, logger: Logger):
        self.file_path = file_path
        self.logger = logger

    def write(self, hash_entries: List[Tuple[str, int, str]]) -> None:
        """
        Writes hash, size, and filename mappings to the hash file.

        Format:
            - 'filename' | size | "hash"
        """
        try:
            max_length = max(len(filename) for _, _, filename in hash_entries) + 2
            with self.file_path.open("w", encoding='utf-8') as f:
                f.write("# Hash to Filename Mapping with Sizes\n")
                for file_hash, size, filename in sorted(hash_entries, key=lambda x: x[2].lower()):
                    padded_filename = f"'{filename}'".ljust(max_length)
                    f.write(f"- {padded_filename} | {size} | \"{file_hash}\"\n")
            self.logger.log(f"Hash file written: {self.file_path}", LogLevel.ACTION)
        except IOError as error:
            self.logger.log(f"Failed to write hash file: {error}", LogLevel.ERROR)

    def read(self) -> List[Tuple[str, int, str]]:
        """
        Reads hash, size, and filename mappings from the hash file.

        Returns:
            List of tuples containing (hash, size, filename).
        """
        hash_entries = []
        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line in f:
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            self.logger.log(f"Failed to read hash file: {error}", LogLevel.ERROR)
        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, int, str]]:
        """
        Parses a single line of the hash file.

        Expected format:
            - 'filename' | size | "hash"

        Returns:
            Tuple of (hash, size, filename) or None if parsing fails.
        """
        line = line.strip()
        if not (line.startswith("- '") and ' | ' in line and line.endswith('"')):
            return None

        try:
            # Split the line into parts
            filename_part, size_part, hash_part = re.split(r'\s*\|\s*', line[2:-1])
            filename = filename_part.strip("'")
            size = int(size_part)
            file_hash = hash_part.strip('"')
            return (file_hash, size, filename)
        except (IndexError, ValueError) as e:
            self.logger.log(f"Invalid hash file entry: {line} | Error: {e}", LogLevel.WARNING)
            return None


class FileRenamer:
    """Handles the renaming of files based on hash and size comparisons."""

    def __init__(self, root_dir: pathlib.Path, logger: Logger):
        self.root_dir = root_dir
        self.logger = logger

    def execute(
        self,
        source_hashes: List[Tuple[str, int, str]],
        target_hashes: List[Tuple[str, int, str]],
        dry_run: bool = True
    ) -> bool:
        """
        Executes the renaming process based on source and target hash mappings.

        Args:
            source_hashes: List of tuples containing (hash, size, relative_path).
            target_hashes: List of tuples containing (hash, size, relative_path).
            dry_run: If True, previews changes without applying them.

        Returns:
            True if the process completes without conflicts, False otherwise.
        """
        source_map = self._map_hash_to_paths(source_hashes)
        target_map = self._map_hash_to_paths(target_hashes)

        rename_pairs = self._determine_rename_pairs(source_map, target_map)
        conflicts = False

        if dry_run:
            self._preview_renames(rename_pairs)

        for src_rel, tgt_rel in rename_pairs:
            src_path = self.root_dir / src_rel
            tgt_path = self.root_dir / tgt_rel

            if src_rel == tgt_rel:
                if self.logger.config.show_unchanged:
                    self.logger.log(f"Unchanged: {src_rel}", LogLevel.SKIP)
                continue

            if not self._validate_paths(src_path, tgt_path):
                conflicts = True
                continue

            if dry_run:
                self.logger.log(f'Will rename: "{src_rel}" → "{tgt_rel}"', LogLevel.ACTION)
            else:
                self._perform_rename(src_path, tgt_path, src_rel, tgt_rel)

        self._log_completion(dry_run, conflicts)
        return not conflicts

    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, int, str]]) -> Dict[Tuple[str, int], List[str]]:
        """
        Maps (hash, size) to a list of file paths.

        Args:
            hash_entries: List of tuples containing (hash, size, relative_path).

        Returns:
            Dictionary mapping (hash, size) to list of relative paths.
        """
        hash_map: Dict[Tuple[str, int], List[str]] = {}
        for file_hash, size, path in hash_entries:
            key = (file_hash, size)
            hash_map.setdefault(key, []).append(path)
        return hash_map

    def _determine_rename_pairs(
        self,
        source_map: Dict[Tuple[str, int], List[str]],
        target_map: Dict[Tuple[str, int], List[str]],
    ) -> List[Tuple[str, str]]:
        """
        Determines the pairs of source and target files to be renamed.

        Args:
            source_map: Mapping of (hash, size) to source file paths.
            target_map: Mapping of (hash, size) to target file paths.

        Returns:
            List of tuples containing (source_path, target_path).
        """
        pairs: List[Tuple[str, str]] = []
        conflicts = []

        for key, src_paths in source_map.items():
            tgt_paths = target_map.get(key, [])
            for src in src_paths:
                if len(tgt_paths) == 0:
                    self.logger.log(f"No matching target for: {src}", LogLevel.WARNING)
                    continue
                elif len(tgt_paths) > 1:
                    self.logger.log(f"Multiple targets found for: {src} | Hash & Size: {key}", LogLevel.WARNING)
                    # Prompt the user to select the correct target
                    selected_tgt = self._prompt_user_selection(src, tgt_paths)
                    if selected_tgt:
                        pairs.append((src, selected_tgt))
                        tgt_paths.remove(selected_tgt)
                    else:
                        self.logger.log(f"Skipped renaming for: {src}", LogLevel.SKIP)
                        continue
                else:
                    pairs.append((src, tgt_paths[0]))

        return pairs

    def _prompt_user_selection(self, src: str, tgt_options: List[str]) -> Optional[str]:
        """
        Prompts the user to select the correct target from multiple options.

        Args:
            src: Source file path.
            tgt_options: List of possible target file paths.

        Returns:
            Selected target file path or None if skipped.
        """
        console = Console()
        console.print(f"\n[bold yellow]Multiple targets found for source file:[/] {src}")
        for idx, tgt in enumerate(tgt_options, start=1):
            console.print(f"  [{idx}] {tgt}")
        console.print("  [0] Skip renaming this file")

        while True:
            choice = Prompt.ask("Select the target to rename to (number)", choices=[str(i) for i in range(0, len(tgt_options)+1)])
            if choice == '0':
                return None
            else:
                selected = tgt_options[int(choice)-1]
                return selected

    def _clean_name(self, name: str) -> str:
        """
        Normalize and sanitize the filename by:
        - Converting to ASCII
        - Lowercasing
        - Removing file extensions
        - Stripping non-alphanumeric characters
        """
        # Normalize Unicode characters to their closest ASCII representation
        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')

        # Convert to lowercase for uniformity
        name = name.lower()

        # Remove the file extension
        name = Path(name).stem

        # Remove all non-alphanumeric characters
        name = re.sub(r'[^a-z0-9]', '', name)

        return name

    def _name_similarity(self, name1: str, name2: str) -> float:
        """
        Calculates similarity between two names.

        Args:
            name1: First sanitized name.
            name2: Second sanitized name.

        Returns:
            Similarity ratio between 0 and 1.
        """
        # Simple similarity metric: ratio of matching characters
        matches = sum(a == b for a, b in zip(name1, name2))
        max_len = max(len(name1), len(name2))
        return matches / max_len if max_len else 0

    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:
        """
        Validates source and target paths before renaming.

        Args:
            src: Source file path.
            tgt: Target file path.

        Returns:
            True if paths are valid for renaming, False otherwise.
        """
        if not src.exists():
            self.logger.log(f"Source missing: {src.relative_to(self.root_dir)}", LogLevel.WARNING)
            return False
        if tgt.exists() and tgt != src:
            self.logger.log(f"Target exists: {tgt.relative_to(self.root_dir)}", LogLevel.ERROR)
            return False
        return True

    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str) -> None:
        """
        Performs the actual file renaming.

        Args:
            src: Source file path.
            tgt: Target file path.
            src_rel: Source relative path.
            tgt_rel: Target relative path.
        """
        try:
            tgt.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src), str(tgt))
            self.logger.log(f'Renamed: "{src_rel}" → "{tgt_rel}"', LogLevel.CHANGE)
        except OSError as error:
            self.logger.log(f"Failed to rename {src_rel}: {error}", LogLevel.ERROR)

    def _preview_renames(self, rename_pairs: List[Tuple[str, str]]) -> None:
        """
        Previews the pending rename operations in a table.

        Args:
            rename_pairs: List of tuples containing (source_path, target_path).
        """
        if not rename_pairs:
            self.logger.log("No files require renaming", LogLevel.WARNING)
            return

        table = Table(
            title="Pending Rename Operations",
            show_header=True,
            header_style="bold blue",
            box=ROUNDED
        )

        table.add_column("Operation", style="cyan", width=4)
        table.add_column("Source", style="white")
        table.add_column("Target", style="green")

        changes = 0
        for src, tgt in rename_pairs:
            if src != tgt:
                table.add_row("→", src, tgt)
                changes += 1

        if changes:
            self.logger.console.print("\n")
            self.logger.console.print(Panel(table, border_style="blue"))
            self.logger.console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")
        else:
            self.logger.log("No files require renaming", LogLevel.WARNING)

    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:
        """
        Logs the completion status of the renaming process.

        Args:
            dry_run: Whether the operation was a dry run.
            has_conflicts: Whether there were any conflicts during renaming.
        """
        operation = "Dry run" if dry_run else "File renaming"
        if has_conflicts:
            self.logger.log(f"{operation}: Conflicts detected, some files skipped", LogLevel.WARNING)
        else:
            self.logger.log(f"{operation} completed successfully", LogLevel.SUMMARY)



class FileEditor:
    """Opens files using the default system editor."""

    @staticmethod
    def open(file_path: pathlib.Path, logger: Logger) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', str(file_path)])
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(['xdg-open', str(file_path)])
            else:
                logger.log(f"Unsupported platform: {sys.platform}", LogLevel.WARNING)
        except Exception as error:
            logger.log(f"Failed to open editor: {error}", LogLevel.ERROR)


def parse_arguments() -> argparse.Namespace:
    """Parses command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Batch Rename Utility with SHA256 Verification",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    subparsers = parser.add_subparsers(dest="command", required=True)

    common_args = argparse.ArgumentParser(add_help=False)
    verbosity_group = common_args.add_argument_group("output options")
    verbosity_group.add_argument(
        "-v", "--verbosity",
        choices=["quiet", "normal", "verbose", "debug"],
        default="normal",
        help="Set output verbosity level"
    )
    verbosity_group.add_argument(
        "--show-skipped",
        action="store_true",
        help="Show skipped files in output"
    )
    verbosity_group.add_argument(
        "--show-unchanged",
        action="store_true",
        help="Show unchanged files in output"
    )
    verbosity_group.add_argument(
        "--prompt",
        action="store_true",
        help="Enable interactive prompting for arguments"
    )

    process_parser = subparsers.add_parser(
        "process",
        parents=[common_args],
        help="Process and rename files",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    process_parser.add_argument(
        "directory", type=str, help="Target directory for processing"
    )
    process_parser.add_argument(
        "--include-subdirectories", action="store_true", help="Include subdirectories"
    )

    return parser.parse_args()


def prompt_for_arguments(args: argparse.Namespace) -> argparse.Namespace:
    """Handle interactive prompting for command arguments."""
    console = Console()

    if not args.prompt:
        return args

    if args.command == "process":
        # Prompt for process command arguments
        directory = Prompt.ask(
            "Enter directory path",
            default=args.directory
        )
        args.directory = directory

        args.include_subdirectories = Confirm.ask(
            "Include subdirectories?",
            default=args.include_subdirectories
        )

    # Validate paths
    root_dir = pathlib.Path(args.directory)
    if not root_dir.exists() or not root_dir.is_dir():
        console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")
        sys.exit(1)

    return args


def configure_logging(args: argparse.Namespace) -> LogConfig:
    """Configures logging based on command-line arguments."""
    verbosity_map = {
        "quiet": VerbosityLevel.QUIET,
        "normal": VerbosityLevel.NORMAL,
        "verbose": VerbosityLevel.VERBOSE,
        "debug": VerbosityLevel.DEBUG
    }

    return LogConfig(
        verbosity=verbosity_map[args.verbosity],
        show_skipped=args.show_skipped,
        show_unchanged=args.show_unchanged
    )


def handle_process_command(args: argparse.Namespace, logger: Logger) -> None:
    """Handles the 'process' command."""
    root_dir = pathlib.Path(args.directory).resolve()
    if not root_dir.is_dir():
        logger.log(f"Invalid directory: {root_dir}", LogLevel.ERROR)
        sys.exit(1)

    # Define internal temporary hash files
    org_file = root_dir / ".original_hashes.py"
    new_file = root_dir / ".new_hashes.py"

    processor = FileProcessor(root_dir, args.include_subdirectories, logger)
    initial_hashes = processor.collect_file_hashes()

    # Write original and new hash files
    for file_path in (org_file, new_file):
        manager = HashFileManager(file_path, logger)
        manager.write(initial_hashes)

    logger.log("Opening new hash file for editing...", LogLevel.ACTION)
    FileEditor.open(new_file, logger)

    if not Confirm.ask("\nProceed with renaming? [y/n]: "):
        logger.log("Operation cancelled by user", LogLevel.WARNING)
        return

    org_manager = HashFileManager(org_file, logger)
    new_manager = HashFileManager(new_file, logger)

    renamer = FileRenamer(root_dir, logger)
    if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):
        if Confirm.ask("\nApply these changes? [y/n]: "):
            renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)

            # Cleanup hash files after successful renaming
            for file_path in (org_file, new_file):
                try:
                    file_path.unlink()
                    logger.log(f"Cleaned up: {file_path}", LogLevel.ACTION)
                except OSError as error:
                    logger.log(f"Cleanup failed: {error}", LogLevel.WARNING)

    logger.print_summary()


def main() -> None:
    """Main entry point of the utility."""
    args = parse_arguments()
    args = prompt_for_arguments(args)
    logger = Logger(configure_logging(args))

    if args.command == "process":
        handle_process_command(args, logger)
    else:
        logger.log("Unknown command", LogLevel.ERROR)


if __name__ == "__main__":
    main()
