<!-- 'https://chatgpt.com/c/6742eec4-e1e0-8008-838c-c83a6b6c95b2' -->

---

problem: the format of the output is unstructured.
solution: format output to markdown, this is readable both in a texteditor but is also possible to render as html.

---

problem: the amount of information given to the user (in the terminal) is too massive and chaotic.
solution: the amount and type of output should be categorized and be controllable through input parameters.

---

example: this is the current output from command: `python v3_1_b_3.py process ".\_teststructure" --include-subdirectories`

output.a:
```
Generating hash files for
'C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure'..
.
Processed: codebase.md
Processed: Q3_2022_PrepareAForm.webm
Processed: SelectEditMenu.webm
Processed: SelectFinish.webm
Processed: SelectHighlightTool.webm
Processed: SelectSignMegaVerbShortTutorial.webm
Processed: SelectSignTool.webm
Processed: SelectTextTool.webm
Processed: subdir/GenTech_MultiDoc_de.webm
Processed: subdir/GenTech_MultiDoc_en.webm
Processed: subdir/GenTech_MultiDoc_fr.webm
Processed: subdir/GenTech_MultiDoc_jp.webm
Processed: subdir/GetMobileApp.webm
Processed: subdir/LeaveFeedbackDesktop-DARK.webm
Processed: subdir/NewIcons_LeaveFeedback-DARK.webm
Processed: subdir/NewIcons_LeaveFeedback2-LIGHT.webm
Processed: subdir/NewIcons_NoMatches-DARK.webm
Processed: subdir/NewIcons_StartConversation-DARK.webm
Processed: subdir/NewIcons_StartConversation-LIGHT.webm
Processed: subdir/NoMatchesDesktop-DARK.webm
Hashes written to
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\FilesToText__filen
ames_ORG.txt
Hashes written to
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\FilesToText__filen
ames_NEW.txt
Hash files generated successfully.
Opening
'C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\FilesToText__file
names_NEW.txt' for editing...
```

intermediate: user updates `FilesToText__filenames_NEW.txt` from
```
6c2498674847f305b1587f1f466fd0c55cfb84f7270900086fa25a11edf42690|codebase.md
0806ce2a7f7030c3077ae24ef6d94680f9b2c3e9c7a115c9ea5616a947d13ada|Q3_2022_PrepareAForm.webm
b88695c02c73aadb94790caf0e71cd4f5748aaf63a695f977e04f9dc66e8834b|SelectEditMenu.webm
85fd58a59683e215f16a1d98aa871d3b68d723a927c3561a72e9d6ae27a9576f|SelectFinish.webm
2dd10d7230109ef3bb48f4065cb696c6b2f3e5fa3e03bf24e636f69c3ffb46c2|SelectHighlightTool.webm
640dfb0de294fdbfe8bd1c18c4c4a82b017dc2be20493e18b0b01ccc88d7202e|SelectSignMegaVerbShortTutorial.webm
a7e33c65da2ae27a007b2e5134d11ea1e51beb3802b5890660e09529fbea0c3a|SelectSignTool.webm
955c67e5d8226d437c5acb2c4c0efb05497421c4657c72d8e9d19dd8fd54342c|SelectTextTool.webm
892c4cf87b72bb0b6b5b3777d3131c21188e9bb6e7e0b107214e0f387ae9e5f8|subdir/GenTech_MultiDoc_de.webm
c960b2ee55bca05ceacd83b29504642b1b5ce524a5ac24f0897a9d31cfe21efe|subdir/GenTech_MultiDoc_en.webm
ff86bf194b4a3f8e218329e86d506a2089f771d053c40f6eb05a848cb6c37d99|subdir/GenTech_MultiDoc_fr.webm
6ea0a25846eab3f44b847b550ac5bb8b30f9e4aad83a3416eed38edc54b9ce12|subdir/GenTech_MultiDoc_jp.webm
30687066c78071781ebc8e6affea0b619b404a5e51ce82efcc48257fedc98b2e|subdir/GetMobileApp.webm
6ca4c508d7a14c1d9420866d4f5deba716a0d0e4fce0bc131ae65dcf1804f3f9|subdir/LeaveFeedbackDesktop-DARK.webm
690aacd6269e9df3f8426a21ef08a1e2bbd59aba2094e6b24f5f256434074d96|subdir/NewIcons_LeaveFeedback-DARK.webm
f82d634b7a621153c5afb243c9d7cdc574b143ab9dfc0fcee5f52708beb66da2|subdir/NewIcons_LeaveFeedback2-LIGHT.webm
3077a5554ca84d021c0bd241174266a0144841ec4e3434129cdc85f89b0c1222|subdir/NewIcons_NoMatches-DARK.webm
a00f67d68e55e9decaccd8e34caaef0500b0a481acfa5fbc0cf4813cba264498|subdir/NewIcons_StartConversation-DARK.webm
b765b990f0d8e7a38573ab84dfd8d197f37f41cac1125d00dbc90b26aac82d41|subdir/NewIcons_StartConversation-LIGHT.webm
3a4f1424d88cf3771a085f2b6c219864f7d04b8dfb9e63bfc388752aa6cec94c|subdir/NoMatchesDesktop-DARK.webm
```

to
```
6c2498674847f305b1587f1f466fd0c55cfb84f7270900086fa25a11edf42690|codebase.md
0806ce2a7f7030c3077ae24ef6d94680f9b2c3e9c7a115c9ea5616a947d13ada|Q3_2022_PrepareAForm.webm
b88695c02c73aadb94790caf0e71cd4f5748aaf63a695f977e04f9dc66e8834b|SelectEditMenu.webm
85fd58a59683e215f16a1d98aa871d3b68d723a927c3561a72e9d6ae27a9576f|SelectFinish.webm
2dd10d7230109ef3bb48f4065cb696c6b2f3e5fa3e03bf24e636f69c3ffb46c2|SelectHighlightTool.webm
640dfb0de294fdbfe8bd1c18c4c4a82b017dc2be20493e18b0b01ccc88d7202e|SelectSignMegaVerbShortTutorial.webm
a7e33c65da2ae27a007b2e5134d11ea1e51beb3802b5890660e09529fbea0c3a|SelectSignTool.webm
955c67e5d8226d437c5acb2c4c0efb05497421c4657c72d8e9d19dd8fd54342c|SelectTextTool.webm
892c4cf87b72bb0b6b5b3777d3131c21188e9bb6e7e0b107214e0f387ae9e5f8|gen_subdir/GenTech_MultiDoc_de.webm
c960b2ee55bca05ceacd83b29504642b1b5ce524a5ac24f0897a9d31cfe21efe|gen_subdir/GenTech_MultiDoc_en.webm
ff86bf194b4a3f8e218329e86d506a2089f771d053c40f6eb05a848cb6c37d99|gen_subdir/GenTech_MultiDoc_fr.webm
6ea0a25846eab3f44b847b550ac5bb8b30f9e4aad83a3416eed38edc54b9ce12|gen_subdir/GenTech_MultiDoc_jp.webm
30687066c78071781ebc8e6affea0b619b404a5e51ce82efcc48257fedc98b2e|gen_subdir/GetMobileApp.webm
6ca4c508d7a14c1d9420866d4f5deba716a0d0e4fce0bc131ae65dcf1804f3f9|subdir/LeaveFeedbackDesktop-DARK.webm
690aacd6269e9df3f8426a21ef08a1e2bbd59aba2094e6b24f5f256434074d96|subdir/NewIcons_LeaveFeedback-DARK.webm
f82d634b7a621153c5afb243c9d7cdc574b143ab9dfc0fcee5f52708beb66da2|subdir/NewIcons_LeaveFeedback2-LIGHT.webm
3077a5554ca84d021c0bd241174266a0144841ec4e3434129cdc85f89b0c1222|subdir/NewIcons_NoMatches-DARK.webm
a00f67d68e55e9decaccd8e34caaef0500b0a481acfa5fbc0cf4813cba264498|subdir/NewIcons_StartConversation-DARK.webm
b765b990f0d8e7a38573ab84dfd8d197f37f41cac1125d00dbc90b26aac82d41|subdir/NewIcons_StartConversation-LIGHT.webm
3a4f1424d88cf3771a085f2b6c219864f7d04b8dfb9e63bfc388752aa6cec94c|subdir/NoMatchesDesktop-DARK.webm
```

prompt: `Have you updated the new filenames in the hash file? Proceed to dry run and renaming. [y/n]: y`
output.b:
```
Regenerating hash files to get the current file structure...
Processed: codebase.md
Processed: Q3_2022_PrepareAForm.webm
Processed: SelectEditMenu.webm
Processed: SelectFinish.webm
Processed: SelectHighlightTool.webm
Processed: SelectSignMegaVerbShortTutorial.webm
Processed: SelectSignTool.webm
Processed: SelectTextTool.webm
Processed: subdir/GenTech_MultiDoc_de.webm
Processed: subdir/GenTech_MultiDoc_en.webm
Processed: subdir/GenTech_MultiDoc_fr.webm
Processed: subdir/GenTech_MultiDoc_jp.webm
Processed: subdir/GetMobileApp.webm
Processed: subdir/LeaveFeedbackDesktop-DARK.webm
Processed: subdir/NewIcons_LeaveFeedback-DARK.webm
Processed: subdir/NewIcons_LeaveFeedback2-LIGHT.webm
Processed: subdir/NewIcons_NoMatches-DARK.webm
Processed: subdir/NewIcons_StartConversation-DARK.webm
Processed: subdir/NewIcons_StartConversation-LIGHT.webm
Processed: subdir/NoMatchesDesktop-DARK.webm
Hashes written to
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\FilesToText__filen
ames_ORG.txt
Updated hash files generated successfully.
Performing dry run to preview changes...
No change for SelectFinish.webm. Skipping.
No change for subdir/NoMatchesDesktop-DARK.webm. Skipping.
No change for SelectHighlightTool.webm. Skipping.
Would rename/move:
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\sub
dir\GenTech_MultiDoc_en.webm ->
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\gen
_subdir\GenTech_MultiDoc_en.webm
Would rename/move:
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\sub
dir\GenTech_MultiDoc_jp.webm ->
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\gen
_subdir\GenTech_MultiDoc_jp.webm
No change for SelectEditMenu.webm. Skipping.
No change for subdir/LeaveFeedbackDesktop-DARK.webm. Skipping.
Would rename/move:
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\sub
dir\GenTech_MultiDoc_fr.webm ->
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\gen
_subdir\GenTech_MultiDoc_fr.webm
No change for subdir/NewIcons_LeaveFeedback2-LIGHT.webm. Skipping.
Would rename/move:
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\sub
dir\GenTech_MultiDoc_de.webm ->
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\gen
_subdir\GenTech_MultiDoc_de.webm
No change for subdir/NewIcons_StartConversation-DARK.webm. Skipping.
No change for codebase.md. Skipping.
No change for subdir/NewIcons_StartConversation-LIGHT.webm. Skipping.
No change for SelectTextTool.webm. Skipping.
No change for SelectSignMegaVerbShortTutorial.webm. Skipping.
No change for subdir/NewIcons_NoMatches-DARK.webm. Skipping.
Would rename/move:
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\sub
dir\GetMobileApp.webm ->
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\gen
_subdir\GetMobileApp.webm
No change for SelectSignTool.webm. Skipping.
No change for subdir/NewIcons_LeaveFeedback-DARK.webm. Skipping.
No change for Q3_2022_PrepareAForm.webm. Skipping.
```

prompt: `Do you want to proceed with these changes? [y/n]: y`
output.c:
```
Executing renaming...
No change for SelectFinish.webm. Skipping.
No change for subdir/NoMatchesDesktop-DARK.webm. Skipping.
No change for SelectHighlightTool.webm. Skipping.
Renamed/Moved:
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\sub
dir\GenTech_MultiDoc_en.webm ->
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\gen
_subdir\GenTech_MultiDoc_en.webm
Renamed/Moved:
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\sub
dir\GenTech_MultiDoc_jp.webm ->
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\gen
_subdir\GenTech_MultiDoc_jp.webm
No change for SelectEditMenu.webm. Skipping.
No change for subdir/LeaveFeedbackDesktop-DARK.webm. Skipping.
Renamed/Moved:
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\sub
dir\GenTech_MultiDoc_fr.webm ->
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\gen
_subdir\GenTech_MultiDoc_fr.webm
No change for subdir/NewIcons_LeaveFeedback2-LIGHT.webm. Skipping.
Renamed/Moved:
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\sub
dir\GenTech_MultiDoc_de.webm ->
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\gen
_subdir\GenTech_MultiDoc_de.webm
No change for subdir/NewIcons_StartConversation-DARK.webm. Skipping.
No change for codebase.md. Skipping.
No change for subdir/NewIcons_StartConversation-LIGHT.webm. Skipping.
No change for SelectTextTool.webm. Skipping.
No change for SelectSignMegaVerbShortTutorial.webm. Skipping.
No change for subdir/NewIcons_NoMatches-DARK.webm. Skipping.
Renamed/Moved:
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\sub
dir\GetMobileApp.webm ->
C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure\gen
_subdir\GetMobileApp.webm
No change for SelectSignTool.webm. Skipping.
No change for subdir/NewIcons_LeaveFeedback-DARK.webm. Skipping.
No change for Q3_2022_PrepareAForm.webm. Skipping.
File renaming completed successfully.
```
---

goal: lets start with correcting the output to be output like this (example):

```markdown
<!-- Begin Log -->
Generating hash files for
`C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\_teststructure`...

- Processed: `codebase.md`
- Processed: `Q3_2022_PrepareAForm.webm`
- Processed: `SelectEditMenu.webm`
- Processed: `SelectFinish.webm`
- Processed: `SelectHighlightTool.webm`
- Processed: `SelectSignMegaVerbShortTutorial.webm`
- Processed: `SelectSignTool.webm`
- Processed: `SelectTextTool.webm`
- Processed: `subdir/GenTech_MultiDoc_de.webm`
- Processed: `subdir/GenTech_MultiDoc_en.webm`
- Processed: `subdir/GenTech_MultiDoc_fr.webm`
- Processed: `subdir/GenTech_MultiDoc_jp.webm`
- Processed: `subdir/GetMobileApp.webm`
- Processed: `subdir/LeaveFeedbackDesktop-DARK.webm`
- Processed: `subdir/NewIcons_LeaveFeedback-DARK.webm`
- Processed: `subdir/NewIcons_LeaveFeedback2-LIGHT.webm`
- Processed: `subdir/NewIcons_NoMatches-DARK.webm`
- Processed: `subdir/NewIcons_StartConversation-DARK.webm`
- Processed: `subdir/NewIcons_StartConversation-LIGHT.webm`
- Processed: `subdir/NoMatchesDesktop-DARK.webm`

Hashes written to
`C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\FilesToText__filenames_ORG.txt`

Hashes written to
`C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\FilesToText__filenames_NEW.txt`

Hash files generated successfully.
Opening `C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\FilesToText__filenames_NEW.txt` for editing...

---

<!-- Intermediate Section -->
User updates `FilesToText__filenames_NEW.txt`:
```plaintext
6c2498674847f305b1587f1f466fd0c55cfb84f7270900086fa25a11edf42690|codebase.md
0806ce2a7f7030c3077ae24ef6d94680f9b2c3e9c7a115c9ea5616a947d13ada|Q3_2022_PrepareAForm.webm
b88695c02c73aadb94790caf0e71cd4f5748aaf63a695f977e04f9dc66e8834b|SelectEditMenu.webm
85fd58a59683e215f16a1d98aa871d3b68d723a927c3561a72e9d6ae27a9576f|SelectFinish.webm
2dd10d7230109ef3bb48f4065cb696c6b2f3e5fa3e03bf24e636f69c3ffb46c2|SelectHighlightTool.webm
640dfb0de294fdbfe8bd1c18c4c4a82b017dc2be20493e18b0b01ccc88d7202e|SelectSignMegaVerbShortTutorial.webm
a7e33c65da2ae27a007b2e5134d11ea1e51beb3802b5890660e09529fbea0c3a|SelectSignTool.webm
955c67e5d8226d437c5acb2c4c0efb05497421c4657c72d8e9d19dd8fd54342c|SelectTextTool.webm
892c4cf87b72bb0b6b5b3777d3131c21188e9bb6e7e0b107214e0f387ae9e5f8|gen_subdir/GenTech_MultiDoc_de.webm
c960b2ee55bca05ceacd83b29504642b1b5ce524a5ac24f0897a9d31cfe21efe|gen_subdir/GenTech_MultiDoc_en.webm
ff86bf194b4a3f8e218329e86d506a2089f771d053c40f6eb05a848cb6c37d99|gen_subdir/GenTech_MultiDoc_fr.webm
6ea0a25846eab3f44b847b550ac5bb8b30f9e4aad83a3416eed38edc54b9ce12|gen_subdir/GenTech_MultiDoc_jp.webm
30687066c78071781ebc8e6affea0b619b404a5e51ce82efcc48257fedc98b2e|gen_subdir/GetMobileApp.webm
6ca4c508d7a14c1d9420866d4f5deba716a0d0e4fce0bc131ae65dcf1804f3f9|subdir/LeaveFeedbackDesktop-DARK.webm
690aacd6269e9df3f8426a21ef08a1e2bbd59aba2094e6b24f5f256434074d96|subdir/NewIcons_LeaveFeedback-DARK.webm
f82d634b7a621153c5afb243c9d7cdc574b143ab9dfc0fcee5f52708beb66da2|subdir/NewIcons_LeaveFeedback2-LIGHT.webm
3077a5554ca84d021c0bd241174266a0144841ec4e3434129cdc85f89b0c1222|subdir/NewIcons_NoMatches-DARK.webm
a00f67d68e55e9decaccd8e34caaef0500b0a481acfa5fbc0cf4813cba264498|subdir/NewIcons_StartConversation-DARK.webm
b765b990f0d8e7a38573ab84dfd8d197f37f41cac1125d00dbc90b26aac82d41|subdir/NewIcons_StartConversation-LIGHT.webm
3a4f1424d88cf3771a085f2b6c219864f7d04b8dfb9e63bfc388752aa6cec94c|subdir/NoMatchesDesktop-DARK.webm
```

---

<!-- Prompt and Output -->
**Prompt:** Have you updated the new filenames in the hash file? Proceed to dry run and renaming. [y/n]: `y`

Regenerating hash files to get the current file structure...
- Processed: `codebase.md`
- Processed: `Q3_2022_PrepareAForm.webm`
- ...
- Processed: `subdir/NewIcons_StartConversation-LIGHT.webm`

Hashes written to
`C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Utils_Todo\py__RenameWithEditor\v3\FilesToText__filenames_ORG.txt`

Updated hash files generated successfully.
Performing dry run to preview changes...
- No change for `SelectFinish.webm`. Skipping.
- Would rename/move:
  `subdir/GenTech_MultiDoc_en.webm` -> `gen_subdir/GenTech_MultiDoc_en.webm`
- ...

File renaming completed successfully.
```

