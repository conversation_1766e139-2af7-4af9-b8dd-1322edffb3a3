# 'ChatGPT o1-preview'
# 'https://chatgpt.com/c/6742dfd4-4350-8008-a36b-87c1d886d5c6'

import hashlib
import os
import sys

def sha256_checksum(file_path):
    """Compute the SHA256 hash of a file."""
    sha256 = hashlib.sha256()
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b''):
            sha256.update(chunk)
    return sha256.hexdigest()

def get_file_hashes(input_directory, relative_path='', recursive=False):
    """
    Get the SHA256 hash and relative path for each file in a directory.
    If recursive is True, it will include files in subdirectories.
    """
    file_hashes = []
    for entry in os.listdir(input_directory):
        entry_path = os.path.join(input_directory, entry)
        entry_relative_path = os.path.join(relative_path, entry)
        if os.path.isfile(entry_path) and os.access(entry_path, os.R_OK):
            file_hash = sha256_checksum(entry_path)
            file_hashes.append((file_hash, entry_relative_path))
            print(f"Found file: {entry_relative_path}")
        elif os.path.isdir(entry_path) and recursive:
            file_hashes.extend(get_file_hashes(entry_path, entry_relative_path, recursive))
    return file_hashes

def write_file_hashes(file_hashes, org_file, new_file):
    """Write the SHA256 hashes and filenames to two text files."""
    formatted_hashes = [f"{h[0]}|{h[1]}" for h in file_hashes]
    with open(org_file, "w", encoding='utf-8') as f:
        f.write("\n".join(formatted_hashes))
    with open(new_file, "w", encoding='utf-8') as f:
        f.write("\n".join(formatted_hashes))
    print(f"Original filenames written to {org_file}")
    print(f"Editable filenames written to {new_file}")

def rename_files(input_directory, new_file):
    """Rename files based on the edited new filenames file."""
    # Read the new filenames
    with open(new_file, "r", encoding='utf-8') as f:
        new_filenames = [line.strip() for line in f if line.strip()]

    # Parse the new filenames into a dictionary {hash: new_relative_path}
    new_filenames_dict = {}
    for line in new_filenames:
        parts = line.split('|', 1)
        if len(parts) != 2:
            print(f"Invalid line in new filenames file: {line}")
            continue
        file_hash, new_relative_path = parts
        new_filenames_dict[file_hash] = new_relative_path

    # Get the current file hashes
    file_hashes = get_file_hashes(input_directory, recursive=True)

    # For each file, check if there's a new filename, and rename
    for file_hash, current_relative_path in file_hashes:
        if file_hash in new_filenames_dict:
            new_relative_path = new_filenames_dict[file_hash]
            if current_relative_path != new_relative_path:
                current_full_path = os.path.join(input_directory, current_relative_path)
                new_full_path = os.path.join(input_directory, new_relative_path)
                new_dir = os.path.dirname(new_full_path)
                if not os.path.exists(new_dir):
                    os.makedirs(new_dir)
                os.rename(current_full_path, new_full_path)
                print(f"Renamed: {current_relative_path} -> {new_relative_path}")
            else:
                print(f"No change for: {current_relative_path}")
        else:
            print(f"No new filename for hash: {file_hash}, file: {current_relative_path}")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Batch rename files using a text editor and matching files based on their SHA256 hashes.")
    parser.add_argument("input_directory", nargs='?', default=os.getcwd(), help="Directory containing files to process.")
    parser.add_argument("--recursive", action="store_true", help="Process files recursively in subdirectories.")
    parser.add_argument("--rename", action="store_true", help="Rename files based on the edited new filenames file.")
    args = parser.parse_args()

    input_directory = args.input_directory
    recursive = args.recursive
    perform_rename = args.rename

    # Define output text files
    out_textfile_org = os.path.join(os.getcwd(), 'file_hashes_original.txt')
    out_textfile_new = os.path.join(os.getcwd(), 'file_hashes_new.txt')

    if not perform_rename:
        # Generate the file hashes and write to text files
        file_hashes = get_file_hashes(input_directory, recursive=recursive)
        write_file_hashes(file_hashes, out_textfile_org, out_textfile_new)
        print("You can now edit the new filenames in 'file_hashes_new.txt'.")
    else:
        # Perform the renaming
        rename_files(input_directory, out_textfile_new)
        print("Renaming completed.")
