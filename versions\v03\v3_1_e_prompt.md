<!-- 'https://chatgpt.com/c/6742eec4-e1e0-8008-838c-c83a6b6c95b2' -->

it was a bad idea to output markdown-syntax directly to the terminal. a better idea would be to simplify the terminal output to the user, and instead make changes to the generated files (`FilesToText__filenames_OLD.txt` and `FilesToText__filenames_NEW.txt`). these could be changed to e.g. .py and we could represent them like this (example):
```
#
# ['hash'] | 'codebase.md'
# ['hash'] | 'Q3_2022_PrepareAForm.webm'
# ['hash'] | 'SelectEditMenu.webm'
# ['hash'] | 'SelectFinish.webm'
# ['hash'] | 'SelectHighlightTool.webm'
# ['hash'] | 'SelectSignMegaVerbShortTutorial.webm'
# ['hash'] | 'SelectSignTool.webm'
# ['hash'] | 'SelectTextTool.webm'
# ['hash'] | '_teststructure.md'
#
# ['hash'] | 'gen_subdir_x1/GenTech_MultiDoc_fr.webm'
# ['hash'] | 'gen_subdir_x1/GenTech_MultiDoc_jp.webm'
# ['hash'] | 'gen_subdir_x1/GetMobileApp.webm'
# ['hash'] | 'gen_subdir_x2/GenTech_MultiDoc_de.webm'
# ['hash'] | 'gen_subdir_x2/GenTech_MultiDoc_en.webm'
#
# ['hash'] | 'subdir/LeaveFeedbackDesktop-DARK.webm'
# ['hash'] | 'subdir/NewIcons_LeaveFeedback-DARK.webm'
# ['hash'] | 'subdir/NewIcons_LeaveFeedback2-LIGHT.webm'
# ['hash'] | 'subdir/NewIcons_NoMatches-DARK.webm'
# ['hash'] | 'subdir/NewIcons_StartConversation-DARK.webm'
# ['hash'] | 'subdir/NewIcons_StartConversation-LIGHT.webm'
# ['hash'] | 'subdir/NoMatchesDesktop-DARK.webm'
#
```
---

keep in mind the key points:
- Ensure that the overall architecture of script is concistent and well-structured.
- Ensure the code include only brief, high-value comments that clarify the purpose of sections or explain complex logic, avoiding excessive commentary.


