
# Further Enhancements: Extending the Philosophy

Based on our discussions and the implementation of the essential duality concept, here are additional improvements that align with the philosophical foundation we've established:

## 1. Format Templates

Allow users to define their own presentation templates beyond the built-in options. This extends the "presentation language" while maintaining the essential contract:

```python
def format_from_template(hash_entries, template):
    """Format hash entries according to a user-defined template."""
    formatted_lines = []
    for file_hash, filename, date_str, depth_str, size_str, content_str in hash_entries:
        context = {
            "hash": file_hash,
            "filename": filename,
            "date": date_str,
            "depth": depth_str,
            "size": size_str,
            "content": content_str
        }
        line = template.format(**context)
        formatted_lines.append(line)
    return formatted_lines
```

## 2. Enhanced Visual Diff Preview

When showing rename previews, highlight only what's changed in paths rather than showing the complete path, making it easier to verify changes:

```python
def highlight_difference(source, target):
    """Highlight only the changed parts of filenames."""
    parts_source = source.split('/')
    parts_target = target.split('/')

    # Find common prefix and suffix
    i = 0
    while i < min(len(parts_source), len(parts_target)) and parts_source[i] == parts_target[i]:
        i += 1

    modified_target = f"{'/'.join(parts_source[:i])}/[bold green]{'/'.join(parts_target[i:])}[/]"
    return modified_target
```

## 3. Operation Preview Types

Extend the preview with different visualization modes that expose different aspects of the transformation:

```python
def preview_by_type(rename_pairs, type='standard'):
    """Generate different views of the pending changes."""
    if type == 'directory_impact':
        # Group by directory to see structural changes
        by_dir = defaultdict(list)
        for src, tgt in rename_pairs:
            src_dir = os.path.dirname(src) or '.'
            by_dir[src_dir].append((src, tgt))
        # Return directory-focused view
    elif type == 'pattern_based':
        # Show common patterns in the renames
        # Useful for understanding batch operations
    return view
```

## 4. Contextual Hash Extraction

Enhance the hash extraction to adapt to document context, making it even more resilient to format changes:

```python
def extract_by_context(line):
    """Extract hash and filename using document context clues."""
    # Basic extraction
    filename_match = re.search(r'"([^"]+)"', line)
    hash_match = re.search(r'\'([a-fA-F0-9]{64})\'', line)

    # Further context analysis
    if filename_match and not hash_match:
        # Look harder for hash based on proximity to filename
        # This handles cases where quotes might be different

    # Additional context analysis...
    return filename, file_hash
```

## 5. Action Effects Modeling

Add capabilities to model the effects of operations before executing them, detecting potential issues:

```python
def simulate_effects(rename_pairs, root_dir):
    """Model the effects of rename operations to detect issues."""
    # Create virtual filesystem
    vfs = VirtualFS.from_directory(root_dir)

    # Apply changes in simulation
    issues = []
    for src, tgt in rename_pairs:
        try:
            vfs.move(src, tgt)
        except Exception as e:
            issues.append((src, tgt, str(e)))

    return issues
```

## 6. Path Intelligence

Add smarter path handling that understands semantic components of paths beyond simple string manipulation:

```python
class PathIntelligence:
    """Understand path components semantically."""

    @staticmethod
    def detect_numbering_scheme(filenames):
        """Detect if files follow a numbering scheme."""
        patterns = []
        for name in filenames:
            # Detect patterns like 001_, file-01, etc.
            match = re.search(r'(\d+)([_\-\.])?', name)
            if match:
                patterns.append((match.group(1), match.start(), match.end()))

        # Analyze pattern consistency
        return dominant_pattern
```

## 7. Minimalist Configuration

Add a minimal configuration system that remains true to the philosophy of simplicity:

```python
class Config:
    """Minimalist configuration with smart defaults."""

    @staticmethod
    def load(config_path=None):
        """Load configuration with priority: explicit > file > environment > defaults."""
        config = {}

        # Default values
        config.update(Config.DEFAULTS)

        # Environment variables override defaults
        config.update({k.lower(): v for k, v in os.environ.items() if k.startswith('RENAME_')})

        # Config file overrides environment
        if config_path and os.path.exists(config_path):
            with open(config_path) as f:
                file_config = yaml.safe_load(f)
                config.update(file_config)

        return config
```

All these enhancements maintain the core philosophical principle of the essential duality - they extend the utility's capabilities while respecting the separation between rich presentation for humans and essential contract for operations.

The focus throughout remains on enhancing the user experience of the "spatial manipulation medium" you've created, while keeping the core mechanism simple and robust.
