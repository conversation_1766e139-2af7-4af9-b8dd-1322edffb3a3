# 'https://chatgpt.com/c/67442d52-e64c-8008-ac21-d3d75fd84f26'


""" Utility for batch renaming files through an intermediary text editor """

import argparse
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
import re
import unicodedata

from pathlib import Path
from dataclasses import dataclass
from enum import Enum, auto
from typing import Dict, List, Optional, Set, Tuple

from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.box import ROUNDED


class VerbosityLevel(Enum):
    QUIET = auto()
    NORMAL = auto()
    VERBOSE = auto()
    DEBUG = auto()


class LogLevel(Enum):
    PROCESSED = auto()
    WARNING = auto()
    ERROR = auto()
    ACTION = auto()
    SUMMARY = auto()
    CHANGE = auto()
    SKIP = auto()
    DEBUG = auto()


@dataclass
class LogConfig:
    verbosity: VerbosityLevel
    show_skipped: bool = False
    show_unchanged: bool = False


class Logger:
    """Handles logging with different verbosity levels and styles."""

    def __init__(self, config: LogConfig):
        self.config = config
        self.console = Console(highlight=False)
        self.changes = 0
        self.skips = 0
        self.errors = 0

        self.level_mapping = {
            VerbosityLevel.QUIET: {LogLevel.ERROR, LogLevel.SUMMARY, LogLevel.CHANGE},
            VerbosityLevel.NORMAL: {
                LogLevel.ERROR,
                LogLevel.SUMMARY,
                LogLevel.CHANGE,
                LogLevel.WARNING,
            },
            VerbosityLevel.VERBOSE: {
                LogLevel.ERROR,
                LogLevel.SUMMARY,
                LogLevel.CHANGE,
                LogLevel.WARNING,
                LogLevel.PROCESSED,
                LogLevel.ACTION,
            },
            VerbosityLevel.DEBUG: set(LogLevel),
        }

    def should_log(self, level: LogLevel) -> bool:
        return level in self.level_mapping[self.config.verbosity]

    def log(self, message: str, level: LogLevel, details: Optional[str] = None) -> None:
        if not self.should_log(level):
            return

        if level == LogLevel.SKIP and not self.config.show_skipped:
            self.skips += 1
            return

        styles = {
            LogLevel.ERROR: "bold red",
            LogLevel.WARNING: "yellow",
            LogLevel.CHANGE: "bold green",
            LogLevel.SUMMARY: "bold blue",
            LogLevel.ACTION: "blue",
            LogLevel.PROCESSED: "bright_black",
            LogLevel.SKIP: "bright_black",
            LogLevel.DEBUG: "dim cyan",
        }

        prefixes = {
            LogLevel.ERROR: "❌",
            LogLevel.WARNING: "⚠️ ",
            LogLevel.CHANGE: "✨",
            LogLevel.SUMMARY: "📋",
            LogLevel.ACTION: "🔄",
            LogLevel.PROCESSED: "📝",
            LogLevel.SKIP: "⏭️ ",
            LogLevel.DEBUG: "🐞 ",
        }

        if level == LogLevel.ERROR:
            self.errors += 1
        elif level == LogLevel.CHANGE:
            self.changes += 1

        styled_message = f"[{styles[level]}]{prefixes[level]} {message}[/]"

        if details and self.config.verbosity == VerbosityLevel.DEBUG:
            styled_message += f"\n  [{styles[level]}]{details}[/]"

        self.console.print(styled_message)

    def print_summary(self) -> None:
        if not self.should_log(LogLevel.SUMMARY):
            return

        table = Table(title="Operation Summary", show_header=False, box=None)
        table.add_column("Type", style="bold")
        table.add_column("Count", justify="right")

        table.add_row("Changes made", str(self.changes))
        if self.config.show_skipped:
            table.add_row("Items skipped", str(self.skips))
        if self.errors > 0:
            table.add_row("Errors encountered", f"[red]{self.errors}[/]")

        self.console.print("\n")
        self.console.print(Panel(table, border_style="blue"))


class FileHasher:
    """Computes SHA256 hashes for files."""

    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path, logger: Logger) -> Optional[str]:
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.log(f"Error reading `{file_path}`: {error}", LogLevel.ERROR)
            return None


class FileProcessor:
    """Processes files and directories to collect their hashes."""

    def __init__(self, root_dir: pathlib.Path, include_subdirs: bool, logger: Logger):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs
        self.logger = logger

    def collect_file_and_dir_hashes(self) -> List[Tuple[str, str, str]]:
        """
        Collects hashes for files and directories.
        Returns a list of tuples: (type, hash, relative_path)
        Type is 'FILE' or 'DIR'.
        """
        hash_entries = []
        for root, dirs, files in os.walk(self.root_dir):
            # Process directories
            for dirname in dirs:
                dir_path = pathlib.Path(root) / dirname
                if not self._is_accessible_dir(dir_path):
                    continue

                relative_path = dir_path.relative_to(self.root_dir).as_posix()
                # For directories, use a consistent hash based on the relative path
                # since directories don't have content to hash
                dir_hash = hashlib.sha256(relative_path.encode('utf-8')).hexdigest()
                hash_entries.append(('DIR', dir_hash, relative_path))
                self.logger.log(f"Processed directory: {relative_path}", LogLevel.PROCESSED)

            # Process files
            for filename in files:
                file_path = pathlib.Path(root) / filename
                if not self._is_accessible_file(file_path):
                    continue

                relative_path = file_path.relative_to(self.root_dir).as_posix()
                file_hash = FileHasher.compute_sha256(file_path, self.logger)

                if file_hash:
                    hash_entries.append(('FILE', file_hash, relative_path))
                    self.logger.log(f"Processed file: {relative_path}", LogLevel.PROCESSED)

            if not self.include_subdirs:
                break
        return hash_entries

    def _is_accessible_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            self.logger.log(f"`{path}` is not accessible or not a file", LogLevel.WARNING)
            return False
        return True

    def _is_accessible_dir(self, path: pathlib.Path) -> bool:
        if not path.is_dir() or not os.access(path, os.R_OK):
            self.logger.log(f"`{path}` is not accessible or not a directory", LogLevel.WARNING)
            return False
        return True


class HashFileManager:
    """Manages reading and writing hash files."""

    def __init__(self, file_path: pathlib.Path, logger: Logger):
        self.file_path = file_path
        self.logger = logger

    def write(self, hash_entries: List[Tuple[str, str, str]]) -> None:
        if not hash_entries:
            self.logger.log("No files or directories found to process.", LogLevel.ERROR)
            sys.exit(1)  # Exit gracefully

        try:
            max_length = max(len(entry[2]) for entry in hash_entries) + 2
            with self.file_path.open("w", encoding='utf-8') as f:
                f.write("# Hash to Path Mapping\n")
                for entry_type, file_hash, relative_path in sorted(hash_entries, key=lambda x: x[2].lower()):
                    padded_path = f"'{relative_path}'".ljust(max_length)
                    f.write(f"- {padded_path} # | \"{file_hash}\" | {entry_type}\n")
            self.logger.log(f"Hash file written: {self.file_path}", LogLevel.ACTION)
        except IOError as error:
            self.logger.log(f"Failed to write hash file: {error}", LogLevel.ERROR)

    def read(self) -> List[Tuple[str, str, str]]:
        hash_entries = []
        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line in f:
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            self.logger.log(f"Failed to read hash file: {error}", LogLevel.ERROR)
        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str, str]]:
        line = line.strip()
        if not (line.startswith("- '") and ' # | "' in line and ' | ' in line and line.endswith('"')):
            return None

        try:
            # Example line format:
            # - 'relative_path' # | "hash" | TYPE
            parts = line.split(" # | \"")
            path_part = parts[0].strip("- '")
            hash_and_type = parts[1].split("\" | ")
            file_hash = hash_and_type[0]
            entry_type = hash_and_type[1] if len(hash_and_type) > 1 else "FILE"
            return (entry_type, file_hash, path_part)
        except (IndexError, ValueError):
            self.logger.log(f"Invalid hash file entry: {line}", LogLevel.WARNING)
            return None


class FileRenamer:
    """Handles the renaming of files and directories based on hash comparisons."""

    def __init__(self, root_dir: pathlib.Path, logger: Logger):
        self.root_dir = root_dir
        self.logger = logger

    def execute(
        self,
        source_hashes: List[Tuple[str, str, str]],
        target_hashes: List[Tuple[str, str, str]],
        dry_run: bool = True
    ) -> bool:
        source_map = self._map_hash_to_paths(source_hashes)
        target_map = self._map_hash_to_paths(target_hashes)

        rename_pairs = self._determine_rename_pairs(source_map, target_map)
        conflicts = False

        if dry_run:
            self._preview_renames(rename_pairs)

        # Sort rename_pairs to handle directories before files
        # Directories have higher path depth and should be renamed first
        rename_pairs_sorted = sorted(rename_pairs, key=lambda pair: pair[0].count('/'), reverse=True)

        for src_rel, tgt_rel, entry_type in rename_pairs_sorted:
            src_path = self.root_dir / src_rel
            tgt_path = self.root_dir / tgt_rel

            if src_rel == tgt_rel:
                if self.logger.config.show_unchanged:
                    self.logger.log(f"Unchanged: {src_rel}", LogLevel.SKIP)
                continue

            if not self._validate_paths(src_path, tgt_path, entry_type):
                conflicts = True
                continue

            if dry_run:
                self.logger.log(f'Will rename: "{src_rel}" → "{tgt_rel}"', LogLevel.ACTION)
            else:
                self._perform_rename(src_path, tgt_path, src_rel, tgt_rel, entry_type)

        self._log_completion(dry_run, conflicts)
        return not conflicts

    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, str, str]]) -> Dict[str, List[Tuple[str, str]]]:
        """
        Maps hash to list of tuples containing (entry_type, relative_path).
        """
        hash_map: Dict[str, List[Tuple[str, str]]] = {}
        for entry_type, file_hash, path in hash_entries:
            hash_map.setdefault(file_hash, []).append((entry_type, path))
        return hash_map

    def _determine_rename_pairs(
        self,
        source_map: Dict[str, List[Tuple[str, str]]],
        target_map: Dict[str, List[Tuple[str, str]]],
    ) -> List[Tuple[str, str, str]]:
        """
        Determines the pairs of source and target paths for renaming.
        Returns a list of tuples: (source_relative_path, target_relative_path, entry_type)
        """
        pairs: List[Tuple[str, str, str]] = []
        processed_targets: Set[str] = set()

        for file_hash, src_entries in source_map.items():
            tgt_entries = target_map.get(file_hash, [])
            for src_entry in src_entries:
                entry_type, src_path = src_entry
                if any(src_path == pair[0] for pair in pairs):
                    continue

                available_tgts = [t for t in tgt_entries if t[1] not in processed_targets]
                if not available_tgts:
                    self.logger.log(f"No matching hash for: {src_path}", LogLevel.WARNING)
                    continue

                best_match = self._select_best_match(src_path, available_tgts, entry_type)
                if best_match:
                    tgt_type, tgt_path = best_match
                    pairs.append((src_path, tgt_path, entry_type))
                    processed_targets.add(tgt_path)

        return pairs

    def _select_best_match(self, source: str, targets: List[Tuple[str, str]], entry_type: str) -> Optional[Tuple[str, str]]:
        """
        Selects the best match from targets based on name similarity.
        Returns a tuple of (target_type, target_path) or None.
        """
        source_clean = self._clean_name(source)
        best_similarity = -1
        best_target = None

        for tgt_entry in targets:
            tgt_type, tgt = tgt_entry
            if entry_type != tgt_type:
                continue  # Match only same types

            tgt_clean = self._clean_name(tgt)
            similarity = self._name_similarity(source_clean, tgt_clean)
            if similarity > best_similarity:
                best_similarity = similarity
                best_target = tgt_entry

        if best_target:
            self.logger.log(
                f"Best match for '{source}' is '{best_target[1]}' with similarity {best_similarity:.2f}",
                LogLevel.DEBUG
            )
        else:
            self.logger.log(f"No suitable match found for '{source}'", LogLevel.WARNING)

        return best_target

    @staticmethod
    def _name_similarity(name1: str, name2: str) -> float:
        matches = sum(a == b for a, b in zip(name1, name2))
        max_len = max(len(name1), len(name2))
        return matches / max_len if max_len else 0

    @staticmethod
    def _clean_name(name: str) -> str:
        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')
        name = name.lower()
        name = pathlib.Path(name).stem
        name = re.sub(r'[^a-z0-9]', '', name)
        return name

    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path, entry_type: str) -> bool:
        if not src.exists():
            self.logger.log(f"Source missing: {src.relative_to(self.root_dir)}", LogLevel.WARNING)
            return False
        if tgt.exists() and tgt != src:
            self.logger.log(f"Target exists: {tgt.relative_to(self.root_dir)}", LogLevel.ERROR)
            return False
        if entry_type == 'DIR' and not tgt.parent.exists():
            self.logger.log(f"Parent directory does not exist for target: {tgt.relative_to(self.root_dir)}", LogLevel.ERROR)
            return False
        return True

    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str, entry_type: str) -> None:
        try:
            tgt.parent.mkdir(parents=True, exist_ok=True)

            # Capture timestamps before moving
            src_stat = src.stat()
            atime = src_stat.st_atime
            mtime = src_stat.st_mtime

            shutil.move(str(src), str(tgt))
            self.logger.log(f'Renamed: "{src_rel}" → "{tgt_rel}"', LogLevel.CHANGE)

            # Preserve timestamps after moving
            os.utime(tgt, (atime, mtime))

            # Preserve creation time on Windows
            if sys.platform.startswith('win'):
                try:
                    import ctypes
                    from ctypes import wintypes

                    # Define necessary Windows structures and functions
                    FILE_WRITE_ATTRIBUTES = 0x100
                    OPEN_EXISTING = 3
                    FILE_FLAG_BACKUP_SEMANTICS = 0x02000000

                    kernel32 = ctypes.WinDLL('kernel32', use_last_error=True)

                    CreateFile = kernel32.CreateFileW
                    CreateFile.argtypes = [
                        wintypes.LPCWSTR,  # lpFileName
                        wintypes.DWORD,    # dwDesiredAccess
                        wintypes.DWORD,    # dwShareMode
                        wintypes.LPVOID,   # lpSecurityAttributes
                        wintypes.DWORD,    # dwCreationDisposition
                        wintypes.DWORD,    # dwFlagsAndAttributes
                        wintypes.HANDLE    # hTemplateFile
                    ]
                    CreateFile.restype = wintypes.HANDLE

                    SetFileTime = kernel32.SetFileTime
                    SetFileTime.argtypes = [
                        wintypes.HANDLE,  # hFile
                        ctypes.POINTER(wintypes.FILETIME),  # lpCreationTime
                        ctypes.POINTER(wintypes.FILETIME),  # lpLastAccessTime
                        ctypes.POINTER(wintypes.FILETIME)   # lpLastWriteTime
                    ]
                    SetFileTime.restype = wintypes.BOOL

                    CloseHandle = kernel32.CloseHandle
                    CloseHandle.argtypes = [wintypes.HANDLE]
                    CloseHandle.restype = wintypes.BOOL

                    # Convert Unix timestamp to Windows FILETIME structure
                    def unix_to_filetime(timestamp: float) -> wintypes.FILETIME:
                        ft = int(timestamp * 10**7) + 116444736000000000
                        return wintypes.FILETIME(ft & 0xFFFFFFFF, ft >> 32)

                    creation_time = unix_to_filetime(src_stat.st_ctime)
                    access_time = unix_to_filetime(src_stat.st_atime)
                    write_time = unix_to_filetime(mtime)

                    handle = CreateFile(
                        str(tgt),
                        FILE_WRITE_ATTRIBUTES,
                        0,
                        None,
                        OPEN_EXISTING,
                        FILE_FLAG_BACKUP_SEMANTICS,
                        None
                    )

                    if handle != wintypes.HANDLE(-1).value:
                        SetFileTime(
                            handle,
                            ctypes.byref(creation_time),
                            ctypes.byref(access_time),
                            ctypes.byref(write_time)
                        )
                        CloseHandle(handle)
                    else:
                        self.logger.log(f"Failed to set creation time for {tgt_rel}", LogLevel.WARNING)

                except Exception as e:
                    self.logger.log(f"Error setting creation time for {tgt_rel}: {e}", LogLevel.WARNING)

        except OSError as error:
            self.logger.log(f"Failed to rename {src_rel}: {error}", LogLevel.ERROR)

    def _preview_renames(self, rename_pairs: List[Tuple[str, str, str]]) -> None:
        if not rename_pairs:
            self.logger.log("No files or directories require renaming", LogLevel.WARNING)
            return

        table = Table(
            title="Pending Rename Operations",
            show_header=True,
            header_style="bold blue",
            box=ROUNDED
        )

        table.add_column("Type", style="cyan", width=6)
        table.add_column("Source", style="white")
        table.add_column("Target", style="green")

        changes = 0
        for src, tgt, entry_type in rename_pairs:
            if src != tgt:
                type_icon = "📁" if entry_type == 'DIR' else "📄"
                table.add_row(type_icon, src, tgt)
                changes += 1

        if changes:
            self.logger.console.print("\n")
            self.logger.console.print(Panel(table, border_style="blue"))
            self.logger.console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")
        else:
            self.logger.log("No files or directories require renaming", LogLevel.WARNING)

    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:
        operation = "Dry run" if dry_run else "File and directory renaming"
        if has_conflicts:
            self.logger.log(f"{operation}: Conflicts detected, some items skipped", LogLevel.WARNING)
        else:
            self.logger.log(f"{operation} completed successfully", LogLevel.SUMMARY)


class FileEditor:
    """Opens files using the default system editor."""

    @staticmethod
    def open(file_path: pathlib.Path, logger: Logger) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', str(file_path)])
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(['xdg-open', str(file_path)])
            else:
                logger.log(f"Unsupported platform: {sys.platform}", LogLevel.WARNING)
        except Exception as error:
            logger.log(f"Failed to open editor: {error}", LogLevel.ERROR)


def parse_arguments() -> argparse.Namespace:
    """Parses command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Batch Rename Utility with SHA256 Verification",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "directory",
        type=str,
        nargs='?',
        help="Target directory for processing"
    )
    parser.add_argument(
        "--include-subdirectories",
        action="store_true",
        default=True,
        help="Include subdirectories"
    )
    parser.add_argument(
        "-v", "--verbosity",
        choices=["quiet", "normal", "verbose", "debug"],
        default="normal",
        help="Set output verbosity level"
    )
    parser.add_argument(
        "--show-skipped",
        action="store_true",
        help="Show skipped items in output"
    )
    parser.add_argument(
        "--show-unchanged",
        action="store_true",
        help="Show unchanged items in output"
    )
    parser.add_argument(
        "--prompt",
        action="store_true",
        help="Enable interactive prompting for arguments"
    )

    return parser.parse_args()


def prompt_for_arguments(args: argparse.Namespace) -> argparse.Namespace:
    """Handles interactive prompting for command arguments."""
    console = Console()

    if args.prompt:
        args.directory = Prompt.ask("Enter directory path", default=args.directory or "").strip()
        args.include_subdirectories = Confirm.ask("Include subdirectories?", default=args.include_subdirectories)

    if not args.directory:
        console.print("[red]Error:[/] the following arguments are required: directory")
        sys.exit(1)

    root_dir = pathlib.Path(args.directory)
    if not root_dir.exists() or not root_dir.is_dir():
        console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")
        sys.exit(1)

    return args


def configure_logging(args: argparse.Namespace) -> LogConfig:
    """Configures logging based on command-line arguments."""
    verbosity_map = {
        "quiet": VerbosityLevel.QUIET,
        "normal": VerbosityLevel.NORMAL,
        "verbose": VerbosityLevel.VERBOSE,
        "debug": VerbosityLevel.DEBUG
    }

    return LogConfig(
        verbosity=verbosity_map[args.verbosity],
        show_skipped=args.show_skipped,
        show_unchanged=args.show_unchanged
    )


def handle_process_command(args: argparse.Namespace, logger: Logger) -> None:
    """Handles the main processing and renaming of files and directories."""
    root_dir = pathlib.Path(args.directory).resolve()

    org_file = root_dir / ".original_hashes.py"
    new_file = root_dir / ".new_hashes.py"

    processor = FileProcessor(root_dir, args.include_subdirectories, logger)
    initial_hashes = processor.collect_file_and_dir_hashes()

    for file_path in (org_file, new_file):
        manager = HashFileManager(file_path, logger)
        manager.write(initial_hashes)

    logger.log("Opening new hash file for editing...", LogLevel.ACTION)
    FileEditor.open(new_file, logger)

    if not Confirm.ask("\nProceed with renaming? [y/n]: "):
        logger.log("Operation cancelled by user", LogLevel.WARNING)
        return

    org_manager = HashFileManager(org_file, logger)
    new_manager = HashFileManager(new_file, logger)

    renamer = FileRenamer(root_dir, logger)
    if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):
        if Confirm.ask("\nApply these changes? [y/n]: "):
            renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)

            for file_path in (org_file, new_file):
                try:
                    file_path.unlink()
                    logger.log(f"Cleaned up: {file_path}", LogLevel.ACTION)
                except OSError as error:
                    logger.log(f"Cleanup failed: {error}", LogLevel.WARNING)

    logger.print_summary()


def main() -> None:
    """Main entry point of the utility."""
    args = parse_arguments()
    args = prompt_for_arguments(args)
    logger = Logger(configure_logging(args))

    handle_process_command(args, logger)


if __name__ == "__main__":
    main()
