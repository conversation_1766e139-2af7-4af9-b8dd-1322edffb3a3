# 'ChatGPT o1-mini'
# 'https://chatgpt.com/c/6742dda3-d600-8008-bc70-b303a82d1588'

import argparse
import hashlib
import os
import pathlib
import subprocess
import sys
from typing import List, Tuple

from rich import print
from rich.filesize import decimal
from rich.markup import escape
from rich.prompt import Confirm
from rich.text import Text
from rich.tree import Tree


def compute_sha256(file_path: str) -> str:
    """Compute the SHA256 hash of a file."""
    sha256 = hashlib.sha256()
    try:
        with open(file_path, 'rb') as file:
            for chunk in iter(lambda: file.read(4096), b''):
                sha256.update(chunk)
    except IOError as error:
        print(f"[red]Error reading {file_path}: {error}[/red]")
        return ""
    return sha256.hexdigest()


def gather_file_hashes(directory: str, include_subdirs: bool = True) -> List[Tuple[str, str]]:
    """Gather SHA256 hashes and relative file paths."""
    hashes = []
    for root, _, files in os.walk(directory):
        for filename in files:
            full_path = os.path.join(root, filename)
            if os.path.isfile(full_path) and os.access(full_path, os.R_OK):
                relative_path = os.path.relpath(full_path, directory)
                file_hash = compute_sha256(full_path)
                if file_hash:
                    hashes.append((file_hash, relative_path))
                    print(f"Processed: {relative_path}")
        if not include_subdirs:
            break
    return hashes


def write_hashes(hashes: List[Tuple[str, str]], output_file: str) -> None:
    """Write hashes and filenames to a text file."""
    try:
        with open(output_file, "w", encoding='utf-8') as file:
            for file_hash, filename in hashes:
                file.write(f"{file_hash}|{filename}\n")
        print(f"[green]Hashes written to {output_file}[/green]")
    except IOError as error:
        print(f"[red]Failed to write to {output_file}: {error}[/red]")


def read_hashes(file_path: str) -> List[Tuple[str, str]]:
    """Read hashes and filenames from a text file."""
    hashes = []
    try:
        with open(file_path, "r", encoding='utf-8') as file:
            for line in file:
                parts = line.strip().split('|')
                if len(parts) == 2:
                    hashes.append((parts[0], parts[1]))
    except IOError as error:
        print(f"[red]Failed to read {file_path}: {error}[/red]")
    return hashes


def open_in_editor(file_path: str) -> None:
    """Open a file in the default text editor."""
    try:
        if sys.platform.startswith('darwin'):
            subprocess.call(('open', file_path))
        elif os.name == 'nt':
            os.startfile(file_path)
        elif os.name == 'posix':
            subprocess.call(('xdg-open', file_path))
        else:
            print(f"[yellow]Unsupported OS for opening files: {sys.platform}[/yellow]")
    except Exception as error:
        print(f"[red]Failed to open {file_path}: {error}[/red]")


def execute_rename(directory: str, org_file: str, new_file: str) -> None:
    """Rename files based on hash matching between original and new hash files."""
    original_hashes = read_hashes(org_file)
    new_hashes = read_hashes(new_file)

    if len(original_hashes) != len(new_hashes):
        print("[yellow]Warning: Mismatch in number of entries between original and new hash files.[/yellow]")

    hash_to_new = {hash_val: new_name for hash_val, new_name in new_hashes}

    for hash_val, original_name in original_hashes:
        new_name = hash_to_new.get(hash_val)
        if new_name:
            original_path = os.path.join(directory, original_name)
            new_path = os.path.join(directory, new_name)
            if os.path.exists(original_path):
                if not os.path.exists(new_path):
                    try:
                        os.rename(original_path, new_path)
                        print(f"[green]Renamed: {original_name} -> {new_name}[/green]")
                    except OSError as error:
                        print(f"[red]Failed to rename {original_name} to {new_name}: {error}[/red]")
                else:
                    print(f"[yellow]Conflict: {new_name} already exists. Skipping {original_name}.[/yellow]")
        else:
            print(f"[red]No new filename found for hash: {hash_val} (File: {original_name})[/red]")


def create_visual_text(path: pathlib.Path) -> Text:
    """Create formatted text for directory visualization."""
    filename = path.name
    text = Text(filename, "green").highlight_regex(r"\..*$", "bold red")
    text.stylize(f"link file://{path}")
    try:
        size = path.stat().st_size
    except OSError:
        size = 0
    text.append(f" ({decimal(size)})", "blue")
    icon = "🐍 " if path.suffix == ".py" else "📄 "
    return Text(icon) + text


def build_directory_tree(directory: pathlib.Path, tree: Tree) -> None:
    """Recursively build a tree structure of the directory."""
    try:
        entries = sorted(
            directory.iterdir(),
            key=lambda p: (p.is_file(), p.name.lower()),
        )
    except PermissionError as error:
        print(f"[red]Permission denied: {error}[/red]")
        return

    for entry in entries:
        if entry.name.startswith("."):
            continue
        if entry.is_dir():
            branch = tree.add(f"[bold magenta]:open_file_folder: [link file://{entry}]{escape(entry.name)}")
            build_directory_tree(entry, branch)
        else:
            tree.add(create_visual_text(entry))


def visualize_directory(directory: str) -> None:
    """Display the directory tree using Rich."""
    path = pathlib.Path(directory)
    if not path.exists():
        print(f"[red]Error: Directory '{directory}' does not exist.[/red]")
        return

    tree = Tree(f":open_file_folder: [link file://{path}]{path}", guide_style="bold bright_blue")
    build_directory_tree(path, tree)
    print(tree)


def parse_args() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="RenameWithEditor: Batch File Renaming Utility")
    subparsers = parser.add_subparsers(dest="command", required=True, help="Available commands")

    # Process Command
    process_parser = subparsers.add_parser("process", help="Generate hash files and rename files")
    process_parser.add_argument("directory", type=str, help="Target directory for processing")
    process_parser.add_argument("--include-subdirectories", action="store_true", help="Include subdirectories in processing")
    process_parser.add_argument("--org-output", type=str, default="FilesToText__filenames_ORG.txt", help="Output file for original filenames and hashes")
    process_parser.add_argument("--new-output", type=str, default="FilesToText__filenames_NEW.txt", help="Output file for new filenames and hashes")

    # Visualize Command
    visualize_parser = subparsers.add_parser("visualize", help="Display directory tree")
    visualize_parser.add_argument("directory", type=str, help="Directory to visualize")

    return parser.parse_args()


def main() -> None:
    """Main function to execute based on user command."""
    args = parse_args()

    if args.command == "process":
        target_dir = os.path.abspath(args.directory)
        org_file = os.path.abspath(args.org_output)
        new_file = os.path.abspath(args.new_output)

        if not pathlib.Path(target_dir).is_dir():
            print(f"[red]Error: '{target_dir}' is not a valid directory.[/red]")
            sys.exit(1)

        # Operation A: Generate hash files
        print(f"[cyan]Generating hash files for '{target_dir}'...[/cyan]")
        hashes = gather_file_hashes(target_dir, include_subdirs=args.include_subdirectories)
        write_hashes(hashes, org_file)
        write_hashes(hashes, new_file)
        print("[cyan]Hash files generated successfully.[/cyan]")

        # Open the NEW hash file in the default editor
        print(f"[cyan]Opening '{new_file}' for editing...[/cyan]")
        open_in_editor(new_file)

        # Wait for user confirmation
        if Confirm.ask("[yellow]Have you updated the new filenames in the hash file? Proceed with renaming.[/yellow]"):
            # Operation B: Rename files
            print(f"[cyan]Renaming files based on '{new_file}'...[/cyan]")
            execute_rename(target_dir, org_file, new_file)
            print("[green]File renaming completed successfully.[/green]")
        else:
            print("[red]Operation aborted by user.[/red]")

    elif args.command == "visualize":
        target_dir = os.path.abspath(args.directory)
        visualize_directory(target_dir)


if __name__ == "__main__":
    main()
