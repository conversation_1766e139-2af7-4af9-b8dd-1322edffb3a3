
Here's the current code for  `RenameWithEditor.py`:

```python
""" Utility for batch renaming files through an intermediary text editor (Class-based structure, Loguru-based logging). """

import argparse
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
import re
import unicodedata
import time
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

from dataclasses import dataclass
from enum import Enum

# Rich imports for console interactions
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.box import ROUNDED

# Loguru for logging
from loguru import logger


# =======================================================
# Configuration
# =======================================================
class Config:
    """
    Holds default settings for the Batch Rename Utility.
    """
    DEFAULT_CLEANUP_LOGS = True
    USE_DEFAULT_SETTINGS = True
    DEFAULT_INCLUDE_SUBDIRECTORIES = False
    DEFAULT_INCLUDE_TIMESTAMP = True


# =======================================================
# Logger Setup
# =======================================================
class LoggerSetup:
    """Sets up Loguru logger to write YAML logs to a file."""

    @staticmethod
    def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
        """
        Configure Loguru to log messages in YAML format to `log_file`.
        """
        def yaml_sink(message):
            record = message.record

            time_str = record['time'].strftime('%Y-%m-%d %H:%M:%S')
            level_str = f"!{record['level'].name}"
            name_str = record['name']
            func_name_str = f"*{record['function']}"
            line_no = record["line"]
            msg = record["message"]

            # For multi-line messages, use a block scalar in YAML
            if "\n" in msg:
                lines = msg.split("\n")
                message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
            else:
                # Quote if it has special characters like ':'
                if ":" in msg:
                    message_str = f"'{msg}'"
                else:
                    message_str = msg

            yaml_lines = [
                f"- time: {time_str}",
                f"  level: {level_str}",
                f"  name: {name_str}",
                f"  funcName: {func_name_str}",
                f"  lineno: {line_no}",
                f"  message: {message_str}",
                ""
            ]

            with open(log_file, "a", encoding="utf-8") as f:
                f.write("\n".join(yaml_lines) + "\n")

        # Remove default handlers and add our YAML sink
        logger.remove()
        logger.add(yaml_sink, level=level, enqueue=True)

    @staticmethod
    def initialize_logging(verbosity: str = "INFO"):
        """
        Initialize YAML logging with a level mapped from the string `verbosity`.
        Maps:
          quiet -> ERROR
          normal -> INFO
          verbose -> DEBUG
          debug -> DEBUG
        """
        level_map = {
            "quiet": "ERROR",
            "normal": "INFO",
            "verbose": "DEBUG",
            "debug": "DEBUG"
        }
        selected_level = level_map.get(verbosity.lower(), "INFO")
        LoggerSetup.setup_yaml_logging(level=selected_level)


# =======================================================
# Argument Handler
# =======================================================
class ArgumentHandler:
    """Handles CLI argument parsing and optional prompting."""

    def __init__(self):
        self.parser = self.parse_arguments()

    @staticmethod
    def parse_arguments():
        logger.debug("Setting up argument parser.")
        parser = argparse.ArgumentParser(
            description="Batch Rename Utility with SHA256 Verification (using Loguru + Rich)."
        )

        parser.add_argument('-d', '--directory', type=str, help="Target directory for processing")

        parser.add_argument('--include_subdirs', action='store_true',
                            default=Config.DEFAULT_INCLUDE_SUBDIRECTORIES,
                            help="Include subdirectories in file processing")

        parser.add_argument('--prompt', action='store_true', help="Prompt for missing arguments")

        parser.add_argument('-v', '--verbosity',
                            choices=["quiet", "normal", "verbose", "debug"],
                            default="normal",
                            help="Set output verbosity level")

        parser.add_argument('--include-time', action='store_true',
                            default=Config.DEFAULT_INCLUDE_TIMESTAMP,
                            help="Include 'HH:MM' in the date column (e.g. YYYY.MM.DD HH:MM).")

        # Log Cleanup arguments
        cleanup_logs_group = parser.add_mutually_exclusive_group()
        cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true',
                                        help="Clean up log files after successful execution")
        cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false',
                                        help="Do not clean up log files after successful execution")
        parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)

        logger.debug("Argument parser setup complete.")
        return parser

    def get_arguments(self):
        return self.parser.parse_args()

    def prompt_for_missing_arguments(self, args):
        logger.debug("Prompting for missing arguments.")
        console = Console()

        def print_section(title):
            console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)

        if args.prompt:
            print_section("Default Settings")
            use_defaults = Confirm.ask("Use default settings?", default=Config.USE_DEFAULT_SETTINGS)
            logger.debug(f"Use defaults: {use_defaults}")

            if not use_defaults:
                # Prompt for input directory
                print_section("Directory")
                current_dir = args.directory or ""
                args.directory = Prompt.ask("Target directory path?", default=current_dir).strip()

                # Prompt for including subdirectories
                print_section("Include Subdirectories?")
                args.include_subdirs = Confirm.ask("Include subdirectories?", default=True)

                print_section("Logging Verbosity")
                choices = ["quiet", "normal", "verbose", "debug"]
                console.print("Verbosity levels:\n  quiet\n  normal\n  verbose\n  debug\n")
                chosen_verbosity = Prompt.ask("Choose verbosity", default=args.verbosity, choices=choices)
                args.verbosity = chosen_verbosity

                # Prompt for including timestamps
                print_section("Include Time?")
                args.include_time = Confirm.ask("Include HH:MM in the date column?", default=True)

                # Prompt for cleanup logs
                print_section("Log Cleanup")
                args.cleanup_logs = Confirm.ask("Clean up log file after successful execution?", default=args.cleanup_logs)
            else:
                # Assign defaults if none provided
                args.directory = args.directory or ""
                args.include_subdirs = args.include_subdirs if args.include_subdirs is not None else Config.DEFAULT_INCLUDE_SUBDIRECTORIES
                # Let the existing defaults stand for verbosity and cleanup_logs

        # Validation
        if not args.directory:
            console.print("[red]Error:[/] The following argument is required: directory")
            sys.exit(1)

        dir_path = Path(args.directory)
        if not dir_path.exists() or not dir_path.is_dir():
            console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")
            sys.exit(1)

        logger.debug("Argument prompting complete.")
        return args


# =======================================================
# File Hasher
# =======================================================
class FileHasher:
    """Computes SHA256 hashes for files."""
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path) -> Optional[str]:
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.error(f"Error reading `{file_path}`: {error}")
            return None


# =======================================================
#  File Processor
# =======================================================
class FileProcessor:
    """Processes files to collect their hashes plus date/time (optionally)."""

    def __init__(self, root_dir: pathlib.Path, include_subdirs: bool, include_time: bool = False):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs
        self.include_time = include_time

    def collect_file_hashes(self) -> List[Tuple[str, str, str]]:
        """
        Collect a list of (file_hash, relative_filename, date_str).
        If self.include_time is True, date_str = 'YYYY.MM.DD HH:MM'
        Otherwise, date_str = 'YYYY.MM.DD'.
        """
        hash_entries = []
        for root, _, files in os.walk(self.root_dir):
            for filename in files:
                file_path = pathlib.Path(root) / filename
                if not self._is_accessible_file(file_path):
                    continue

                relative_path = file_path.relative_to(self.root_dir).as_posix()
                file_hash = FileHasher.compute_sha256(file_path)
                if file_hash:
                    # Generate the date/time string
                    mtime = file_path.stat().st_mtime
                    if self.include_time:
                        date_str = time.strftime("%Y.%m.%d %H:%M", time.localtime(mtime))
                    else:
                        date_str = time.strftime("%Y.%m.%d", time.localtime(mtime))

                    hash_entries.append((file_hash, relative_path, date_str))
                    logger.debug(f"Processed: {relative_path}")

            if not self.include_subdirs:
                break
        return hash_entries

    def _is_accessible_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            logger.warning(f"`{path}` is not accessible or not a file")
            return False
        return True


# =======================================================
# Hash File Manager
# =======================================================
class HashFileManager:
    """
    Manages reading and writing hash files (with optional time).
    We accept an additional param 'include_time' to adjust the header.
    """

    def __init__(self, file_path: pathlib.Path, include_time: bool = False):
        self.file_path = file_path
        self.include_time = include_time

    def write(self, hash_entries: List[Tuple[str, str, str]]) -> None:
        """
        Writes lines in one of two formats for the header:
            #  YYYY.MM.DD  | FILENAME                                | FILEHASH
            ''' 2025.03.07 | ''' - "filename1.txt"                 # | '991852c2ca80b708ce3bda2c477a158abc1351769957086e400f805f5ec095fe'
            ''' 2025.03.07 | ''' - "filename2.txt"                 # | 'a12b3c8260a25916262371ff4e6646306bb9716f032c3545b6faa4ed82864a41'
            ''' 2025.03.07 | ''' - "filename3.txt"                 # | '0405f63ec7d058f0865a2d5ebc1da5a1ed549925c627a25109bd252f9b52f537'

            or:

            #  YYYY.MM.DD  HH:MM | FILENAME                          | FILEHASH
            ''' 2025.03.07 21:13 | ''' - "filename1.txt"           # | '991852c2ca80b708ce3bda2c477a158abc1351769957086e400f805f5ec095fe'
            ''' 2025.03.07 21:17 | ''' - "filename2.txt"           # | 'a12b3c8260a25916262371ff4e6646306bb9716f032c3545b6faa4ed82864a41'
            ''' 2025.03.07 21:21 | ''' - "filename3.txt"           # | '0405f63ec7d058f0865a2d5ebc1da5a1ed549925c627a25109bd252f9b52f537'
        """
        try:
            with self.file_path.open("w", encoding='utf-8') as f:
                # Adjust the header line depending on whether time is included
                if self.include_time:
                    f.write("#  YYYY.MM.DD  HH:MM | FILENAME                            | FILEHASH\n")
                else:
                    f.write("#  YYYY.MM.DD  | FILENAME                                  | FILEHASH\n")

                # Sort by filename (case-insensitive)
                sorted_entries = sorted(hash_entries, key=lambda x: x[1].lower())

                # Determine padding for the filename column
                max_length = 2 + max((len(filename) for _, filename, _ in sorted_entries), default=0)

                for file_hash, filename, date_str in sorted_entries:
                    # Insert double-quotes around filename, single-quotes around the hash
                    padded_filename = f"\"{filename}\"".ljust(max_length)
                    line = f"''' {date_str} | ''' - {padded_filename} # | '{file_hash}'\n"
                    f.write(line)

            logger.info(f"Hash file written: {self.file_path.name}")
        except IOError as error:
            logger.error(f"Failed to write hash file: {error}")

    def read(self) -> List[Tuple[str, str, str]]:
        """
        Reads lines in the updated format or the old one, returning (hash, filename, date_str).
        """
        hash_entries = []
        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line in f:
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            logger.error(f"Failed to read hash file: {error}")
        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str, str]]:
        """
        Attempts to parse lines of the form:
           ''' 2025.03.07 | ''' - "filename"   # | 'hash'
        or
           ''' 2025.03.07 21:17 | ''' - "filename"   # | 'hash'
        or the old format:
           - 'filename' # | "hash"
        """
        line = line.strip()
        if not line or line.startswith("#"):
            return None

        # Variation that handles optional HH:MM after the date
        #  e.g. ''' 2025.03.07 21:17 | ''' - "filename"   # | 'abcdef1234...'
        new_format = r"^'''\s*(\d{4}\.\d{2}\.\d{2}(?:\s+\d{2}:\d{2})?)\s*\|\s*'''\s*-\s*\"(.+?)\"\s*#\s*\|\s*'([a-fA-F0-9]{64})'$"
        match_new = re.match(new_format, line)
        if match_new:
            date_str = match_new.group(1)   # can be "YYYY.MM.DD" or "YYYY.MM.DD HH:MM"
            filename = match_new.group(2)
            file_hash = match_new.group(3)
            return (file_hash, filename, date_str)

        # Old format fallback: - 'filename' # | "hash"
        old_format = r'^-\s*\'(.+?)\'\s*#\s*\|\s*"([a-fA-F0-9]{64})"$'
        match_old = re.match(old_format, line)
        if match_old:
            filename = match_old.group(1)
            file_hash = match_old.group(2)
            return (file_hash, filename, "")  # no date info

        logger.warning(f"Invalid hash file entry: {line}")
        return None


# =======================================================
# File Renamer
# =======================================================
class FileRenamer:
    """Handles the renaming of files based on hash comparisons."""

    def __init__(self, root_dir: pathlib.Path):
        self.root_dir = root_dir

    def execute(
        self,
        source_hashes: List[Tuple[str, str, str]],
        target_hashes: List[Tuple[str, str, str]],
        dry_run: bool = True
    ) -> bool:
        """
        We only need the (hash, filename) pair for matching and renaming.
        """
        src_map_entries = [(h, f) for (h, f, _) in source_hashes]
        tgt_map_entries = [(h, f) for (h, f, _) in target_hashes]

        source_map = self._map_hash_to_paths(src_map_entries)
        target_map = self._map_hash_to_paths(tgt_map_entries)

        rename_pairs = self._determine_rename_pairs(source_map, target_map)
        conflicts = False

        if dry_run:
            self._preview_renames(rename_pairs)

        for src_rel, tgt_rel in rename_pairs:
            src_path = self.root_dir / src_rel
            tgt_path = self.root_dir / tgt_rel

            if src_rel == tgt_rel:
                logger.debug(f"Unchanged: {src_rel}")
                continue

            if not self._validate_paths(src_path, tgt_path):
                conflicts = True
                continue

            if dry_run:
                logger.info(f'Will rename: "{src_rel}" → "{tgt_rel}"')
            else:
                self._perform_rename(src_path, tgt_path, src_rel, tgt_rel)

        self._log_completion(dry_run, conflicts)
        return not conflicts

    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        hash_map: Dict[str, List[str]] = {}
        for file_hash, path in hash_entries:
            hash_map.setdefault(file_hash, []).append(path)
        return hash_map

    def _determine_rename_pairs(
        self,
        source_map: Dict[str, List[str]],
        target_map: Dict[str, List[str]],
    ) -> List[Tuple[str, str]]:
        pairs: List[Tuple[str, str]] = []
        processed_targets: Set[str] = set()

        for file_hash, src_paths in source_map.items():
            tgt_paths = target_map.get(file_hash, [])
            for src in src_paths:
                if any(src == pair[0] for pair in pairs):
                    # Already handled
                    continue

                available_tgts = [t for t in tgt_paths if t not in processed_targets]
                if not available_tgts:
                    logger.warning(f"No matching hash for: {src}")
                    continue

                best_match = self._select_best_match(src, available_tgts)
                if best_match:
                    pairs.append((src, best_match))
                    processed_targets.add(best_match)

        return pairs

    def _select_best_match(self, source: str, targets: List[str]) -> Optional[str]:
        source_clean = self._clean_name(source)
        best_similarity = -1.0
        best_target = None

        for tgt in targets:
            tgt_clean = self._clean_name(tgt)
            similarity = self._name_similarity(source_clean, tgt_clean)
            if similarity > best_similarity:
                best_similarity = similarity
                best_target = tgt

        if best_target:
            logger.debug(
                f"Best match for '{source}' is '{best_target}' with similarity {best_similarity:.2f}"
            )
        else:
            logger.warning(f"No suitable match found for '{source}'")
        return best_target

    @staticmethod
    def _name_similarity(name1: str, name2: str) -> float:
        matches = sum(a == b for a, b in zip(name1, name2))
        max_len = max(len(name1), len(name2))
        return matches / max_len if max_len else 0

    @staticmethod
    def _clean_name(name: str) -> str:
        # Normalize to ASCII, remove special chars
        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')
        name = name.lower()
        name = pathlib.Path(name).stem
        name = re.sub(r'[^a-z0-9]', '', name)
        return name

    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:
        if not src.exists():
            logger.warning(f"Source missing: {src.relative_to(self.root_dir)}")
            return False
        if tgt.exists() and tgt != src:
            logger.error(f"Target exists: {tgt.relative_to(self.root_dir)}")
            return False
        return True

    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str) -> None:
        try:
            tgt.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src), str(tgt))
            logger.info(f'Renamed: "{src_rel}" → "{tgt_rel}"')
        except OSError as error:
            logger.error(f"Failed to rename {src_rel}: {error}")

    def _preview_renames(self, rename_pairs: List[Tuple[str, str]]) -> None:
        if not rename_pairs:
            logger.warning("No files require renaming")
            return

        table = Table(
            title="Pending Rename Operations",
            show_header=True,
            header_style="bold blue",
            box=ROUNDED
        )
        table.add_column("Operation", style="cyan", width=4)
        table.add_column("Source", style="white")
        table.add_column("Target", style="green")

        changes = 0
        for src, tgt in rename_pairs:
            if src != tgt:
                table.add_row("→", src, tgt)
                changes += 1

        if changes:
            console = Console()
            console.print("\n")
            console.print(Panel(table, border_style="blue"))
            console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")
        else:
            logger.warning("No files require renaming")

    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:
        operation = "Dry run" if dry_run else "File renaming"
        if has_conflicts:
            logger.warning(f"{operation}: Conflicts detected, some files skipped")
        else:
            logger.info(f"{operation} completed successfully")


# =======================================================
# File Editor
# =======================================================
class FileEditor:
    """Opens files using the default system editor."""

    @staticmethod
    def open(file_path: pathlib.Path) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', str(file_path)])
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(['xdg-open', str(file_path)])
            else:
                logger.warning(f"Unsupported platform: {sys.platform}")
        except Exception as error:
            logger.error(f"Failed to open editor: {error}")


# =======================================================
# Main App Class
# =======================================================
class BatchRenameApp:
    """
    Encapsulates the CLI workflow for batch renaming:
      1) Parse & prompt for Arguments
      2) Initialize Logging
      3) Collect & write .original_hashes.py & .new_hashes.py
      4) Edit & confirm renaming
      5) Cleanup
    """

    def __init__(self):
        self.arg_handler = ArgumentHandler()
        self.args: Optional[argparse.Namespace] = None

    def run(self) -> None:
        # 1) Parse & prompt
        self.args = self.arg_handler.get_arguments()
        self.args = self.arg_handler.prompt_for_missing_arguments(self.args)

        # 2) Initialize Logging
        LoggerSetup.initialize_logging(self.args.verbosity)

        # 3) Handle the main process
        success = False
        try:
            self.handle_process_command()
            success = True
        except Exception as e:
            logger.error(f"Execution failed: {e}")

        # 4) Cleanup logs if needed and success
        if success and self.args.cleanup_logs:
            logger.remove()  # remove Loguru handler to release file
            log_file = Path("app.log.yml")
            if log_file.exists():
                console = Console()
                try:
                    log_file.unlink()
                    console.print(f"[bold green]Log file {log_file} has been cleaned up.[/bold green]\n")
                except Exception as e:
                    console.print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")

    def handle_process_command(self) -> None:
        console = Console()
        root_dir = pathlib.Path(self.args.directory).resolve()

        org_file = root_dir / ".original_hashes.py"
        new_file = root_dir / ".new_hashes.py"

        try:
            # Pass include_time to the FileProcessor
            processor = FileProcessor(root_dir, self.args.include_subdirs, self.args.include_time)
            initial_hashes = processor.collect_file_hashes()

            # Pass include_time to HashFileManager so it writes the correct header
            for file_path in (org_file, new_file):
                manager = HashFileManager(file_path, include_time=self.args.include_time)
                manager.write(initial_hashes)

            logger.info("Opening new hash file for editing...")
            FileEditor.open(new_file)

            if not Confirm.ask("\nProceed with renaming? [y/n]: ", default="y"):
                logger.warning("Operation cancelled by user")
                return

            org_manager = HashFileManager(org_file)
            new_manager = HashFileManager(new_file)

            renamer = FileRenamer(root_dir)
            # Dry run first
            if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):
                if Confirm.ask("\nApply these changes? [y/n]: ", default="y"):
                    renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)

        finally:
            # Cleanup: Attempt to delete the hash files
            for file_path in (org_file, new_file):
                try:
                    if file_path.exists():
                        file_path.unlink()
                        logger.info(f"Cleaned up: {file_path.name}")
                except OSError as error:
                    logger.warning(f"Cleanup failed for {file_path.name}: {error}")


# =======================================================
# Execution entrypoint
# =======================================================
def main() -> None:
    """Main entry point of the utility."""
    app = BatchRenameApp()
    app.run()


if __name__ == "__main__":
    main()
```

---


Here's the `.new_hashes` generated with `RenameWithEditor` @main.py :
```
#  YYYY.MM.DD  HH:MM | DEPTH | FILENAME                        | FILEHASH
''' 2025.03.20 17:33 | 1 | ''' - "a001-prompt_through_system_instructions.py"        # | 'cff95ea9b115ed1045431dc435a302038904fea1bab7f6852efb73f3a6a32184'
''' 2025.03.02 22:24 | 1 | ''' - "a002-interactive_system_instruction_chain.bat"     # | '1ba5c24140a314ab0b974e37b0d60791c467394bead42e8e5fb2fa840f27024b'
''' 2025.03.20 18:22 | 1 | ''' - "a002-interactive_system_instruction_chain.log.yml" # | '50f1ed918d047521ce210c4b9a81fb301694156c87d3f26ba2f080280322289b'
''' 2025.03.20 17:39 | 1 | ''' - "a002-interactive_system_instruction_chain.py"      # | '13ea64fe8b61fd86faf99352c7e8792eec3c06bec82fed048ddd462e460a0a0b'
''' 2025.03.20 18:02 | 1 | ''' - "a003-interactive_system_instructions.py"           # | '3d5a7a7ebdd824cc80e21af6c272ba83af7a667cfe81aa030dbfcf9a85ad8f1d'
''' 2025.03.28 11:16 | 1 | ''' - "a004-interactive_system_instruction_chain.log.yml" # | '5f22d6f45ec3897538d9e611b11f15e4adbb187922291a04f6ce181744a5e887'
''' 2025.03.28 11:32 | 1 | ''' - "a005-interactive_system_instruction_chain.log.yml" # | '942b2df6fefe52275cf300461bcc9c82617a672946beb2a664b34d140a92e219'
''' 2025.03.28 11:31 | 1 | ''' - "a005-interactive_system_instruction_chain.py"      # | '7b0e05c45b86a4eee6851a06fe2b0d4fabbfd7e21526c922e032aaf7c9ced21e'
''' 2025.03.28 11:40 | 1 | ''' - "a006-interactive_system_instruction_chain.log.yml" # | 'a9f4cf12f88453110b157438cf0e31018be9775ba4d25e0432bc2b0a2be4daaa'
''' 2025.03.28 11:39 | 1 | ''' - "a006-interactive_system_instruction_chain.py"      # | '1244321d4bbd0997f9336fa7076e4fbc7cbab9b089d8165dc68d0beaac60f1f6'
''' 2025.03.28 11:59 | 1 | ''' - "a007-interactive_system_instruction_chain.log.yml" # | 'ce1afe5f4aedaf08deb02643ddfc5e621b25d2cab063fc367ca4fe0cff1a142a'
''' 2025.03.28 11:59 | 1 | ''' - "a007-interactive_system_instruction_chain.py"      # | '894a8e6e34feb0697ecfe448b6acf094d9dc261ac6297dd54f747cf45d0be0f2'
''' 2025.03.28 12:35 | 1 | ''' - "a008-interactive_system_instruction_chain.log.yml" # | 'e957c02625551910421e7edc0758dcedd03e83e1bc6493d726e9f3b31de18aef'
''' 2025.03.28 12:34 | 1 | ''' - "a008-interactive_system_instruction_chain.py"      # | 'bc28e5931b4a54a5d4c67aa5c75e5dfb01539fb5bd20e1ed2ea241fa4894d524'
''' 2025.03.28 13:27 | 1 | ''' - "a009-interactive_system_instruction_chain.log.yml" # | '72d3e351559dfdfa8d1995a4830766e038d04460980c7833975eb562a4746e01'
''' 2025.03.28 13:26 | 1 | ''' - "a009-interactive_system_instruction_chain.py"      # | 'd4144af2de9bb7852da9f1f6e94c4076c6a6b1ed8bf0dd419954a3c43d0eb0f9'
''' 2025.03.28 13:23 | 1 | ''' - "a010-interactive_system_instruction_chain.log.yml" # | '64395122b7e86b13f9cef4ec9c3510683c951116a9551b60f5bf2db83dbe37dc'
''' 2025.03.28 13:22 | 1 | ''' - "a010-interactive_system_instruction_chain.py"      # | 'f0b2c15b2f11e661ec05d77019b508f185ddddf4219e5bb2df932c2194c9f11e'
''' 2025.03.28 13:36 | 1 | ''' - "a011-interactive_system_instruction_chain.log.yml" # | 'fd2f568281da2fc700363f869804c2e249e1010f4032003286c22de6c68c857b'
''' 2025.03.28 13:35 | 1 | ''' - "a011-interactive_system_instruction_chain.py"      # | '0fa153bfa84fa48778dbe7b34365f4db59522537dc2b05231a85adb91776288d'
''' 2025.03.28 14:01 | 1 | ''' - "a012-interactive_system_instruction_chain.log.yml" # | 'b3eff689b96d98ca55fd1e00758f82f54a1b10b580a2a0f2e2fba51cd8315ff5'
''' 2025.03.28 14:00 | 1 | ''' - "a012-interactive_system_instruction_chain.py"      # | 'b8e0043fa395b96af47c9d7e9b308cdec813ebbc546d24f21058c4d0f5509667'
''' 2025.03.28 14:03 | 1 | ''' - "a013-interactive_system_instruction_chain.log.yml" # | '408fed50112505d7c4cd2088b18419de3fe213d8f64ebf553aa90cf9d628ad05'
''' 2025.03.28 13:26 | 1 | ''' - "a013-interactive_system_instruction_chain.py"      # | 'd4144af2de9bb7852da9f1f6e94c4076c6a6b1ed8bf0dd419954a3c43d0eb0f9'
''' 2025.03.28 14:11 | 1 | ''' - "a014-interactive_system_instruction_chain.log.yml" # | 'fc84412517e052981e0a44970bcc40cb4166bcbff27c89d0624e06a09e8eacdc'
''' 2025.03.28 14:10 | 1 | ''' - "a014-interactive_system_instruction_chain.py"      # | 'd8360a30b1f0de6b312a0aac7d59a654b5ddd4862741643531f35ec9764c3df4'
''' 2025.03.28 14:14 | 1 | ''' - "a015-interactive_system_instruction_chain.log.yml" # | '79b266e45e72dac874dc0b3a42e35bda427639d491e858fbc0b72ef115c71025'
''' 2025.03.28 14:13 | 1 | ''' - "a015-interactive_system_instruction_chain.py"      # | 'd0bdd8e1e9c59183b2d86560e920ac5fbe8e29fc0b9bf6d540166787e305696b'
''' 2025.03.28 14:32 | 1 | ''' - "a016-interactive_system_instruction_chain.log.yml" # | 'dafbf61749549eb7b9baf9a345631e073f4f15a14a3afc15c3c87c1ae5f0ba9e'
''' 2025.03.28 14:31 | 1 | ''' - "a016-interactive_system_instruction_chain.py"      # | '7c7957e25b4dd03e7d53a052f1e70b04ddf8dc3834e99dab788dcd3d903b0e46'
''' 2025.03.28 15:01 | 1 | ''' - "a017-interactive_system_instruction_chain.py"      # | '7c7957e25b4dd03e7d53a052f1e70b04ddf8dc3834e99dab788dcd3d903b0e46'
''' 2025.03.28 13:08 | 1 | ''' - "slett.py"                                          # | 'ab7f7e3112fd41cb2b32e2eff800cc806882494d4dc4c32e06096964d9d34250'
```

Similar to the optional optional depth, can you add optional column for file's size (kb)  between `| DEPTH` AND `| FILENAME`? Example:

```
#  YYYY.MM.DD  HH:MM | DEPTH | SIZE_KB | FILENAME                                                    | FILEHASH
''' 2025.03.20 17:33 | 1     | x       | ''' - "a001-prompt_through_system_instructions.py"        # | 'cff95ea9b115ed1045431dc435a302038904fea1bab7f6852efb73f3a6a32184'
''' 2025.03.02 22:24 | 1     | x       | ''' - "a002-interactive_system_instruction_chain.bat"     # | '1ba5c24140a314ab0b974e37b0d60791c467394bead42e8e5fb2fa840f27024b'
''' 2025.03.20 18:22 | 1     | x       | ''' - "a002-interactive_system_instruction_chain.log.yml" # | '50f1ed918d047521ce210c4b9a81fb301694156c87d3f26ba2f080280322289b'
''' 2025.03.20 17:39 | 1     | x       | ''' - "a002-interactive_system_instruction_chain.py"      # | '13ea64fe8b61fd86faf99352c7e8792eec3c06bec82fed048ddd462e460a0a0b'
''' 2025.03.20 18:02 | 1     | x       | ''' - "a003-interactive_system_instructions.py"           # | '3d5a7a7ebdd824cc80e21af6c272ba83af7a667cfe81aa030dbfcf9a85ad8f1d'
''' 2025.03.28 11:16 | 1     | x       | ''' - "a004-interactive_system_instruction_chain.log.yml" # | '5f22d6f45ec3897538d9e611b11f15e4adbb187922291a04f6ce181744a5e887'
''' 2025.03.28 11:32 | 1     | x       | ''' - "a005-interactive_system_instruction_chain.log.yml" # | '942b2df6fefe52275cf300461bcc9c82617a672946beb2a664b34d140a92e219'
''' 2025.03.28 11:31 | 1     | x       | ''' - "a005-interactive_system_instruction_chain.py"      # | '7b0e05c45b86a4eee6851a06fe2b0d4fabbfd7e21526c922e032aaf7c9ced21e'
''' 2025.03.28 11:40 | 1     | x       | ''' - "a006-interactive_system_instruction_chain.log.yml" # | 'a9f4cf12f88453110b157438cf0e31018be9775ba4d25e0432bc2b0a2be4daaa'
''' 2025.03.28 11:39 | 1     | x       | ''' - "a006-interactive_system_instruction_chain.py"      # | '1244321d4bbd0997f9336fa7076e4fbc7cbab9b089d8165dc68d0beaac60f1f6'
''' 2025.03.28 11:59 | 1     | x       | ''' - "a007-interactive_system_instruction_chain.log.yml" # | 'ce1afe5f4aedaf08deb02643ddfc5e621b25d2cab063fc367ca4fe0cff1a142a'
''' 2025.03.28 11:59 | 1     | x       | ''' - "a007-interactive_system_instruction_chain.py"      # | '894a8e6e34feb0697ecfe448b6acf094d9dc261ac6297dd54f747cf45d0be0f2'
''' 2025.03.28 12:35 | 1     | x       | ''' - "a008-interactive_system_instruction_chain.log.yml" # | 'e957c02625551910421e7edc0758dcedd03e83e1bc6493d726e9f3b31de18aef'
''' 2025.03.28 12:34 | 1     | x       | ''' - "a008-interactive_system_instruction_chain.py"      # | 'bc28e5931b4a54a5d4c67aa5c75e5dfb01539fb5bd20e1ed2ea241fa4894d524'
''' 2025.03.28 13:27 | 1     | x       | ''' - "a009-interactive_system_instruction_chain.log.yml" # | '72d3e351559dfdfa8d1995a4830766e038d04460980c7833975eb562a4746e01'
''' 2025.03.28 13:26 | 1     | x       | ''' - "a009-interactive_system_instruction_chain.py"      # | 'd4144af2de9bb7852da9f1f6e94c4076c6a6b1ed8bf0dd419954a3c43d0eb0f9'
''' 2025.03.28 13:23 | 1     | x       | ''' - "a010-interactive_system_instruction_chain.log.yml" # | '64395122b7e86b13f9cef4ec9c3510683c951116a9551b60f5bf2db83dbe37dc'
''' 2025.03.28 13:22 | 1     | x       | ''' - "a010-interactive_system_instruction_chain.py"      # | 'f0b2c15b2f11e661ec05d77019b508f185ddddf4219e5bb2df932c2194c9f11e'
''' 2025.03.28 13:36 | 1     | x       | ''' - "a011-interactive_system_instruction_chain.log.yml" # | 'fd2f568281da2fc700363f869804c2e249e1010f4032003286c22de6c68c857b'
''' 2025.03.28 13:35 | 1     | x       | ''' - "a011-interactive_system_instruction_chain.py"      # | '0fa153bfa84fa48778dbe7b34365f4db59522537dc2b05231a85adb91776288d'
''' 2025.03.28 14:01 | 1     | x       | ''' - "a012-interactive_system_instruction_chain.log.yml" # | 'b3eff689b96d98ca55fd1e00758f82f54a1b10b580a2a0f2e2fba51cd8315ff5'
''' 2025.03.28 14:00 | 1     | x       | ''' - "a012-interactive_system_instruction_chain.py"      # | 'b8e0043fa395b96af47c9d7e9b308cdec813ebbc546d24f21058c4d0f5509667'
''' 2025.03.28 14:03 | 1     | x       | ''' - "a013-interactive_system_instruction_chain.log.yml" # | '408fed50112505d7c4cd2088b18419de3fe213d8f64ebf553aa90cf9d628ad05'
''' 2025.03.28 13:26 | 1     | x       | ''' - "a013-interactive_system_instruction_chain.py"      # | 'd4144af2de9bb7852da9f1f6e94c4076c6a6b1ed8bf0dd419954a3c43d0eb0f9'
''' 2025.03.28 14:11 | 1     | x       | ''' - "a014-interactive_system_instruction_chain.log.yml" # | 'fc84412517e052981e0a44970bcc40cb4166bcbff27c89d0624e06a09e8eacdc'
''' 2025.03.28 14:10 | 1     | x       | ''' - "a014-interactive_system_instruction_chain.py"      # | 'd8360a30b1f0de6b312a0aac7d59a654b5ddd4862741643531f35ec9764c3df4'
''' 2025.03.28 14:14 | 1     | x       | ''' - "a015-interactive_system_instruction_chain.log.yml" # | '79b266e45e72dac874dc0b3a42e35bda427639d491e858fbc0b72ef115c71025'
''' 2025.03.28 14:13 | 1     | x       | ''' - "a015-interactive_system_instruction_chain.py"      # | 'd0bdd8e1e9c59183b2d86560e920ac5fbe8e29fc0b9bf6d540166787e305696b'
''' 2025.03.28 14:32 | 1     | x       | ''' - "a016-interactive_system_instruction_chain.log.yml" # | 'dafbf61749549eb7b9baf9a345631e073f4f15a14a3afc15c3c87c1ae5f0ba9e'
''' 2025.03.28 14:31 | 1     | x       | ''' - "a016-interactive_system_instruction_chain.py"      # | '7c7957e25b4dd03e7d53a052f1e70b04ddf8dc3834e99dab788dcd3d903b0e46'
''' 2025.03.28 15:01 | 1     | x       | ''' - "a017-interactive_system_instruction_chain.py"      # | '7c7957e25b4dd03e7d53a052f1e70b04ddf8dc3834e99dab788dcd3d903b0e46'
''' 2025.03.28 13:08 | 1     | x       | ''' - "slett.py"                                          # | 'ab7f7e3112fd41cb2b32e2eff800cc806882494d4dc4c32e06096964d9d34250'
```

