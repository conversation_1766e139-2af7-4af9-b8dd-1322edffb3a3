# Project Files Documentation for `v6_1`

### File Structure

```
└── docs
│   ├── development.md
│   ├── installation.md
│   ├── usage.md
└── src
│   ├── __init__.py
│   ├── __main__.py
│   └── core
│   │   ├── directory_manager.py
│   │   ├── file_processor.py
│   │   ├── hash_manager.py
│   └── ui
│   │   ├── cli.py
│   │   ├── editor.py
│   └── utils
│   │   ├── config.py
│   │   ├── logging.py
│   └── windows
│   │   ├── context_menu.py
└── test_files
│   ├── renamed_renamed_renamed_file1.txt
│   ├── renamed_renamed_renamed_file2.txt
│   └── subdir
│   │   ├── renamed_renamed_renamed_file3.txt
└── tests
│   ├── __init__.py
│   ├── test_directory_manager.py
│   ├── test_file_processor.py
│   ├── test_hash_manager.py
```
### 1. `docs\development.md`

#### `docs\development.md`

```markdown
# Development Guide

## Project Structure


```
### 2. `docs\installation.md`

#### `docs\installation.md`

```markdown
# Installation Guide

## Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

## Installation Steps

1. **Clone the repository**
   
```
### 3. `docs\usage.md`

#### `docs\usage.md`

```markdown
# Usage Guide

## Command Line Interface

### Basic Commands

1. **Rename Files**
   
```
### 4. `src\__init__.py`

#### `src\__init__.py`

```python
"""
File Renamer Utility
A Python-based file renaming utility with hash verification and Windows Explorer integration.
"""

__version__ = '1.0.0'
__author__ = 'File Renamer Team'

```
### 5. `src\__main__.py`

#### `src\__main__.py`

```python
import sys
from src.ui.cli import main

if __name__ == "__main__":
    sys.exit(main())

```
### 6. `src\core\directory_manager.py`

#### `src\core\directory_manager.py`

```python
import os
import pathlib
from typing import List, Optional

from src.utils.logging import Logger, LogLevel

class DirectoryManager:
    """Manages directory operations and structure visualization."""

    def __init__(self, root_dir: pathlib.Path, logger: Logger):
        self.root_dir = root_dir
        self.logger = logger

    def ensure_directory(self, path: pathlib.Path) -> bool:
        """Creates directory if it doesn't exist."""
        try:
            path.mkdir(parents=True, exist_ok=True)
            return True
        except OSError as error:
            self.logger.log(f"Failed to create directory {path}: {error}", LogLevel.ERROR)
            return False

    def get_directory_structure(self, max_depth: Optional[int] = None) -> List[str]:
        """Returns a formatted list representing the directory structure."""
        if not self.root_dir.exists():
            self.logger.log(f"Directory not found: {self.root_dir}", LogLevel.ERROR)
            return []

        structure = []
        try:
            self._build_structure(self.root_dir, structure, "", 0, max_depth)
        except PermissionError as error:
            self.logger.log(f"Access denied: {error}", LogLevel.ERROR)
        
        return structure

    def _build_structure(
        self,
        directory: pathlib.Path,
        structure: List[str],
        indent: str,
        current_depth: int,
        max_depth: Optional[int]
    ) -> None:
        """Recursively builds the directory structure."""
        if max_depth is not None and current_depth > max_depth:
            return

        try:
            entries = sorted(
                directory.iterdir(),
                key=lambda p: (p.is_file(), p.name.lower())
            )

            for entry in entries:
                if entry.name.startswith('.'):
                    continue

                rel_path = entry.relative_to(self.root_dir)
                if entry.is_dir():
                    structure.append(f"{indent}📁 {entry.name}/")
                    self._build_structure(
                        entry,
                        structure,
                        indent + "  ",
                        current_depth + 1,
                        max_depth
                    )
                else:
                    structure.append(f"{indent}📄 {entry.name}")

        except PermissionError as error:
            structure.append(f"{indent}🚫 Access Denied: {directory.name}")

```
### 7. `src\core\file_processor.py`

#### `src\core\file_processor.py`

```python
import os
import pathlib
import shutil
from typing import Dict, List, Optional, Set, Tuple

from src.utils.logging import Logger, LogLevel
from src.core.hash_manager import HashManager

class FileProcessor:
    """Handles file operations including hashing and renaming."""

    def __init__(self, root_dir: pathlib.Path, logger: Logger):
        self.root_dir = root_dir
        self.logger = logger
        self.hash_manager = HashManager()

    def collect_files(self, include_subdirs: bool = True) -> List[pathlib.Path]:
        """Collects all files in the directory."""
        files = []
        for root, _, filenames in os.walk(self.root_dir):
            for filename in filenames:
                file_path = pathlib.Path(root) / filename
                if self._is_valid_file(file_path):
                    files.append(file_path)
            if not include_subdirs:
                break
        return files

    def _is_valid_file(self, path: pathlib.Path) -> bool:
        """Checks if a file is valid and accessible."""
        if not path.is_file() or not os.access(path, os.R_OK):
            self.logger.log(f"`{path}` is not accessible", LogLevel.WARNING)
            return False
        return True

    def rename_file(self, source: pathlib.Path, target: pathlib.Path, dry_run: bool = False, verify: bool = True) -> bool:
        """Renames a file with optional hash verification."""
        if not source.exists():
            self.logger.log(f"Source missing: {source}", LogLevel.ERROR)
            return False

        if target.exists() and target != source:
            self.logger.log(f"Target exists: {target}", LogLevel.ERROR)
            return False

        try:
            if verify:
                original_hash = HashManager.compute_sha256(source, self.logger)
                if not original_hash:
                    return False

            target.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(source), str(target))

            if verify:
                new_hash = HashManager.compute_sha256(target, self.logger)
                if new_hash != original_hash:
                    self.logger.log(
                        f"Hash verification failed after move: {target}",
                        LogLevel.ERROR
                    )
                    return False

            self.logger.log(
                f'Renamed: "{source.relative_to(self.root_dir)}" â†’ '
                f'"{target.relative_to(self.root_dir)}"',
                LogLevel.CHANGE
            )
            return True

        except OSError as error:
            self.logger.log(f"Failed to rename {source}: {error}", LogLevel.ERROR)
            return False

```
### 8. `src\core\hash_manager.py`

#### `src\core\hash_manager.py`

```python
import hashlib
import pathlib
from typing import Optional

from src.utils.logging import Logger, LogLevel

class HashManager:
    """Manages file hash computation and verification."""
    
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path, logger: Logger) -> Optional[str]:
        """Computes SHA256 hash for a given file."""
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(HashManager.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.log(f"Error reading `{file_path}`: {error}", LogLevel.ERROR)
            return None

    @staticmethod
    def verify_hash(file_path: pathlib.Path, expected_hash: str, logger: Logger) -> bool:
        """Verifies if a file matches an expected hash."""
        actual_hash = HashManager.compute_sha256(file_path, logger)
        if not actual_hash:
            return False
        
        matches = actual_hash.lower() == expected_hash.lower()
        if not matches:
            logger.log(
                f"Hash mismatch for {file_path}",
                LogLevel.WARNING,
                f"Expected: {expected_hash}\nActual: {actual_hash}"
            )
        return matches

```
### 9. `src\ui\cli.py`

#### `src\ui\cli.py`

```python
import argparse
import pathlib
import sys
from typing import List, Optional

from rich.console import Console
from rich.panel import Panel
from rich.prompt import Confirm
from rich.table import Table

from src.utils.logging import Logger, LogLevel, LogConfig
from src.utils.config import Config
from src.core.directory_manager import DirectoryManager
from src.core.file_processor import FileProcessor
from src.ui.editor import EditorManager

class CLI:
    """Command-line interface for the file renaming utility."""

    def __init__(self):
        self.console = Console()
        self.config = Config()
        self.logger = Logger(self.config.log_config)

    def run(self, args: Optional[List[str]] = None) -> int:
        """Main entry point for the CLI."""
        parser = self._create_parser()
        parsed_args = parser.parse_args(args)

        try:
            return self._execute_command(parsed_args)
        except Exception as error:
            self.logger.log(f"Unexpected error: {error}", LogLevel.ERROR)
            return 1

    def _create_parser(self) -> argparse.ArgumentParser:
        """Creates the argument parser."""
        parser = argparse.ArgumentParser(
            description="File renaming utility with hash verification"
        )

        parser.add_argument(
            "-v", "--verbose",
            action="store_true",
            help="Enable verbose output"
        )

        subparsers = parser.add_subparsers(dest="command", required=True)

        # Rename command
        rename_parser = subparsers.add_parser("rename", help="Rename files")
        rename_parser.add_argument(
            "directory",
            type=pathlib.Path,
            help="Directory containing files to rename"
        )
        rename_parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be done without making changes"
        )

        # View command
        view_parser = subparsers.add_parser("view", help="View directory structure")
        view_parser.add_argument(
            "directory",
            type=pathlib.Path,
            help="Directory to view"
        )
        view_parser.add_argument(
            "--depth",
            type=int,
            help="Maximum depth to display"
        )

        return parser

    def _execute_command(self, args: argparse.Namespace) -> int:
        """Executes the specified command."""
        if args.verbose:
            self.config.log_config.verbosity = LogLevel.VERBOSE

        if not args.directory.exists():
            self.logger.log(f"Directory not found: {args.directory}", LogLevel.ERROR)
            return 1

        if args.command == "rename":
            return self._handle_rename(args)
        elif args.command == "view":
            return self._handle_view(args)
        
        return 1

    def _handle_rename(self, args: argparse.Namespace) -> int:
        """Handles the rename command."""
        processor = FileProcessor(args.directory, self.logger)
        editor = EditorManager(self.logger)

        files = processor.collect_files()
        if not files:
            self.logger.log("No files found to rename", LogLevel.WARNING)
            return 1

        if not editor.edit_rename_plan(files):
            self.logger.log("Failed to create rename plan", LogLevel.ERROR)
            return 1

        success = True
        for old_path in files:
            new_name = f"renamed_{old_path.name}"
            new_path = old_path.parent / new_name
            if not processor.rename_file(old_path, new_path, dry_run=args.dry_run):
                success = False

        if args.dry_run:
            self.logger.log("Dry run completed", LogLevel.SUMMARY)
            return 0 if success else 1

        if not Confirm.ask("Proceed with renaming?"):
            self.logger.log("Operation cancelled", LogLevel.WARNING)
            return 1

        return 0 if success else 1

    def _handle_view(self, args: argparse.Namespace) -> int:
        """Handles the view command."""
        manager = DirectoryManager(args.directory, self.logger)
        structure = manager.get_directory_structure(args.depth)
        
        if not structure:
            return 1

        table = Table(show_header=False, box=None)
        table.add_column("Structure")
        for line in structure:
            table.add_row(line)

        self.console.print("\n")
        self.console.print(Panel(table, title="Directory Structure"))
        return 0

def main() -> int:
    """Entry point for the CLI."""
    return CLI().run()

if __name__ == "__main__":
    sys.exit(main())

```
### 10. `src\ui\editor.py`

#### `src\ui\editor.py`

```python
import os
import pathlib
import subprocess
import sys
import tempfile
from typing import List, Optional

from src.utils.logging import Logger, LogLevel

class EditorManager:
    """Manages text editor integration for rename operations."""

    def __init__(self, logger: Logger):
        self.logger = logger

    def edit_rename_plan(self, files: List[pathlib.Path]) -> bool:
        """Creates and opens a rename plan in the default text editor."""
        editor = self._get_default_editor()
        if not editor:
            return False

        with tempfile.NamedTemporaryFile(
            mode='w+',
            suffix='.txt',
            delete=False,
            encoding='utf-8'
        ) as temp_file:
            try:
                self._write_rename_plan(temp_file, files)
                temp_file.flush()
                
                if not self._open_editor(editor, temp_file.name):
                    return False

                return self._read_rename_plan(temp_file.name)
            finally:
                try:
                    os.unlink(temp_file.name)
                except OSError:
                    pass

    def _get_default_editor(self) -> Optional[str]:
        """Returns a simple file handling approach for Replit environment."""
        return "simple"

    def _command_exists(self, cmd: str) -> bool:
        """Checks if a command exists in the system path."""
        return any(
            os.access(os.path.join(path, cmd), os.X_OK)
            for path in os.environ["PATH"].split(os.pathsep)
            if os.path.exists(path)
        )

    def _open_editor(self, editor: str, file_path: str) -> bool:
        """Handles file editing based on editor type."""
        if editor == "simple":
            return True
        return False

    def _write_rename_plan(self, file, files: List[pathlib.Path]) -> None:
        """Writes the initial rename plan to a file."""
        file.write("# File Rename Plan\n")
        file.write("# Format: <current_path> -> <new_path>\n")
        file.write("# Lines starting with # are ignored\n\n")

        for path in sorted(files):
            file.write(f"{path} -> {path}\n")

    def _read_rename_plan(self, file_path: str) -> bool:
        """Creates a simple rename plan for demonstration."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f if line.strip() and not line.startswith('#')]
            
            # In simple mode, we'll just append a prefix to the files
            base_dir = pathlib.Path(file_path).parent
            for line in lines:
                source = pathlib.Path(line.split(' -> ')[0])
                if source.exists():
                    new_name = f"renamed_{source.name}"
                    with open(file_path, 'a', encoding='utf-8') as f:
                        f.write(f"{source} -> {source.parent / new_name}\n")
            
            return True
        except IOError as error:
            self.logger.log(f"Failed to read/write rename plan: {error}", LogLevel.ERROR)
            return False

```
### 11. `src\utils\config.py`

#### `src\utils\config.py`

```python
import json
import os
import pathlib
from dataclasses import dataclass
from typing import Dict, Optional

from src.utils.logging import LogConfig, LogLevel

@dataclass
class Config:
    """Application configuration."""
    log_config: LogConfig
    default_editor: Optional[str] = None
    backup_enabled: bool = True
    max_depth: Optional[int] = None

    def __init__(self):
        self.config_dir = self._get_config_dir()
        self.config_file = self.config_dir / "config.json"
        self.log_config = LogConfig()
        self.load()

    def load(self) -> None:
        """Loads configuration from file."""
        if not self.config_file.exists():
            self.save()
            return

        try:
            with self.config_file.open('r', encoding='utf-8') as f:
                data = json.load(f)
                self._update_from_dict(data)
        except (json.JSONDecodeError, OSError) as error:
            print(f"Error loading config: {error}")

    def save(self) -> None:
        """Saves configuration to file."""
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            with self.config_file.open('w', encoding='utf-8') as f:
                json.dump(
                    {
                        "verbosity": self.log_config.verbosity.name,
                        "show_skipped": self.log_config.show_skipped,
                        "show_unchanged": self.log_config.show_unchanged,
                        "use_colors": self.log_config.use_colors,
                        "default_editor": self.default_editor,
                        "backup_enabled": self.backup_enabled,
                        "max_depth": self.max_depth,
                    },
                    f,
                    indent=4
                )
        except OSError as error:
            print(f"Error saving config: {error}")

    def _update_from_dict(self, data: Dict) -> None:
        """Updates configuration from a dictionary."""
        if "verbosity" in data:
            self.log_config.verbosity = LogLevel[data["verbosity"]]
        if "show_skipped" in data:
            self.log_config.show_skipped = data["show_skipped"]
        if "show_unchanged" in data:
            self.log_config.show_unchanged = data["show_unchanged"]
        if "use_colors" in data:
            self.log_config.use_colors = data["use_colors"]
        if "default_editor" in data:
            self.default_editor = data["default_editor"]
        if "backup_enabled" in data:
            self.backup_enabled = data["backup_enabled"]
        if "max_depth" in data:
            self.max_depth = data["max_depth"]

    def _get_config_dir(self) -> pathlib.Path:
        """Gets the configuration directory path."""
        if os.name == 'nt':
            base_dir = pathlib.Path(os.environ.get('APPDATA', ''))
        else:
            base_dir = pathlib.Path.home() / '.config'
        
        return base_dir / "file_renamer"

```
### 12. `src\utils\logging.py`

#### `src\utils\logging.py`

```python
from dataclasses import dataclass
from enum import Enum, auto
from typing import Optional

from rich.console import Console
from rich.panel import Panel
from rich.table import Table

class LogLevel(Enum):
    ERROR = auto()
    WARNING = auto()
    INFO = auto()
    DEBUG = auto()
    CHANGE = auto()
    SKIP = auto()
    SUMMARY = auto()

@dataclass
class LogConfig:
    """Configuration for logging behavior."""
    verbosity: LogLevel = LogLevel.INFO
    show_skipped: bool = False
    show_unchanged: bool = False
    use_colors: bool = True

class Logger:
    """Enhanced logging with rich formatting."""

    def __init__(self, config: LogConfig):
        self.config = config
        self.console = Console(
            color_system="auto" if config.use_colors else None
        )
        self.error_count = 0
        self.warning_count = 0
        self.change_count = 0
        self.skip_count = 0

    def log(
        self,
        message: str,
        level: LogLevel,
        details: Optional[str] = None
    ) -> None:
        """Logs a message with the specified level."""
        if not self._should_log(level):
            return

        if level == LogLevel.SKIP and not self.config.show_skipped:
            self.skip_count += 1
            return

        self._update_counters(level)
        
        style = self._get_style(level)
        prefix = self._get_prefix(level)
        
        styled_message = f"[{style}]{prefix} {message}[/]"
        
        if details and self.config.verbosity == LogLevel.DEBUG:
            styled_message += f"\n  [{style}]{details}[/]"
        
        self.console.print(styled_message)

    def _should_log(self, level: LogLevel) -> bool:
        """Determines if a message should be logged based on verbosity."""
        level_values = {
            LogLevel.ERROR: 0,
            LogLevel.WARNING: 1,
            LogLevel.INFO: 2,
            LogLevel.DEBUG: 3,
            LogLevel.CHANGE: 2,
            LogLevel.SKIP: 2,
            LogLevel.SUMMARY: 1,
        }

        return level_values[level] <= level_values[self.config.verbosity]

    def _update_counters(self, level: LogLevel) -> None:
        """Updates internal counters based on log level."""
        if level == LogLevel.ERROR:
            self.error_count += 1
        elif level == LogLevel.WARNING:
            self.warning_count += 1
        elif level == LogLevel.CHANGE:
            self.change_count += 1

    def _get_style(self, level: LogLevel) -> str:
        """Gets the style for a log level."""
        return {
            LogLevel.ERROR: "bold red",
            LogLevel.WARNING: "yellow",
            LogLevel.INFO: "blue",
            LogLevel.DEBUG: "dim white",
            LogLevel.CHANGE: "bold green",
            LogLevel.SKIP: "dim white",
            LogLevel.SUMMARY: "bold blue",
        }[level]

    def _get_prefix(self, level: LogLevel) -> str:
        """Gets the prefix for a log level."""
        return {
            LogLevel.ERROR: "❌",
            LogLevel.WARNING: "⚠️ ",
            LogLevel.INFO: "ℹ️ ",
            LogLevel.DEBUG: "🔍",
            LogLevel.CHANGE: "✨",
            LogLevel.SKIP: "⏭️ ",
            LogLevel.SUMMARY: "📋",
        }[level]

    def print_summary(self) -> None:
        """Prints a summary of all operations."""
        if not self._should_log(LogLevel.SUMMARY):
            return

        table = Table(title="Operation Summary", show_header=False, box=None)
        table.add_column("Type", style="bold")
        table.add_column("Count", justify="right")

        table.add_row("Changes made", str(self.change_count))
        if self.config.show_skipped:
            table.add_row("Items skipped", str(self.skip_count))
        if self.warning_count > 0:
            table.add_row("Warnings", str(self.warning_count))
        if self.error_count > 0:
            table.add_row("Errors", f"[red]{self.error_count}[/]")

        self.console.print("\n")
        self.console.print(Panel(table, border_style="blue"))

```
### 13. `src\windows\context_menu.py`

#### `src\windows\context_menu.py`

```python
import os
import sys
import winreg
from typing import Optional

from src.utils.logging import Logger, LogLevel

class WindowsContextMenu:
    """Manages Windows Explorer context menu integration."""

    def __init__(self, logger: Logger):
        self.logger = logger
        self.app_name = "FileRenamer"
        self.command_key = "Software\\Classes\\Directory\\shell\\" + self.app_name

    def install(self, executable_path: str) -> bool:
        """Installs the context menu entry."""
        if not sys.platform.startswith('win'):
            self.logger.log(
                "Context menu integration only supported on Windows",
                LogLevel.ERROR
            )
            return False

        try:
            # Create main menu item
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER, self.command_key) as key:
                winreg.SetValue(key, "", winreg.REG_SZ, "Batch Rename Files")
                winreg.SetValueEx(
                    key,
                    "Icon",
                    0,
                    winreg.REG_SZ,
                    executable_path
                )

            # Create command
            command_path = os.path.join(self.command_key, "command")
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER, command_path) as key:
                command = f'"{executable_path}" rename "%V"'
                winreg.SetValue(key, "", winreg.REG_SZ, command)

            self.logger.log(
                "Context menu integration installed successfully",
                LogLevel.CHANGE
            )
            return True

        except WindowsError as error:
            self.logger.log(
                f"Failed to install context menu: {error}",
                LogLevel.ERROR
            )
            return False

    def uninstall(self) -> bool:
        """Removes the context menu entry."""
        if not sys.platform.startswith('win'):
            return False

        try:
            self._delete_registry_key(self.command_key)
            self.logger.log(
                "Context menu integration removed successfully",
                LogLevel.CHANGE
            )
            return True

        except WindowsError as error:
            self.logger.log(
                f"Failed to remove context menu: {error}",
                LogLevel.ERROR
            )
            return False

    def _delete_registry_key(self, key_path: str) -> None:
        """Recursively deletes a registry key and all its subkeys."""
        try:
            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                key_path,
                0,
                winreg.KEY_ALL_ACCESS
            ) as key:
                while True:
                    try:
                        sub_key = winreg.EnumKey(key, 0)
                        self._delete_registry_key(os.path.join(key_path, sub_key))
                    except OSError:
                        break

            winreg.DeleteKey(winreg.HKEY_CURRENT_USER, key_path)
        except WindowsError:
            pass

    def is_installed(self) -> bool:
        """Checks if the context menu entry is installed."""
        try:
            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                self.command_key,
                0,
                winreg.KEY_READ
            ):
                return True
        except WindowsError:
            return False

```
### 14. `test_files\renamed_renamed_renamed_file1.txt`

#### `test_files\renamed_renamed_renamed_file1.txt`

```text
Content for file 1

```
### 15. `test_files\renamed_renamed_renamed_file2.txt`

#### `test_files\renamed_renamed_renamed_file2.txt`

```text
Content for file 2

```
### 16. `test_files\subdir\renamed_renamed_renamed_file3.txt`

#### `test_files\subdir\renamed_renamed_renamed_file3.txt`

```text
Content for file 3

```
### 17. `tests\__init__.py`

#### `tests\__init__.py`

```python
"""
Test suite for the File Renamer utility.
"""

```
### 18. `tests\test_directory_manager.py`

#### `tests\test_directory_manager.py`

```python
import os
import pathlib
import tempfile
import unittest
from unittest.mock import MagicMock

from src.core.directory_manager import DirectoryManager
from src.utils.logging import Logger, LogConfig

class TestDirectoryManager(unittest.TestCase):
    """Test cases for DirectoryManager."""

    def setUp(self):
        self.logger = Logger(LogConfig())
        self.temp_dir = tempfile.TemporaryDirectory()
        self.test_dir = pathlib.Path(self.temp_dir.name)
        self.manager = DirectoryManager(self.test_dir, self.logger)

    def tearDown(self):
        self.temp_dir.cleanup()

    def test_ensure_directory(self):
        """Tests directory creation."""
        test_path = self.test_dir / "test_dir" / "subdir"
        
        self.assertTrue(self.manager.ensure_directory(test_path))
        self.assertTrue(test_path.exists())
        self.assertTrue(test_path.is_dir())

    def test_get_directory_structure(self):
        """Tests directory structure generation."""
        # Create test directory structure
        structure = {
            "dir1": ["file1.txt", "file2.txt"],
            "dir1/subdir": ["file3.txt"],
            "dir2": ["file4.txt"]
        }

        for dir_path, files in structure.items():
            full_dir = self.test_dir / dir_path
            full_dir.mkdir(parents=True, exist_ok=True)
            for file_name in files:
                (full_dir / file_name).touch()

        # Test without depth limit
        result = self.manager.get_directory_structure()
        self.assertTrue(any("dir1" in line for line in result))
        self.assertTrue(any("subdir" in line for line in result))
        self.assertTrue(any("file1.txt" in line for line in result))

        # Test with depth limit
        result = self.manager.get_directory_structure(max_depth=1)
        self.assertTrue(any("dir1" in line for line in result))
        self.assertFalse(any("file3.txt" in line for line in result))

    def test_get_directory_structure_nonexistent(self):
        """Tests structure generation for non-existent directory."""
        manager = DirectoryManager(self.test_dir / "nonexistent", self.logger)
        self.assertEqual(manager.get_directory_structure(), [])

    def test_get_directory_structure_permission_error(self):
        """Tests structure generation with permission error."""
        if os.name != 'nt':  # Skip on Windows
            test_dir = self.test_dir / "restricted"
            test_dir.mkdir()
            test_dir.chmod(0o000)
            
            try:
                result = self.manager.get_directory_structure()
                self.assertTrue(any("Access Denied" in line for line in result))
            finally:
                test_dir.chmod(0o755)

if __name__ == '__main__':
    unittest.main()

```
### 19. `tests\test_file_processor.py`

#### `tests\test_file_processor.py`

```python
import os
import pathlib
import tempfile
import unittest
from unittest.mock import MagicMock, patch

from src.core.file_processor import FileProcessor
from src.utils.logging import Logger, LogConfig

class TestFileProcessor(unittest.TestCase):
    """Test cases for FileProcessor."""

    def setUp(self):
        self.logger = Logger(LogConfig())
        self.temp_dir = tempfile.TemporaryDirectory()
        self.test_dir = pathlib.Path(self.temp_dir.name)
        self.processor = FileProcessor(self.test_dir, self.logger)

    def tearDown(self):
        self.temp_dir.cleanup()

    def test_collect_files(self):
        """Tests file collection."""
        # Create test files
        test_files = [
            "test1.txt",
            "test2.txt",
            "subdir/test3.txt"
        ]
        
        for file_path in test_files:
            full_path = self.test_dir / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.touch()

        # Test with subdirs
        files = self.processor.collect_files(include_subdirs=True)
        self.assertEqual(len(files), 3)

        # Test without subdirs
        files = self.processor.collect_files(include_subdirs=False)
        self.assertEqual(len(files), 2)

    def test_rename_file(self):
        """Tests file renaming."""
        source = self.test_dir / "source.txt"
        target = self.test_dir / "target.txt"
        
        source.write_text("test content")
        
        self.assertTrue(self.processor.rename_file(source, target))
        self.assertFalse(source.exists())
        self.assertTrue(target.exists())
        self.assertEqual(target.read_text(), "test content")

    def test_rename_file_nonexistent(self):
        """Tests renaming non-existent file."""
        source = self.test_dir / "nonexistent.txt"
        target = self.test_dir / "target.txt"
        
        self.assertFalse(self.processor.rename_file(source, target))

    def test_rename_file_target_exists(self):
        """Tests renaming when target already exists."""
        source = self.test_dir / "source.txt"
        target = self.test_dir / "target.txt"
        
        source.write_text("source content")
        target.write_text("target content")
        
        self.assertFalse(self.processor.rename_file(source, target))
        self.assertTrue(source.exists())
        self.assertTrue(target.exists())

    @patch('src.core.hash_manager.HashManager.compute_sha256')
    def test_rename_file_verify_hash(self, mock_compute):
        """Tests hash verification during rename."""
        mock_compute.side_effect = ["hash1", "hash1"]
        
        source = self.test_dir / "source.txt"
        target = self.test_dir / "target.txt"
        
        source.write_text("test content")
        
        self.assertTrue(self.processor.rename_file(source, target, verify=True))
        self.assertEqual(mock_compute.call_count, 2)

if __name__ == '__main__':
    unittest.main()

```
### 20. `tests\test_hash_manager.py`

#### `tests\test_hash_manager.py`

```python
import pathlib
import tempfile
import unittest
from unittest.mock import MagicMock

from src.core.hash_manager import HashManager
from src.utils.logging import Logger, LogConfig

class TestHashManager(unittest.TestCase):
    """Test cases for HashManager."""

    def setUp(self):
        self.logger = Logger(LogConfig())
        self.temp_dir = tempfile.TemporaryDirectory()
        self.test_dir = pathlib.Path(self.temp_dir.name)

    def tearDown(self):
        self.temp_dir.cleanup()

    def test_compute_sha256(self):
        """Tests SHA256 hash computation."""
        test_file = self.test_dir / "test.txt"
        test_content = "test content"
        expected_hash = "7b8df9b54c9f33c7b60a0951a9e11264"

        with test_file.open('w') as f:
            f.write(test_content)

        result = HashManager.compute_sha256(test_file, self.logger)
        self.assertIsNotNone(result)
        self.assertEqual(len(result), 64)  # SHA256 is 64 characters

    def test_compute_sha256_nonexistent_file(self):
        """Tests hash computation for non-existent file."""
        result = HashManager.compute_sha256(
            self.test_dir / "nonexistent.txt",
            self.logger
        )
        self.assertIsNone(result)

    def test_verify_hash(self):
        """Tests hash verification."""
        test_file = self.test_dir / "test.txt"
        test_content = "test content"

        with test_file.open('w') as f:
            f.write(test_content)

        hash_value = HashManager.compute_sha256(test_file, self.logger)
        self.assertTrue(
            HashManager.verify_hash(test_file, hash_value, self.logger)
        )

    def test_verify_hash_mismatch(self):
        """Tests hash verification with mismatched hash."""
        test_file = self.test_dir / "test.txt"
        test_content = "test content"

        with test_file.open('w') as f:
            f.write(test_content)

        self.assertFalse(
            HashManager.verify_hash(
                test_file,
                "invalid_hash",
                self.logger
            )
        )

if __name__ == '__main__':
    unittest.main()

```
