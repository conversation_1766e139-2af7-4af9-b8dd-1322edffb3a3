
# Response Guidelines:
- You carefully consider convergence and output to provide the most robust and valid solution, alongside a rationale that is the distilled essence of the iterative process. You always strive to ensure accuracy and thoughtfulness in your responses.
- Your responses should be technically excellent, balancing simplicity with functionality, adhering to SOLID principles, and avoiding unnecessary comments by making the code self-explanatory. Ensure that your solutions are not only innovative and contextually appropriate but also practical and easy for the user to apply immediately.
- To ensure that your advice is not just theoretical but directly actionable, always include practical, ready-to-use examples, such as command-line strings or debugging techniques that allow the user to implement or verify your suggestions immediately.
- Always lay a foundation worthy of building upon. This is a rule you live by, because it’s the difference between chaos and clarity. **All you do is point of departure**. Set the stage for success-It results in workspaces others can seamlessly take over, not only will this remove friction, but it is also *helpful* in the way for others easily can be exposed to and learn from *your* definitions.
- Stress the significance of eliminating redundant code to enhance efficiency and maintainability.
- Organize the code in a logical and consistent manner to facilitate understanding.

# Context:
- Project: A self-contained utility for batch renaming files through an intermediary text editor. This tool ensures file integrity by leveraging SHA256 hashes and offers a secure method to preview and execute batch renaming operations. Key functionalities include computing SHA256 hashes of files, recording these hashes to text files, renaming files based on hash matching, and visualizing directory structures for easy navigation and verification.
- Environment: Windows 11 workstation with integration into Windows Explorer's context menu for seamless user interaction.

Please respond whether or not you accept the premise, then I'll provide more information.
# Goal:
- Ensure that the overall architecture of script is concistent and well-structured.
- Ensure that variables, functions, and classes have concistent and meaningful names.
- Ensure the code include only brief, high-value comments that clarify the purpose of sections or explain complex logic, avoiding excessive commentary.
- Ensure the generated files (e.g. `FilesToText__filenames_ORG.py` and `FilesToText__filenames_NEW.py`) are cleaned up afterwards.

# Code:
```python
import argparse
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
from dataclasses import dataclass
from enum import Enum, auto
from typing import Dict, List, Optional, Set, Tuple

from rich.console import Console
from rich.panel import Panel
from rich.prompt import Confirm
from rich.table import Table


class VerbosityLevel(Enum):
    QUIET = auto()
    NORMAL = auto()
    VERBOSE = auto()
    DEBUG = auto()


class LogLevel(Enum):
    PROCESSED = auto()
    WARNING = auto()
    ERROR = auto()
    ACTION = auto()
    SUMMARY = auto()
    CHANGE = auto()
    SKIP = auto()


@dataclass
class LogConfig:
    verbosity: VerbosityLevel
    show_skipped: bool = False
    show_unchanged: bool = False


class Logger:
    def __init__(self, config: LogConfig):
        self.config = config
        self.console = Console(highlight=False)
        self.changes_count = 0
        self.skips_count = 0
        self.errors_count = 0

        self.level_mapping = {
            VerbosityLevel.QUIET: {LogLevel.ERROR, LogLevel.SUMMARY, LogLevel.CHANGE},
            VerbosityLevel.NORMAL: {LogLevel.ERROR, LogLevel.SUMMARY, LogLevel.CHANGE, LogLevel.WARNING},
            VerbosityLevel.VERBOSE: {LogLevel.ERROR, LogLevel.SUMMARY, LogLevel.CHANGE, LogLevel.WARNING,
                                   LogLevel.PROCESSED, LogLevel.ACTION},
            VerbosityLevel.DEBUG: {level for level in LogLevel}
        }

    def should_log(self, level: LogLevel) -> bool:
        return level in self.level_mapping[self.config.verbosity]

    def log(self, message: str, level: LogLevel, details: Optional[str] = None) -> None:
        if not self.should_log(level):
            return

        if level == LogLevel.SKIP and not self.config.show_skipped:
            self.skips_count += 1
            return

        style_map = {
            LogLevel.ERROR: "bold red",
            LogLevel.WARNING: "yellow",
            LogLevel.CHANGE: "bold green",
            LogLevel.SUMMARY: "bold blue",
            LogLevel.ACTION: "blue",
            LogLevel.PROCESSED: "bright_black",  # Changed from dim white for better visibility
            LogLevel.SKIP: "bright_black"
        }

        prefix_map = {
            LogLevel.ERROR: "❌",
            LogLevel.WARNING: "⚠️ ",
            LogLevel.CHANGE: "✨",
            LogLevel.SUMMARY: "📋",
            LogLevel.ACTION: "🔄",
            LogLevel.PROCESSED: "📝",
            LogLevel.SKIP: "⏭️ "
        }

        if level == LogLevel.ERROR:
            self.errors_count += 1
        elif level == LogLevel.CHANGE:
            self.changes_count += 1

        prefix = prefix_map.get(level, "")
        style = style_map[level]

        # Ensure the entire line is styled
        styled_message = f"[{style}]{prefix} {message}[/]"

        if details and self.config.verbosity == VerbosityLevel.DEBUG:
            styled_message += f"\n  [{style}]{details}[/]"

        self.console.print(styled_message)

    def print_summary(self) -> None:
        if not self.should_log(LogLevel.SUMMARY):
            return

        table = Table(title="Operation Summary", show_header=False, box=None)
        table.add_column("Type", style="bold")
        table.add_column("Count", justify="right")

        table.add_row("Changes made", str(self.changes_count))
        if self.config.show_skipped:
            table.add_row("Items skipped", str(self.skips_count))
        if self.errors_count > 0:
            table.add_row("Errors encountered", f"[red]{self.errors_count}[/]")

        self.console.print("\n")
        self.console.print(Panel(table, border_style="blue"))


class FileHasher:
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path) -> Optional[str]:
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            global_logger.log(f"Error reading `{file_path}`: {error}", LogLevel.ERROR)
            return None


class FileProcessor:
    def __init__(self, root_dir: pathlib.Path, include_subdirs: bool):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs

    def collect_file_hashes(self) -> List[Tuple[str, str]]:
        hash_entries = []
        for root, _, files in os.walk(self.root_dir):
            for filename in files:
                file_path = pathlib.Path(root) / filename
                if not self._is_valid_file(file_path):
                    continue

                relative_path = file_path.relative_to(self.root_dir).as_posix()
                file_hash = FileHasher.compute_sha256(file_path)

                if file_hash:
                    hash_entries.append((file_hash, relative_path))
                    global_logger.log(f"Processed: {relative_path}", LogLevel.PROCESSED)

            if not self.include_subdirs:
                break
        return hash_entries

    def _is_valid_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            global_logger.log(f"`{path}` is not accessible or not a file", LogLevel.WARNING)
            return False
        return True


class HashFileManager:
    def __init__(self, file_path: pathlib.Path):
        self.file_path = file_path

    def write(self, hash_entries: List[Tuple[str, str]]) -> None:
        try:
            max_filename_length = max(len(filename) for _, filename in hash_entries) + 2

            with self.file_path.open("w", encoding='utf-8') as f:
                f.write("# Hash to Filename Mapping\n")
                for file_hash, filename in sorted(hash_entries, key=lambda x: x[1].lower()):
                    padded_filename = f"'{filename}'".ljust(max_filename_length)
                    f.write(f"- {padded_filename} # | \"{file_hash}\"\n")
            global_logger.log(f"Hash file written: {self.file_path}", LogLevel.ACTION)
        except IOError as error:
            global_logger.log(f"Failed to write hash file: {error}", LogLevel.ERROR)

    def read(self) -> List[Tuple[str, str]]:
        hash_entries = []
        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line in f:
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            global_logger.log(f"Failed to read hash file: {error}", LogLevel.ERROR)
        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str]]:
        line = line.strip()
        if not (line.startswith("- '") and ' # | "' in line and line.endswith('"')):
            return None

        try:
            filename_part, hash_part = line.split(" # | \"")
            filename = filename_part.strip("- '").rstrip()
            file_hash = hash_part[:-1]
            return (file_hash, filename)
        except (IndexError, ValueError):
            global_logger.log(f"Invalid hash file entry: {line}", LogLevel.WARNING)
            return None


class FileRenamer:
    def __init__(self, root_dir: pathlib.Path):
        self.root_dir = root_dir

    def execute(self, source_hashes: List[Tuple[str, str]], target_hashes: List[Tuple[str, str]],
               dry_run: bool = True) -> bool:
        # Build path-to-hash and hash-to-paths mappings for source
        source_path_to_hash = {path: hash_val for hash_val, path in source_hashes}
        source_hash_to_paths = self._build_hash_to_paths_map(source_hashes)

        # Build the same mappings for target
        target_path_to_hash = {path: hash_val for hash_val, path in target_hashes}
        target_hash_to_paths = self._build_hash_to_paths_map(target_hashes)

        # Find best matches between source and target files
        rename_pairs = self._find_rename_pairs(
            source_hash_to_paths,
            target_hash_to_paths,
            source_path_to_hash
        )

        has_conflicts = False
        for source_path, target_path in rename_pairs:
            source_full = self.root_dir / source_path
            target_full = self.root_dir / target_path

            # Skip if source and target are the same
            if source_path == target_path:
                if global_logger.config.show_unchanged:
                    global_logger.log(f"Unchanged: {source_path}", LogLevel.SKIP)
                continue

            # Validate paths
            if not self._validate_paths(source_full, target_full):
                has_conflicts = True
                continue

            # Perform or simulate rename
            if dry_run:
                global_logger.log(
                    f'Will rename: "{source_path}" → "{target_path}"',
                    LogLevel.ACTION
                )
            else:
                self._perform_rename(source_full, target_full, source_path, target_path)

        self._log_completion(dry_run, has_conflicts)
        return not has_conflicts

    def _build_hash_to_paths_map(self, hash_entries: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        """Build a mapping of hash to list of paths, preserving order."""
        hash_to_paths: Dict[str, List[str]] = {}
        for hash_val, path in hash_entries:
            if hash_val not in hash_to_paths:
                hash_to_paths[hash_val] = []
            hash_to_paths[hash_val].append(path)
        return hash_to_paths

    def _find_rename_pairs(
        self,
        source_hash_to_paths: Dict[str, List[str]],
        target_hash_to_paths: Dict[str, List[str]],
        source_path_to_hash: Dict[str, str]
    ) -> List[Tuple[str, str]]:
        """
        Find matching pairs of files to rename based on hash and path similarity.
        Returns list of (source_path, target_path) tuples.
        """
        rename_pairs: List[Tuple[str, str]] = []
        processed_sources = set()
        processed_targets = set()

        # First pass: direct path matches for files with same hash
        for source_path, source_hash in source_path_to_hash.items():
            if source_path in processed_sources:
                continue

            # Find target paths with same hash
            target_paths = target_hash_to_paths.get(source_hash, [])
            if not target_paths:
                global_logger.log(
                    f"No target found with matching hash for: {source_path}",
                    LogLevel.WARNING
                )
                continue

            # Look for most similar path match
            best_match = self._find_best_path_match(
                source_path,
                [p for p in target_paths if p not in processed_targets]
            )

            if best_match:
                rename_pairs.append((source_path, best_match))
                processed_sources.add(source_path)
                processed_targets.add(best_match)

        return rename_pairs

    def _find_best_path_match(self, source_path: str, target_paths: List[str]) -> Optional[str]:
        """
        Find the best matching target path based on similarity.
        Uses path components and filename similarity for matching.
        """
        if not target_paths:
            return None

        source_parts = pathlib.Path(source_path).parts
        source_name = source_parts[-1]

        # First try to find a match with similar directory structure
        for target_path in target_paths:
            target_parts = pathlib.Path(target_path).parts
            # If directory structure matches exactly
            if source_parts[:-1] == target_parts[:-1]:
                return target_path

        # Then try to find a match with similar filename
        best_match = None
        best_similarity = 0
        for target_path in target_paths:
            target_name = pathlib.Path(target_path).name
            similarity = self._calculate_name_similarity(source_name, target_name)
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = target_path

        return best_match

    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """
        Calculate similarity between two filenames.
        Returns a value between 0 and 1, where 1 is most similar.
        """
        # Remove common prefixes/suffixes that might interfere
        common_prefixes = ['_', 'test_', 'file_']
        common_suffixes = ['.md', '.txt', '.webm']

        for prefix in common_prefixes:
            name1 = name1.removeprefix(prefix)
            name2 = name2.removeprefix(prefix)

        for suffix in common_suffixes:
            name1 = name1.removesuffix(suffix)
            name2 = name2.removesuffix(suffix)

        # Simple similarity: length of common prefix
        min_len = min(len(name1), len(name2))
        common_len = 0
        for i in range(min_len):
            if name1[i].lower() != name2[i].lower():
                break
            common_len += 1

        return common_len / max(len(name1), len(name2))

    def _validate_paths(self, source: pathlib.Path, target: pathlib.Path) -> bool:
        if not source.exists():
            global_logger.log(f"Source missing: {source.relative_to(self.root_dir)}", LogLevel.WARNING)
            return False
        if target.exists() and target != source:
            global_logger.log(f"Target exists: {target.relative_to(self.root_dir)}", LogLevel.ERROR)
            return False
        return True

    def _perform_rename(self, source: pathlib.Path, target: pathlib.Path,
                       source_rel: pathlib.Path, target_rel: pathlib.Path) -> None:
        try:
            target.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(source), str(target))
            global_logger.log(f'Renamed: "{source_rel}" → "{target_rel}"', LogLevel.CHANGE)
        except OSError as error:
            global_logger.log(f"Failed to rename {source_rel}: {error}", LogLevel.ERROR)

    def _log_completion(self, is_dry_run: bool, has_conflicts: bool) -> None:
        operation = "Dry run" if is_dry_run else "File renaming"
        if has_conflicts:
            global_logger.log(
                f"{operation}: Conflicts detected, some files skipped",
                LogLevel.WARNING
            )
        else:
            global_logger.log(f"{operation} completed successfully", LogLevel.SUMMARY)



class DirectoryVisualizer:
    def __init__(self, root_dir: pathlib.Path):
        self.root_dir = root_dir

    def display(self) -> None:
        if not self.root_dir.exists():
            global_logger.log(f"Directory not found: {self.root_dir}", LogLevel.ERROR)
            return

        global_logger.log(f"Directory structure: {self.root_dir}", LogLevel.SUMMARY)
        tree = []
        self._build_tree(self.root_dir, tree)
        for line in tree:
            global_logger.console.print(line)

    def _build_tree(self, directory: pathlib.Path, tree: List[str], indent: str = "") -> None:
        try:
            entries = sorted(directory.iterdir(), key=lambda p: (p.is_file(), p.name.lower()))
            for entry in entries:
                if entry.name.startswith("."):
                    continue

                if entry.is_dir():
                    tree.append(f"{indent}[blue]📁 {entry.name}/[/]")
                    self._build_tree(entry, tree, indent + "  ")
                else:
                    tree.append(f"{indent}[white]📄 {entry.name}[/]")
        except PermissionError as error:
            global_logger.log(f"Access denied: {error}", LogLevel.ERROR)


class FileEditor:
    @staticmethod
    def open(file_path: pathlib.Path) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(('open', str(file_path)))
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(('xdg-open', str(file_path)))
            else:
                global_logger.log(f"Unsupported platform: {sys.platform}", LogLevel.WARNING)
        except Exception as error:
            global_logger.log(f"Failed to open editor: {error}", LogLevel.ERROR)


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Batch Rename Utility with SHA256 Verification")
    subparsers = parser.add_subparsers(dest="command", required=True)

    common_args = argparse.ArgumentParser(add_help=False)
    verbosity_group = common_args.add_argument_group("output options")
    verbosity_group.add_argument(
        "-v", "--verbosity",
        choices=["quiet", "normal", "verbose", "debug"],
        default="normal",
        help="Set output verbosity level"
    )
    verbosity_group.add_argument(
        "--show-skipped",
        action="store_true",
        help="Show skipped files in output"
    )
    verbosity_group.add_argument(
        "--show-unchanged",
        action="store_true",
        help="Show unchanged files in output"
    )

    process_parser = subparsers.add_parser("process", parents=[common_args])
    process_parser.add_argument("directory", type=str)
    process_parser.add_argument("--include-subdirectories", action="store_true")
    process_parser.add_argument("--org-output", type=str, default="FilesToText__filenames_ORG.py")
    process_parser.add_argument("--new-output", type=str, default="FilesToText__filenames_NEW.py")
    process_parser.add_argument("--visualize", action="store_true")

    visualize_parser = subparsers.add_parser("visualize", parents=[common_args])
    visualize_parser.add_argument("directory", type=str)

    return parser.parse_args()


def get_log_config(args: argparse.Namespace) -> LogConfig:
    verbosity_map = {
        "quiet": VerbosityLevel.QUIET,
        "normal": VerbosityLevel.NORMAL,
        "verbose": VerbosityLevel.VERBOSE,
        "debug": VerbosityLevel.DEBUG
    }

    return LogConfig(
        verbosity=verbosity_map[args.verbosity],
        show_skipped=args.show_skipped,
        show_unchanged=args.show_unchanged
    )


def process_command(args: argparse.Namespace) -> None:
    root_dir = pathlib.Path(args.directory).resolve()
    if not root_dir.is_dir():
        global_logger.log(f"Invalid directory: {root_dir}", LogLevel.ERROR)
        sys.exit(1)

    org_file = pathlib.Path(args.org_output).resolve()
    new_file = pathlib.Path(args.new_output).resolve()

    processor = FileProcessor(root_dir, args.include_subdirectories)
    initial_hashes = processor.collect_file_hashes()

    for file_path in (org_file, new_file):
        manager = HashFileManager(file_path)
        manager.write(initial_hashes)

    global_logger.log("Opening new hash file for editing...", LogLevel.ACTION)
    FileEditor.open(new_file)

    if not Confirm.ask("\nProceed with renaming? [y/n]: "):
        global_logger.log("Operation cancelled by user", LogLevel.WARNING)
        return

    org_manager = HashFileManager(org_file)
    new_manager = HashFileManager(new_file)

    renamer = FileRenamer(root_dir)
    if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):
        if Confirm.ask("\nApply these changes? [y/n]: "):
            renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)

            if args.visualize:
                DirectoryVisualizer(root_dir).display()

            # Cleanup hash files
            for file_path in (org_file, new_file):
                try:
                    file_path.unlink()
                    global_logger.log(f"Cleaned up: {file_path}", LogLevel.ACTION)
                except OSError as error:
                    global_logger.log(f"Cleanup failed: {error}", LogLevel.WARNING)

    global_logger.print_summary()


def main() -> None:
    args = parse_args()

    global global_logger
    global_logger = Logger(get_log_config(args))

    if args.command == "process":
        process_command(args)
    elif args.command == "visualize":
        DirectoryVisualizer(pathlib.Path(args.directory).resolve()).display()
    else:
        global_logger.log("Unknown command", LogLevel.ERROR)


if __name__ == "__main__":
    main()


```
