
# Response Guidelines:
- You carefully consider convergence and output to provide the most robust and valid solution, alongside a rationale that is the distilled essence of the iterative process. You always strive to ensure accuracy and thoughtfulness in your responses.
- Your responses should be technically excellent, balancing simplicity with functionality, adhering to SOLID principles, and avoiding unnecessary comments by making the code self-explanatory. Ensure that your solutions are not only innovative and contextually appropriate but also practical and easy for the user to apply immediately.
- To ensure that your advice is not just theoretical but directly actionable, always include practical, ready-to-use examples, such as command-line strings or debugging techniques that allow the user to implement or verify your suggestions immediately.
- Always lay a foundation worthy of building upon. This is a rule you live by, because it’s the difference between chaos and clarity. **All you do is point of departure**. Set the stage for success-It results in workspaces others can seamlessly take over, not only will this remove friction, but it is also *helpful* in the way for others easily can be exposed to and learn from *your* definitions.
- Stress the significance of eliminating redundant code to enhance efficiency and maintainability.
- Organize the code in a logical and consistent manner to facilitate understanding.

# Context:
- Project: A self-contained utility for batch renaming files through an intermediary text editor. This tool ensures file integrity by leveraging SHA256 hashes and offers a secure method to preview and execute batch renaming operations. Key functionalities include computing SHA256 hashes of files, recording these hashes to text files, renaming files based on hash matching, and visualizing directory structures for easy navigation and verification.
- Environment: Windows 11 workstation with integration into Windows Explorer's context menu for seamless user interaction.

# Goal:
- Ensure that the overall architecture of script is concistent and well-structured.
- Ensure that variables, functions, and classes have concistent and meaningful names.
- Ensure the code include only brief, high-value comments that clarify the purpose of sections or explain complex logic, avoiding excessive commentary.
- Ensure the generated files (e.g. `FilesToText__filenames_ORG.py` and `FilesToText__filenames_NEW.py`) are cleaned up afterwards.

# Code:
```python
import argparse
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
from typing import List, Tuple

from rich.prompt import Confirm


class HashManager:
    """Handles SHA256 hash computations."""

    @staticmethod
    def compute_hash(file_path: pathlib.Path) -> str:
        """Compute the SHA256 hash of a file."""
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(4096), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            Logger.log_static(f"Error reading `{file_path}`: {error}", category="Errors")
            return ""


class Logger:
    """Handles categorized logging."""

    # Define static method to allow HashManager to log without an instance
    @staticmethod
    def log_static(message: str, category: str = "Info"):
        """Static method to log messages without needing an instance."""
        if category in Logger.enabled_categories_static:
            if category == "Processed":
                print(f"- Processed: `{message}`")
            elif category == "Warnings":
                print(f"- Warning: {message}")
            elif category == "Errors":
                print(f"- Error: {message}")
            elif category == "Actions":
                print(f"- Action: {message}")
            elif category == "Summary":
                print(f"\n**Summary:** {message}\n")
            else:
                print(f"- {message}")

    # Initialize static categories
    enabled_categories_static = set()

    def __init__(self, enabled_categories: List[str]):
        """
        Initialize the Logger with specified categories.

        Parameters:
            enabled_categories (List[str]): List of categories to display.
        """
        self.enabled_categories = set(enabled_categories)
        Logger.enabled_categories_static = self.enabled_categories  # Update static categories

    def log(self, message: str, category: str = "Info"):
        """
        Log a message under a specific category.

        Parameters:
            message (str): The message to log.
            category (str): The category under which to log the message.
        """
        if category in self.enabled_categories:
            if category == "Processed":
                print(f"- Processed: `{message}`")
            elif category == "Warnings":
                print(f"- Warning: {message}")
            elif category == "Errors":
                print(f"- Error: {message}")
            elif category == "Actions":
                print(f"- Action: {message}")
            elif category == "Summary":
                print(f"\n**Summary:** {message}\n")
            else:
                print(f"- {message}")

    def header(self, message: str, level: int = 1):
        """Print a header."""
        print(f"\n{'#' * level} {message}\n")


class FileRenamer:
    """Manages the batch renaming process."""

    def __init__(self, directory: pathlib.Path, include_subdirs: bool, logger: Logger):
        self.directory = directory
        self.include_subdirs = include_subdirs
        self.logger = logger

    def gather_file_hashes(self) -> List[Tuple[str, str]]:
        """Gather SHA256 hashes and relative file paths."""
        hashes = []
        for root, _, files in os.walk(self.directory):
            for filename in files:
                full_path = pathlib.Path(root) / filename
                if full_path.is_file() and os.access(full_path, os.R_OK):
                    relative_path = full_path.relative_to(self.directory).as_posix()
                    file_hash = HashManager.compute_hash(full_path)
                    if file_hash:
                        hashes.append((file_hash, relative_path))
                        self.logger.log(relative_path, category="Processed")
                else:
                    self.logger.log(f"`{full_path}` (Not accessible or not a file)", category="Warnings")
            if not self.include_subdirs:
                break
        return hashes

    def write_hashes(self, hashes: List[Tuple[str, str]], output_file: pathlib.Path) -> None:
        """Write hashes and filenames to a Python file with comments."""
        try:
            with output_file.open("w", encoding='utf-8') as file:
                file.write("# Hash to Filename Mapping\n")
                for file_hash, filename in hashes:
                    file.write(f"- '{filename}'  # | \"{file_hash}\"\n")
            self.logger.log(f"Hashes written to `{output_file}`", category="Actions")
        except IOError as error:
            self.logger.log(f"Failed to write to `{output_file}`: {error}", category="Errors")

    def read_hashes(self, file_path: pathlib.Path) -> List[Tuple[str, str]]:
        """Read hashes and filenames from a Python file."""
        hashes = []
        try:
            with file_path.open("r", encoding='utf-8') as file:
                for line in file:
                    line = line.strip()
                    if line.startswith("- '") and '# | "' in line and line.endswith('"'):
                        try:
                            # Extract filename and hash
                            filename_part, hash_part = line.split("# | \"")
                            filename = filename_part.split("'")[1]
                            hash_val = hash_part[:-1]  # Remove trailing '"'
                            hashes.append((hash_val, filename))
                        except (IndexError, ValueError):
                            self.logger.log(f"Malformed line in `{file_path}`: {line}", category="Warnings")
        except IOError as error:
            self.logger.log(f"Failed to read `{file_path}`: {error}", category="Errors")
        return hashes

    def execute_renaming(self, org_hashes: List[Tuple[str, str]], new_hashes: List[Tuple[str, str]], dry_run: bool = False):
        """Execute renaming based on the new hash file."""
        original_map = {hash_val: original_name for hash_val, original_name in org_hashes}
        new_map = {hash_val: new_name for hash_val, new_name in new_hashes}

        all_hashes = set(original_map.keys()) & set(new_map.keys())

        conflicts = False

        for hash_val in all_hashes:
            original_name = original_map.get(hash_val)
            new_name = new_map.get(hash_val)

            if not original_name or not new_name:
                continue  # Skip if either name is missing

            original_path = self.directory / original_name
            new_path = self.directory / new_name

            if not original_path.exists():
                self.logger.log(f"Original file `{original_path}` does not exist. Skipping.", category="Warnings")
                continue

            if original_path.resolve() == new_path.resolve():
                self.logger.log(f"No change for `{original_name}`. Skipping.", category="Processed")
                continue

            if new_path.exists():
                self.logger.log(f"Target file `{new_path}` already exists. Conflict detected.", category="Errors")
                conflicts = True
                continue

            if dry_run:
                self.logger.log(f'"{original_path}" -> "{new_path}"', category="Actions")
            else:
                try:
                    new_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.move(str(original_path), str(new_path))
                    self.logger.log(f'"{original_path}" -> "{new_path}"', category="Actions")
                except OSError as error:
                    self.logger.log(f"Failed to rename/move `{original_path}` to `{new_path}`: {error}", category="Errors")

        if dry_run:
            if conflicts:
                self.logger.log("Conflicts detected during dry run. Some files may not be processed.", category="Warnings")
            else:
                self.logger.log("Dry run completed successfully. No conflicts detected.", category="Summary")
        else:
            if conflicts:
                self.logger.log("Conflicts detected during renaming. Some files were not processed.", category="Warnings")
            else:
                self.logger.log("File renaming completed successfully.", category="Summary")

    def perform_renaming(self, org_file: pathlib.Path, new_file: pathlib.Path) -> None:
        """Coordinate the renaming process with a dry-run option."""
        org_hashes = self.read_hashes(org_file)
        new_hashes = self.read_hashes(new_file)

        # Dry run to preview changes
        self.logger.header("Performing dry run to preview changes...")
        self.execute_renaming(org_hashes, new_hashes, dry_run=True)

        if Confirm.ask("Do you want to proceed with these changes? [y/n]: "):
            self.logger.header("Executing renaming...")
            self.execute_renaming(org_hashes, new_hashes, dry_run=False)
        else:
            self.logger.log("Operation aborted by user.", category="Warnings")


class DirectoryVisualizer:
    """Generates a visual representation of the directory structure."""

    def __init__(self, logger: Logger):
        self.logger = logger

    def build_directory_tree(self, directory: pathlib.Path, tree: List[str], indent: str = ""):
        """Recursively build a tree structure of the directory."""
        try:
            entries = sorted(
                directory.iterdir(),
                key=lambda p: (p.is_file(), p.name.lower()),
            )
        except PermissionError as error:
            self.logger.log(f"Permission denied: {error}", category="Errors")
            return

        for entry in entries:
            if entry.name.startswith("."):
                continue
            if entry.is_dir():
                tree.append(f"{indent}- 📁 {entry.name}/")
                self.build_directory_tree(entry, tree, indent + "  ")
            else:
                tree.append(f"{indent}- 📄 {entry.name}")

    def visualize(self, directory: pathlib.Path):
        """Display the directory tree."""
        if not directory.exists():
            self.logger.log(f"Error: Directory '{directory}' does not exist.", category="Errors")
            return

        tree = []
        self.build_directory_tree(directory, tree)
        self.logger.header(f"Directory Structure for `{directory}`", level=2)
        for line in tree:
            print(line)


def open_in_editor(file_path: pathlib.Path, logger: Logger) -> None:
    """Open a file in the default text editor."""
    try:
        if sys.platform.startswith('darwin'):
            subprocess.call(('open', str(file_path)))
        elif os.name == 'nt':
            os.startfile(str(file_path))
        elif os.name == 'posix':
            subprocess.call(('xdg-open', str(file_path)))
        else:
            logger.log(f"Unsupported OS for opening files: {sys.platform}", category="Warnings")
    except Exception as error:
        logger.log(f"Failed to open `{file_path}`: {error}", category="Errors")


def parse_args() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Batch Rename Utility with SHA256 Verification")
    subparsers = parser.add_subparsers(dest="command", required=True, help="Available commands")

    # Process Command
    process_parser = subparsers.add_parser("process", help="Generate hash files and rename files")
    process_parser.add_argument("directory", type=str, help="Target directory for processing")
    process_parser.add_argument("--include-subdirectories", action="store_true", help="Include subdirectories in processing")
    process_parser.add_argument("--org-output", type=str, default="FilesToText__filenames_ORG.py", help="Output file for original filenames and hashes")
    process_parser.add_argument("--new-output", type=str, default="FilesToText__filenames_NEW.py", help="Output file for new filenames and hashes")
    process_parser.add_argument("--visualize", action="store_true", help="Visualize directory structure after renaming")
    process_parser.add_argument("--show-processed", action="store_true", help="Show processed files")
    process_parser.add_argument("--show-warnings", action="store_true", help="Show warnings")
    process_parser.add_argument("--show-errors", action="store_true", help="Show errors")
    process_parser.add_argument("--show-actions", action="store_true", help="Show actions taken (e.g., renaming/moving)")
    process_parser.add_argument("--show-summary", action="store_true", help="Show summary of operations")

    # Visualize Command
    visualize_parser = subparsers.add_parser("visualize", help="Display directory tree")
    visualize_parser.add_argument("directory", type=str, help="Directory to visualize")

    return parser.parse_args()


def main() -> None:
    """Main function to execute based on user command."""
    args = parse_args()

    # Determine which categories to show based on arguments
    categories = []
    if args.command == "process":
        if args.show_processed:
            categories.append("Processed")
        if args.show_warnings:
            categories.append("Warnings")
        if args.show_errors:
            categories.append("Errors")
        if args.show_actions:
            categories.append("Actions")
        if args.show_summary:
            categories.append("Summary")
    elif args.command == "visualize":
        # Visualization might not need categorized logging
        categories = []

    # If no specific categories are enabled, default to essential ones
    if not categories and args.command == "process":
        categories = ["Processed", "Actions", "Summary"]

    logger = Logger(categories)

    if args.command == "process":
        target_dir = pathlib.Path(args.directory).resolve()
        org_file = pathlib.Path(args.org_output).resolve()
        new_file = pathlib.Path(args.new_output).resolve()

        if not target_dir.is_dir():
            logger.log(f"Error: '{target_dir}' is not a valid directory.", category="Errors")
            sys.exit(1)

        renamer = FileRenamer(target_dir, include_subdirs=args.include_subdirectories, logger=logger)

        logger.header(f"Generating hash files for `{target_dir}`...")

        # Generate initial hash mappings
        initial_hashes = renamer.gather_file_hashes()
        renamer.write_hashes(initial_hashes, org_file)
        renamer.write_hashes(initial_hashes, new_file)

        logger.log("Hash files generated successfully.", category="Summary")
        logger.log(f"Opening `{new_file}` for editing...", category="Actions")
        open_in_editor(new_file, logger)

        # Wait for user confirmation
        if Confirm.ask("Have you updated the new filenames in the hash file? Proceed to dry run and renaming? [y/n]: "):
            logger.header("Regenerating hash files to get the current file structure...")
            updated_hashes = renamer.gather_file_hashes()
            renamer.write_hashes(updated_hashes, org_file)
            logger.log("Updated hash files generated successfully.", category="Summary")

            # Perform renaming with dry run
            renamer.perform_renaming(org_file, new_file)

            if args.visualize:
                visualizer = DirectoryVisualizer(logger)
                visualizer.visualize(target_dir)
        else:
            logger.log("Operation aborted by user.", category="Warnings")

    elif args.command == "visualize":
        target_dir = pathlib.Path(args.directory).resolve()
        visualizer = DirectoryVisualizer(logger)
        visualizer.visualize(target_dir)

    else:
        logger.log("Unknown command.", category="Errors")


if __name__ == "__main__":
    main()

```
