import json
import os
import pathlib
from dataclasses import dataclass
from typing import Dict, Optional

from src.utils.logging import LogConfig, LogLevel

@dataclass
class Config:
    """Application configuration."""
    log_config: LogConfig
    default_editor: Optional[str] = None
    backup_enabled: bool = True
    max_depth: Optional[int] = None

    def __init__(self):
        self.config_dir = self._get_config_dir()
        self.config_file = self.config_dir / "config.json"
        self.log_config = LogConfig()
        self.load()

    def load(self) -> None:
        """Loads configuration from file."""
        if not self.config_file.exists():
            self.save()
            return

        try:
            with self.config_file.open('r', encoding='utf-8') as f:
                data = json.load(f)
                self._update_from_dict(data)
        except (json.JSONDecodeError, OSError) as error:
            print(f"Error loading config: {error}")

    def save(self) -> None:
        """Saves configuration to file."""
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            with self.config_file.open('w', encoding='utf-8') as f:
                json.dump(
                    {
                        "verbosity": self.log_config.verbosity.name,
                        "show_skipped": self.log_config.show_skipped,
                        "show_unchanged": self.log_config.show_unchanged,
                        "use_colors": self.log_config.use_colors,
                        "default_editor": self.default_editor,
                        "backup_enabled": self.backup_enabled,
                        "max_depth": self.max_depth,
                    },
                    f,
                    indent=4
                )
        except OSError as error:
            print(f"Error saving config: {error}")

    def _update_from_dict(self, data: Dict) -> None:
        """Updates configuration from a dictionary."""
        if "verbosity" in data:
            self.log_config.verbosity = LogLevel[data["verbosity"]]
        if "show_skipped" in data:
            self.log_config.show_skipped = data["show_skipped"]
        if "show_unchanged" in data:
            self.log_config.show_unchanged = data["show_unchanged"]
        if "use_colors" in data:
            self.log_config.use_colors = data["use_colors"]
        if "default_editor" in data:
            self.default_editor = data["default_editor"]
        if "backup_enabled" in data:
            self.backup_enabled = data["backup_enabled"]
        if "max_depth" in data:
            self.max_depth = data["max_depth"]

    def _get_config_dir(self) -> pathlib.Path:
        """Gets the configuration directory path."""
        if os.name == 'nt':
            base_dir = pathlib.Path(os.environ.get('APPDATA', ''))
        else:
            base_dir = pathlib.Path.home() / '.config'
        
        return base_dir / "file_renamer"
