""" Utility for batch renaming files through an intermediary text editor """

import argparse
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
import re
import unicodedata

from pathlib import Path
from dataclasses import dataclass
from enum import Enum, auto
from typing import Dict, List, Optional, Set, Tuple

from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.box import ROUNDED


class VerbosityLevel(Enum):
    QUIET = auto()
    NORMAL = auto()
    VERBOSE = auto()
    DEBUG = auto()


class LogLevel(Enum):
    PROCESSED = auto()
    WARNING = auto()
    ERROR = auto()
    ACTION = auto()
    SUMMARY = auto()
    CHANGE = auto()
    SKIP = auto()
    DEBUG = auto()


@dataclass
class LogConfig:
    verbosity: VerbosityLevel
    show_skipped: bool = False
    show_unchanged: bool = False


class Logger:
    """Handles logging with different verbosity levels and styles."""

    def __init__(self, config: LogConfig):
        self.config = config
        self.console = Console(highlight=False)
        self.changes = 0
        self.skips = 0
        self.errors = 0

        self.level_mapping = {
            VerbosityLevel.QUIET: {LogLevel.ERROR, LogLevel.SUMMARY, LogLevel.CHANGE},
            VerbosityLevel.NORMAL: {
                LogLevel.ERROR,
                LogLevel.SUMMARY,
                LogLevel.CHANGE,
                LogLevel.WARNING,
            },
            VerbosityLevel.VERBOSE: {
                LogLevel.ERROR,
                LogLevel.SUMMARY,
                LogLevel.CHANGE,
                LogLevel.WARNING,
                LogLevel.PROCESSED,
                LogLevel.ACTION,
            },
            VerbosityLevel.DEBUG: set(LogLevel),
        }

    def should_log(self, level: LogLevel) -> bool:
        return level in self.level_mapping[self.config.verbosity]

    def log(self, message: str, level: LogLevel, details: Optional[str] = None) -> None:
        if not self.should_log(level):
            return

        if level == LogLevel.SKIP and not self.config.show_skipped:
            self.skips += 1
            return

        styles = {
            LogLevel.ERROR: "bold red",
            LogLevel.WARNING: "yellow",
            LogLevel.CHANGE: "bold green",
            LogLevel.SUMMARY: "bold blue",
            LogLevel.ACTION: "blue",
            LogLevel.PROCESSED: "bright_black",
            LogLevel.SKIP: "bright_black",
            LogLevel.DEBUG: "dim cyan",
        }

        prefixes = {
            LogLevel.ERROR: "❌",
            LogLevel.WARNING: "⚠️ ",
            LogLevel.CHANGE: "✨",
            LogLevel.SUMMARY: "📋",
            LogLevel.ACTION: "🔄",
            LogLevel.PROCESSED: "📝",
            LogLevel.SKIP: "⏭️ ",
            LogLevel.DEBUG: "🐞 ",
        }

        if level == LogLevel.ERROR:
            self.errors += 1
        elif level == LogLevel.CHANGE:
            self.changes += 1

        styled_message = f"[{styles[level]}]{prefixes[level]} {message}[/]"

        if details and self.config.verbosity == VerbosityLevel.DEBUG:
            styled_message += f"\n  [{styles[level]}]{details}[/]"

        self.console.print(styled_message)

    def print_summary(self) -> None:
        if not self.should_log(LogLevel.SUMMARY):
            return

        table = Table(title="Operation Summary", show_header=False, box=None)
        table.add_column("Type", style="bold")
        table.add_column("Count", justify="right")

        table.add_row("Changes made", str(self.changes))
        if self.config.show_skipped:
            table.add_row("Items skipped", str(self.skips))
        if self.errors > 0:
            table.add_row("Errors encountered", f"[red]{self.errors}[/]")

        self.console.print("\n")
        self.console.print(Panel(table, border_style="blue"))


class FileHasher:
    """Computes SHA256 hashes for files."""
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path, logger: Logger) -> Optional[str]:
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.log(f"Error reading `{file_path}`: {error}", LogLevel.ERROR)
            return None


class FileProcessor:
    """Processes files to collect their hashes."""

    def __init__(self, root_dir: pathlib.Path, include_subdirs: bool, logger: Logger):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs
        self.logger = logger

    def collect_file_hashes(self) -> List[Tuple[str, str]]:
        hash_entries = []
        for root, _, files in os.walk(self.root_dir):
            for filename in files:
                file_path = pathlib.Path(root) / filename
                if not self._is_accessible_file(file_path):
                    continue

                relative_path = file_path.relative_to(self.root_dir).as_posix()
                file_hash = FileHasher.compute_sha256(file_path, self.logger)

                if file_hash:
                    hash_entries.append((file_hash, relative_path))
                    self.logger.log(f"Processed: {relative_path}", LogLevel.PROCESSED)

            if not self.include_subdirs:
                break
        return hash_entries

    def _is_accessible_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            self.logger.log(f"`{path}` is not accessible or not a file", LogLevel.WARNING)
            return False
        return True


class HashFileManager:
    """Manages reading and writing hash files."""

    def __init__(self, file_path: pathlib.Path, logger: Logger):
        self.file_path = file_path
        self.logger = logger

    def write(self, hash_entries: List[Tuple[str, str]]) -> None:
        try:
            max_length = max(len(filename) for _, filename in hash_entries) + 2
            with self.file_path.open("w", encoding='utf-8') as f:
                f.write("# Hash to Filename Mapping\n")
                for file_hash, filename in sorted(hash_entries, key=lambda x: x[1].lower()):
                    padded_filename = f"'{filename}'".ljust(max_length)
                    f.write(f"- {padded_filename} # | \"{file_hash}\"\n")
            self.logger.log(f"Hash file written: {self.file_path}", LogLevel.ACTION)
        except IOError as error:
            self.logger.log(f"Failed to write hash file: {error}", LogLevel.ERROR)

    def read(self) -> List[Tuple[str, str]]:
        hash_entries = []
        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line in f:
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            self.logger.log(f"Failed to read hash file: {error}", LogLevel.ERROR)
        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str]]:
        line = line.strip()
        if not (line.startswith("- '") and ' # | "' in line and line.endswith('"')):
            return None

        try:
            filename_part, hash_part = line.split(" # | \"")
            filename = filename_part.strip("- '").rstrip()
            file_hash = hash_part[:-1]
            return (file_hash, filename)
        except (IndexError, ValueError):
            self.logger.log(f"Invalid hash file entry: {line}", LogLevel.WARNING)
            return None


class FileRenamer:
    """Handles the renaming of files based on hash comparisons."""

    def __init__(self, root_dir: pathlib.Path, logger: Logger):
        self.root_dir = root_dir
        self.logger = logger

    def execute(
        self,
        source_hashes: List[Tuple[str, str]],
        target_hashes: List[Tuple[str, str]],
        dry_run: bool = True
    ) -> bool:
        source_map = self._map_hash_to_paths(source_hashes)
        target_map = self._map_hash_to_paths(target_hashes)

        rename_pairs = self._determine_rename_pairs(source_map, target_map)
        conflicts = False

        if dry_run:
            self._preview_renames(rename_pairs)

        for src_rel, tgt_rel in rename_pairs:
            src_path = self.root_dir / src_rel
            tgt_path = self.root_dir / tgt_rel

            if src_rel == tgt_rel:
                if self.logger.config.show_unchanged:
                    self.logger.log(f"Unchanged: {src_rel}", LogLevel.SKIP)
                continue

            if not self._validate_paths(src_path, tgt_path):
                conflicts = True
                continue

            if dry_run:
                self.logger.log(f'Will rename: "{src_rel}" → "{tgt_rel}"', LogLevel.ACTION)
            else:
                self._perform_rename(src_path, tgt_path, src_rel, tgt_rel)

        self._log_completion(dry_run, conflicts)
        return not conflicts

    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        hash_map: Dict[str, List[str]] = {}
        for file_hash, path in hash_entries:
            hash_map.setdefault(file_hash, []).append(path)
        return hash_map

    def _determine_rename_pairs(
        self,
        source_map: Dict[str, List[str]],
        target_map: Dict[str, List[str]],
    ) -> List[Tuple[str, str]]:
        pairs: List[Tuple[str, str]] = []
        processed_targets: Set[str] = set()

        for file_hash, src_paths in source_map.items():
            tgt_paths = target_map.get(file_hash, [])
            for src in src_paths:
                if any(src == pair[0] for pair in pairs):
                    continue

                available_tgts = [t for t in tgt_paths if t not in processed_targets]
                if not available_tgts:
                    self.logger.log(f"No matching hash for: {src}", LogLevel.WARNING)
                    continue

                best_match = self._select_best_match(src, available_tgts)
                if best_match:
                    pairs.append((src, best_match))
                    processed_targets.add(best_match)

        return pairs

    def _select_best_match(self, source: str, targets: List[str]) -> Optional[str]:
        source_clean = self._clean_name(source)
        best_similarity = -1
        best_target = None

        for tgt in targets:
            tgt_clean = self._clean_name(tgt)
            similarity = self._name_similarity(source_clean, tgt_clean)
            if similarity > best_similarity:
                best_similarity = similarity
                best_target = tgt

        if best_target:
            self.logger.log(
                f"Best match for '{source}' is '{best_target}' with similarity {best_similarity:.2f}",
                LogLevel.DEBUG
            )
        else:
            self.logger.log(f"No suitable match found for '{source}'", LogLevel.WARNING)

        return best_target

    @staticmethod
    def _name_similarity(name1: str, name2: str) -> float:
        matches = sum(a == b for a, b in zip(name1, name2))
        max_len = max(len(name1), len(name2))
        return matches / max_len if max_len else 0

    @staticmethod
    def _clean_name(name: str) -> str:
        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')
        name = name.lower()
        name = pathlib.Path(name).stem
        name = re.sub(r'[^a-z0-9]', '', name)
        return name

    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:
        if not src.exists():
            self.logger.log(f"Source missing: {src.relative_to(self.root_dir)}", LogLevel.WARNING)
            return False
        if tgt.exists() and tgt != src:
            self.logger.log(f"Target exists: {tgt.relative_to(self.root_dir)}", LogLevel.ERROR)
            return False
        return True

    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str) -> None:
        try:
            tgt.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src), str(tgt))
            self.logger.log(f'Renamed: "{src_rel}" → "{tgt_rel}"', LogLevel.CHANGE)
        except OSError as error:
            self.logger.log(f"Failed to rename {src_rel}: {error}", LogLevel.ERROR)

    def _preview_renames(self, rename_pairs: List[Tuple[str, str]]) -> None:
        if not rename_pairs:
            self.logger.log("No files require renaming", LogLevel.WARNING)
            return

        table = Table(
            title="Pending Rename Operations",
            show_header=True,
            header_style="bold blue",
            box=ROUNDED
        )

        table.add_column("Operation", style="cyan", width=4)
        table.add_column("Source", style="white")
        table.add_column("Target", style="green")

        changes = 0
        for src, tgt in rename_pairs:
            if src != tgt:
                table.add_row("→", src, tgt)
                changes += 1

        if changes:
            self.logger.console.print("\n")
            self.logger.console.print(Panel(table, border_style="blue"))
            self.logger.console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")
        else:
            self.logger.log("No files require renaming", LogLevel.WARNING)

    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:
        operation = "Dry run" if dry_run else "File renaming"
        if has_conflicts:
            self.logger.log(f"{operation}: Conflicts detected, some files skipped", LogLevel.WARNING)
        else:
            self.logger.log(f"{operation} completed successfully", LogLevel.SUMMARY)


class FileEditor:
    """Opens files using the default system editor."""

    @staticmethod
    def open(file_path: pathlib.Path, logger: Logger) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', str(file_path)])
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(['xdg-open', str(file_path)])
            else:
                logger.log(f"Unsupported platform: {sys.platform}", LogLevel.WARNING)
        except Exception as error:
            logger.log(f"Failed to open editor: {error}", LogLevel.ERROR)


def parse_arguments() -> argparse.Namespace:
    """Parses command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Batch Rename Utility with SHA256 Verification",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "directory",
        type=str,
        nargs='?',
        help="Target directory for processing"
    )
    parser.add_argument(
        "--include-subdirectories",
        action="store_true",
        help="Include subdirectories"
    )
    parser.add_argument(
        "-v", "--verbosity",
        choices=["quiet", "normal", "verbose", "debug"],
        default="normal",
        help="Set output verbosity level"
    )
    parser.add_argument(
        "--show-skipped",
        action="store_true",
        help="Show skipped files in output"
    )
    parser.add_argument(
        "--show-unchanged",
        action="store_true",
        help="Show unchanged files in output"
    )
    parser.add_argument(
        "--prompt",
        action="store_true",
        help="Enable interactive prompting for arguments"
    )

    return parser.parse_args()


def prompt_for_arguments(args: argparse.Namespace) -> argparse.Namespace:
    """Handles interactive prompting for command arguments."""
    console = Console()

    if args.prompt:
        args.directory = Prompt.ask("Enter directory path", default=args.directory or "").strip()
        args.include_subdirectories = Confirm.ask("Include subdirectories?", default=args.include_subdirectories)

    if not args.directory:
        console.print("[red]Error:[/] the following arguments are required: directory")
        sys.exit(1)

    root_dir = pathlib.Path(args.directory)
    if not root_dir.exists() or not root_dir.is_dir():
        console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")
        sys.exit(1)

    return args


def configure_logging(args: argparse.Namespace) -> LogConfig:
    """Configures logging based on command-line arguments."""
    verbosity_map = {
        "quiet": VerbosityLevel.QUIET,
        "normal": VerbosityLevel.NORMAL,
        "verbose": VerbosityLevel.VERBOSE,
        "debug": VerbosityLevel.DEBUG
    }

    return LogConfig(
        verbosity=verbosity_map[args.verbosity],
        show_skipped=args.show_skipped,
        show_unchanged=args.show_unchanged
    )


def handle_process_command(args: argparse.Namespace, logger: Logger) -> None:
    """Handles the main processing and renaming of files."""
    root_dir = pathlib.Path(args.directory).resolve()

    org_file = root_dir / ".original_hashes.py"
    new_file = root_dir / ".new_hashes.py"

    processor = FileProcessor(root_dir, args.include_subdirectories, logger)
    initial_hashes = processor.collect_file_hashes()

    for file_path in (org_file, new_file):
        manager = HashFileManager(file_path, logger)
        manager.write(initial_hashes)

    logger.log("Opening new hash file for editing...", LogLevel.ACTION)
    FileEditor.open(new_file, logger)

    if not Confirm.ask("\nProceed with renaming? [y/n]: "):
        logger.log("Operation cancelled by user", LogLevel.WARNING)
        return

    org_manager = HashFileManager(org_file, logger)
    new_manager = HashFileManager(new_file, logger)

    renamer = FileRenamer(root_dir, logger)
    if renamer.execute(org_manager.read(), new_manager.read(), dry_run=True):
        if Confirm.ask("\nApply these changes? [y/n]: "):
            renamer.execute(org_manager.read(), new_manager.read(), dry_run=False)

            for file_path in (org_file, new_file):
                try:
                    file_path.unlink()
                    logger.log(f"Cleaned up: {file_path}", LogLevel.ACTION)
                except OSError as error:
                    logger.log(f"Cleanup failed: {error}", LogLevel.WARNING)

    logger.print_summary()


def main() -> None:
    """Main entry point of the utility."""
    args = parse_arguments()
    args = prompt_for_arguments(args)
    logger = Logger(configure_logging(args))

    handle_process_command(args, logger)


if __name__ == "__main__":
    main()
