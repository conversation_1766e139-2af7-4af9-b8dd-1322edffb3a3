"""
list_directory_tree.py

Displays a tree of files/directories using the Tree renderable from the Rich library.
"""

import os
import pathlib
import sys

from rich import print
from rich.filesize import decimal
from rich.markup import escape
from rich.text import Text
from rich.tree import Tree


def create_file_text(path: pathlib.Path) -> Text:
    """Creates a formatted Text object for a file."""
    text_filename = Text(path.name, "green")
    text_filename.highlight_regex(r"\..*$", "bold red")
    text_filename.stylize(f"link file://{path}")
    file_size = path.stat().st_size
    text_filename.append(f" ({decimal(file_size)})", "blue")
    icon = "🐍 " if path.suffix == ".py" else "📄 "
    return Text(icon) + text_filename


def walk_directory(directory: pathlib.Path, tree: Tree) -> None:
    """Recursively builds a Tree with directory contents."""
    paths = sorted(
        pathlib.Path(directory).iterdir(),
        key=lambda path: (path.is_file(), path.name.lower()),
    )

    for path in paths:
        if path.name.startswith("."):
            continue

        if path.is_dir():
            style = "dim" if path.name.startswith("__") else ""
            branch = tree.add(
                f"[bold magenta]:open_file_folder: [link file://{path}]{escape(path.name)}",
                style=style,
                guide_style=style,
            )
            walk_directory(path, branch)
        else:
            tree.add(create_file_text(path))


def main(directory: str) -> None:
    """Main function to display a directory tree."""
    tree = Tree(
        f":open_file_folder: [link file://{directory}]{directory}",
        guide_style="bold bright_blue",
    )
    walk_directory(pathlib.Path(directory), tree)
    print(tree)


if __name__ == "__main__":
    try:
        directory = os.path.abspath(sys.argv[1])
    except IndexError:
        print("[b]Usage:[/] python list_directory_tree.py <DIRECTORY>")
    else:
        main(directory)
