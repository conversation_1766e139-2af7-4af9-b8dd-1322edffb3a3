
It currently outputs like this (three columns):

```
#  YYYY.MM.DD  | FILENAME                                | FILEHASH
''' 2025.03.07 | ''' - "filename1.txt"                 # | '991852c2ca80b708ce3bda2c477a158abc1351769957086e400f805f5ec095fe'
''' 2025.03.07 | ''' - "filename2.txt"                 # | 'a12b3c8260a25916262371ff4e6646306bb9716f032c3545b6faa4ed82864a41'
''' 2025.03.07 | ''' - "filename3.txt"                 # | '0405f63ec7d058f0865a2d5ebc1da5a1ed549925c627a25109bd252f9b52f537'
```

---

Your goal is to modify it such that it **optionally** includes the time also (instead of `YYYY.MM.DD` it would be `YYYY.MM.DD HH:MM`):

```
#  YYYY.MM.DD  HH:MM | FILENAME                          | FILEHASH
''' 2025.03.07 21:13 | ''' - "filename1.txt"           # | '991852c2ca80b708ce3bda2c477a158abc1351769957086e400f805f5ec095fe'
''' 2025.03.07 21:17 | ''' - "filename2.txt"           # | 'a12b3c8260a25916262371ff4e6646306bb9716f032c3545b6faa4ed82864a41'
''' 2025.03.07 21:21 | ''' - "filename3.txt"           # | '0405f63ec7d058f0865a2d5ebc1da5a1ed549925c627a25109bd252f9b52f537'
```
