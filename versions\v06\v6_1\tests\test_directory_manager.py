import os
import pathlib
import tempfile
import unittest
from unittest.mock import MagicMock

from src.core.directory_manager import DirectoryManager
from src.utils.logging import Logger, LogConfig

class TestDirectoryManager(unittest.TestCase):
    """Test cases for DirectoryManager."""

    def setUp(self):
        self.logger = Logger(LogConfig())
        self.temp_dir = tempfile.TemporaryDirectory()
        self.test_dir = pathlib.Path(self.temp_dir.name)
        self.manager = DirectoryManager(self.test_dir, self.logger)

    def tearDown(self):
        self.temp_dir.cleanup()

    def test_ensure_directory(self):
        """Tests directory creation."""
        test_path = self.test_dir / "test_dir" / "subdir"
        
        self.assertTrue(self.manager.ensure_directory(test_path))
        self.assertTrue(test_path.exists())
        self.assertTrue(test_path.is_dir())

    def test_get_directory_structure(self):
        """Tests directory structure generation."""
        # Create test directory structure
        structure = {
            "dir1": ["file1.txt", "file2.txt"],
            "dir1/subdir": ["file3.txt"],
            "dir2": ["file4.txt"]
        }

        for dir_path, files in structure.items():
            full_dir = self.test_dir / dir_path
            full_dir.mkdir(parents=True, exist_ok=True)
            for file_name in files:
                (full_dir / file_name).touch()

        # Test without depth limit
        result = self.manager.get_directory_structure()
        self.assertTrue(any("dir1" in line for line in result))
        self.assertTrue(any("subdir" in line for line in result))
        self.assertTrue(any("file1.txt" in line for line in result))

        # Test with depth limit
        result = self.manager.get_directory_structure(max_depth=1)
        self.assertTrue(any("dir1" in line for line in result))
        self.assertFalse(any("file3.txt" in line for line in result))

    def test_get_directory_structure_nonexistent(self):
        """Tests structure generation for non-existent directory."""
        manager = DirectoryManager(self.test_dir / "nonexistent", self.logger)
        self.assertEqual(manager.get_directory_structure(), [])

    def test_get_directory_structure_permission_error(self):
        """Tests structure generation with permission error."""
        if os.name != 'nt':  # Skip on Windows
            test_dir = self.test_dir / "restricted"
            test_dir.mkdir()
            test_dir.chmod(0o000)
            
            try:
                result = self.manager.get_directory_structure()
                self.assertTrue(any("Access Denied" in line for line in result))
            finally:
                test_dir.chmod(0o755)

if __name__ == '__main__':
    unittest.main()
