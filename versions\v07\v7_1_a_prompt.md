<!-- 'https://chatgpt.com/c/67442d52-e64c-8008-ac21-d3d75fd84f26' -->

# Elements to adress:
- The reason why it's written as .py is that when the file is opened in the users default editor it will reap the benefits of syntax-highlighting (when e.g. opened in sublime text with python syntax). In the generated file you'll notice the hash being prefixed with `#`, which makes them appear as comments when opened with python syntax. Therefore I want to continue using .py.
- With regards to user experience in renaming the hash file, it will be automatically opened and represented as a flat list of files to the user. The benefit of doing it like this is that the user can do whatever he wants in the file (except from detaching the hashes from the filepaths/filenames), such as e.g. sorting/reordering the lines. Another benefit is that it's a really intuitive and quick way of renaming multiple different files/folders, by using e.g. multicursor line editing in sublime text.
- With regards to name similarity, I agree that this part could benefit from a more sophisticated approach. However it's important that it's not solely based on the hash, because that will cause problems in scenarios where there are file duplicates (duplicate hashes).
- The directory renaming is essential and needs to work properly, I agree that this should be improved. Additionally, whenever interacting (e.g. creating new) folders the date modified and date created should reflect those they were originally in before being moved/recreate by the script.



# Features to implement:
- Provide options to skip hashing for files that haven't changed since the last operation, leveraging metadata to save time.


