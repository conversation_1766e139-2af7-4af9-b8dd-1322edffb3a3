
import os
from datetime import datetime
import sys
sys.stdout.reconfigure(encoding='utf-8')

# Folder containing your images
folder = "D:/Dokumenter O.l/Documens/Privat/Dokumenter/Personlig/MobilBackup/Bilder/n"
folder = "D:/Dokumenter O.l/Documens/Privat/Dokumenter/Personlig/MobilBackup/VideoOpptak/n/n2"

# Ensure you're using the correct format for Windows
for filename in os.listdir(folder):
    if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
        try:
            filepath = os.path.join(folder, filename)

            # Get last modified time
            mod_time = os.path.getmtime(filepath)
            dt = datetime.fromtimestamp(mod_time)

            # Format: yyyy.mm.dd--kl.hh.mm.ss
            formatted_time = dt.strftime("%Y.%m.%d--kl.%H.%M.%S")

            # Keep original extension
            ext = os.path.splitext(filename)[1]
            new_name = f"{formatted_time}{ext}"
            new_path = os.path.join(folder, new_name)

            # Rename
            os.rename(filepath, new_path)
            print(f"Renamed: {filename} → {new_name}")
        except:
            pass
