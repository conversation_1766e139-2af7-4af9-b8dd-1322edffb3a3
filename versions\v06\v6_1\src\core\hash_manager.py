import hashlib
import pathlib
from typing import Optional

from src.utils.logging import Logger, LogLevel

class HashManager:
    """Manages file hash computation and verification."""
    
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path, logger: Logger) -> Optional[str]:
        """Computes SHA256 hash for a given file."""
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(HashManager.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.log(f"Error reading `{file_path}`: {error}", LogLevel.ERROR)
            return None

    @staticmethod
    def verify_hash(file_path: pathlib.Path, expected_hash: str, logger: Logger) -> bool:
        """Verifies if a file matches an expected hash."""
        actual_hash = HashManager.compute_sha256(file_path, logger)
        if not actual_hash:
            return False
        
        matches = actual_hash.lower() == expected_hash.lower()
        if not matches:
            logger.log(
                f"Hash mismatch for {file_path}",
                LogLevel.WARNING,
                f"Expected: {expected_hash}\nActual: {actual_hash}"
            )
        return matches
