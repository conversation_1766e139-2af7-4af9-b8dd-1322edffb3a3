# Git Commit History Graph 
 
Generated on: 2025-03-28 15:04:54 
 
## Commit: 12da2d9 - (11 days ago) +added exclusions and changed cli-order - DSK (HEAD -> master) 
 
### Changed Files: 
 
```diff 
~ (Modified) src.md 
~ (Modified) src/main.py 
``` 
 
| 
| 
 
## Commit: cb7b2d1 - (11 days ago) +added optional folderdepth column - DSK 
 
### Changed Files: 
 
```diff 
+ (Added) __meta__/c001_prompt.md 
~ (Modified) src.md 
~ (Modified) src/main.py 
``` 
 
| 
| 
 
## Commit: c498268 - (11 days ago) chk - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) .gitignore 
+ (Added) __meta__/2025.03.07_chatgpt.com.c.67cb2eb6-262c-8008-948d-4e2729bc53de.url 
~ (Modified) src/main.py 
``` 
 
| 
| 
 
## Commit: 8131528 - (3 weeks ago) ++chk:changed ".new_hashes" with timestamp - LAP 
 
### Changed Files: 
 
```diff 
~ (Modified) src.md 
~ (Modified) src/main.py 
``` 
 
| 
| 
 
## Commit: 7df1882 - (3 weeks ago) +chk:prompts - LAP 
 
### Changed Files: 
 
```diff 
- (Renamed from) .cmd/py_venv_pip_devmode.bat	__meta__/.cmd/py_venv_pip_devmode.bat 
- (Renamed from) .cmd/py_venv_pip_install.bat	__meta__/.cmd/py_venv_pip_install.bat 
- (Renamed from) .cmd/py_venv_run_script.bat	__meta__/.cmd/py_venv_run_script.bat 
- (Renamed from) .cmd/py_venv_terminal.bat	__meta__/.cmd/py_venv_terminal.bat 
- (Renamed from) .cmd/py_venv_upgrade_requirements.bat	__meta__/.cmd/py_venv_upgrade_requirements.bat 
- (Renamed from) .cmd/py_venv_write_requirements.bat	__meta__/.cmd/py_venv_write_requirements.bat 
+ (Added) __meta__/a001_prompt.md 
+ (Added) __meta__/a002_prompt.md 
+ (Added) __meta__/a003_prompt.md 
+ (Added) __meta__/b001_prompt.md 
``` 
 
| 
| 
 
## Commit: 17cdb73 - (3 weeks ago) ++chk:changed ".new_hashes" with datemodified prefix - LAP 
 
### Changed Files: 
 
```diff 
~ (Modified) src.md 
~ (Modified) src/main.py 
``` 
 
| 
| 
 
## Commit: bd03b77 - (3 weeks ago) defaults - LAP 
 
### Changed Files: 
 
```diff 
~ (Modified) src.md 
~ (Modified) src/main.py 
``` 
 
| 
| 
 
## Commit: b7c1bdb - (3 weeks ago) chk - LAP 
 
### Changed Files: 
 
```diff 
+ (Added) versions/v8_works/main.bat 
+ (Added) versions/v8_works/main.py 
``` 
 
| 
| 
 
## Commit: 466a0ec - (3 months ago) refactored:wip - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) py__RenameWithEditor.sublime-project 
~ (Modified) requirements.txt 
~ (Modified) src/main.py 
``` 
 
| 
| 
 
## Commit: 6a15a65 - (3 months ago) refactor - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) src/main.py 
``` 
 
| 
| 
 
## Commit: 3c27369 - (3 months ago) cleanup breadcrumbs - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) src/main.py 
``` 
 
| 
| 
 
## Commit: 7bdedce - (3 months ago) chk - DSK 
 
### Changed Files: 
 
```diff 
+ (Added) .cmd/py_venv_pip_devmode.bat 
+ (Added) .cmd/py_venv_pip_install.bat 
+ (Added) .cmd/py_venv_run_script.bat 
+ (Added) .cmd/py_venv_terminal.bat 
+ (Added) .cmd/py_venv_upgrade_requirements.bat 
+ (Added) .cmd/py_venv_write_requirements.bat 
+ (Added) .gitignore 
+ (Added) py__RenameWithEditor.sublime-project 
+ (Added) py_venv_init.bat 
+ (Added) requirements.txt 
+ (Added) src.md 
+ (Added) src/main.bat 
+ (Added) src/main.py 
+ (Added) versions/v1/FilesToText_01-Copy(2).py 
+ (Added) versions/v1/FilesToText_01-Copy(3).py 
+ (Added) versions/v1/FilesToText_01-Copy.py 
+ (Added) versions/v1/FilesToText_01.py 
+ (Added) versions/v1/FilesToText_01__filenames_NEW.txt 
+ (Added) versions/v1/FilesToText_01__filenames_ORG.txt 
+ (Added) versions/v1/FilesToText_02_2.py 
+ (Added) versions/v1/FilesToText_03_1_writefiles.py 
+ (Added) versions/v1/old/FilesToText_01.py 
+ (Added) versions/v1/old/FilesToText_02_subdirs.py 
+ (Added) versions/v1/old/list_directory_tree.py 
+ (Added) versions/v1/py__RenameWithTextEditor.md 
+ (Added) versions/v1/requirements.txt 
+ (Added) versions/v2/FilesToText__filenames_NEW.txt 
+ (Added) versions/v2/FilesToText__filenames_ORG.txt 
+ (Added) versions/v2/TESTFOLDER/Q3_2022_PrepareAForm.webm 
+ (Added) versions/v2/TESTFOLDER/TESTSUBDIR/GenTech_MultiDoc_de.webm 
+ (Added) versions/v2/TESTFOLDER/TESTSUBDIR/GenTech_MultiDoc_en.webm 
+ (Added) versions/v2/TESTFOLDER/TESTSUBDIR/GenTech_MultiDoc_fr.webm 
+ (Added) versions/v2/TESTFOLDER/TESTSUBDIR/GenTech_MultiDoc_jp.webm 
+ (Added) versions/v2/TESTFOLDER/TESTSUBDIR/GetMobileApp.webm 
+ (Added) versions/v2/TESTFOLDER/TESTSUBDIR/LeaveFeedbackDesktop-DARK.webm 
+ (Added) versions/v2/TESTFOLDER/TESTSUBDIR/NewIcons_LeaveFeedback-DARK.webm 
+ (Added) versions/v2/TESTFOLDER/TESTSUBDIR/NewIcons_LeaveFeedback2-LIGHT.webm 
+ (Added) versions/v2/TESTFOLDER/TESTSUBDIR/NewIcons_NoMatches-DARK.webm 
+ (Added) versions/v2/TESTFOLDER/TESTSUBDIR/NewIcons_StartConversation-DARK.webm 
+ (Added) versions/v2/TESTFOLDER/TESTSUBDIR/NewIcons_StartConversation-LIGHT.webm 
+ (Added) versions/v2/TESTFOLDER/TESTSUBDIR/NoMatchesDesktop-DARK.webm 
+ (Added) versions/v2/TESTFOLDER/x_SelectEditMenu.webm 
+ (Added) versions/v2/TESTFOLDER/x_SelectFinish.webm 
+ (Added) versions/v2/TESTFOLDER/x_SelectHighlightTool.webm 
+ (Added) versions/v2/TESTFOLDER/x_SelectSignMegaVerbShortTutorial.webm 
+ (Added) versions/v2/TESTFOLDER/x_SelectTextTool.webm 
+ (Added) versions/v2/TESTFOLDER/x____TESTFOLDER.md 
+ (Added) versions/v2/TESTFOLDER/xyx_SelectSignTool.webm 
+ (Added) versions/v2/TESTFOLDER_1/Q2_2024_ResizePages.webm 
+ (Added) versions/v2/example/FilesToText__filenames_ORG.txt 
+ (Added) versions/v2/example/exxxx_FilesToText__filenames_ORG.txt 
+ (Added) versions/v2/example/exxxx_example.md 
+ (Added) versions/v2/rename_map_new.txt 
+ (Added) versions/v2/rename_map_org.txt 
+ (Added) versions/v2/v2_1_a.py 
+ (Added) versions/v2/v2_2_a.py 
+ (Added) versions/v2/v2_2_b.py 
+ (Added) versions/v2/v2_2_c.py 
+ (Added) versions/v2/v2_2_d.py 
+ (Added) versions/v2/v2_3_a.py 
+ (Added) versions/v2/v2_4_a.py 
+ (Added) versions/v2/v2_4_b.py 
+ (Added) versions/v2/v2_4_c.py 
+ (Added) versions/v2/v2_4_d.py 
+ (Added) versions/v3/FilesToText__filenames_NEW.md 
+ (Added) versions/v3/FilesToText__filenames_NEW.py 
+ (Added) versions/v3/FilesToText__filenames_NEW.txt 
+ (Added) versions/v3/FilesToText__filenames_ORG.md 
+ (Added) versions/v3/FilesToText__filenames_ORG.py 
+ (Added) versions/v3/FilesToText__filenames_ORG.txt 
+ (Added) versions/v3/_teststructure/FilesToText__filenames_NEW.md 
+ (Added) versions/v3/_teststructure/FilesToText__filenames_ORG.md 
+ (Added) versions/v3/_teststructure/Q3_2022_PrepareAForm.webm 
+ (Added) versions/v3/_teststructure/SelectEditMenu.webm 
+ (Added) versions/v3/_teststructure/SelectFinish.webm 
+ (Added) versions/v3/_teststructure/SelectHighlightTool.webm 
+ (Added) versions/v3/_teststructure/SelectSignMegaVerbShortTutorial.webm 
+ (Added) versions/v3/_teststructure/SelectSignTool.webm 
+ (Added) versions/v3/_teststructure/SelectTextTool.webm 
+ (Added) versions/v3/_teststructure/_teststructure.md 
+ (Added) versions/v3/_teststructure/codebase.md 
+ (Added) versions/v3/_teststructure/gen_subdir1/GenTech_MultiDoc_de.webm 
+ (Added) versions/v3/_teststructure/gen_subdir1/GenTech_MultiDoc_jp.webm 
+ (Added) versions/v3/_teststructure/gen_subdir2/GenTech_MultiDoc_en.webm 
+ (Added) versions/v3/_teststructure/gen_subdir2/GetMobileApp.webm 
+ (Added) versions/v3/_teststructure/gen_subdir3/GenTech_MultiDoc_fr.webm 
+ (Added) versions/v3/_teststructure/subdir/LeaveFeedbackDesktop-DARK.webm 
+ (Added) versions/v3/_teststructure/subdir/New_Icons_LeaveFeedback-DARK.webm 
+ (Added) versions/v3/_teststructure/subdir/New_Icons_LeaveFeedback2-LIGHT.webm 
+ (Added) versions/v3/_teststructure/subdir/New_Icons_NoMatches-DARK.webm 
+ (Added) versions/v3/_teststructure/subdir/New_Icons_StartConversation-DARK.webm 
+ (Added) versions/v3/_teststructure/subdir/New_Icons_StartConversation-LIGHT.webm 
+ (Added) versions/v3/_teststructure/subdir/NoMatchesDesktop-DARK.webm 
+ (Added) versions/v3/v3_1_a.py 
+ (Added) versions/v3/v3_1_a_prompt.md 
+ (Added) versions/v3/v3_1_b.py 
+ (Added) versions/v3/v3_1_b_2.py 
+ (Added) versions/v3/v3_1_b_2_prompt.md 
+ (Added) versions/v3/v3_1_b_2_prompt_attachment_1.md 
+ (Added) versions/v3/v3_1_b_2_prompt_attachment_1_b.md 
+ (Added) versions/v3/v3_1_b_2_prompt_attachment_2.md 
+ (Added) versions/v3/v3_1_b_2_prompt_attachment_2_b.md 
+ (Added) versions/v3/v3_1_b_3.py 
+ (Added) versions/v3/v3_1_b_3_prompt.md 
+ (Added) versions/v3/v3_1_c.py 
+ (Added) versions/v3/v3_1_d.py 
+ (Added) versions/v3/v3_1_d_prompt.md 
+ (Added) versions/v3/v3_1_e.py 
+ (Added) versions/v3/v3_1_e_prompt.md 
+ (Added) versions/v3/v3_1_f.py 
+ (Added) versions/v3/v3_1_g.py 
+ (Added) versions/v3/v3_1_g_prompt.md 
+ (Added) versions/v3/v3_1_h.py 
+ (Added) versions/v3/v3_1_h_prompt.md 
+ (Added) versions/v3/v3_1_i.py 
+ (Added) versions/v4/_teststructure/GenTech_MultiDoc_de.webm 
+ (Added) versions/v4/_teststructure/GenTech_MultiDoc_en.webm 
+ (Added) versions/v4/_teststructure/GenTech_MultiDoc_fr.webm 
+ (Added) versions/v4/_teststructure/GenTech_MultiDoc_jp.webm 
+ (Added) versions/v4/_teststructure/GetMobileApp.webm 
+ (Added) versions/v4/_teststructure/LeaveFeedbackDesktop-DARK.webm 
+ (Added) versions/v4/_teststructure/NewIcons_LeaveFeedback-DARK.webm 
+ (Added) versions/v4/_teststructure/NewIcons_LeaveFeedback2-LIGHT.webm 
+ (Added) versions/v4/_teststructure/NewIcons_NoMatches-DARK.webm 
+ (Added) versions/v4/_teststructure/NewIcons_StartConversation-DARK.webm 
+ (Added) versions/v4/_teststructure/NewIcons_StartConversation-LIGHT.webm 
+ (Added) versions/v4/_teststructure/NoMatchesDesktop-DARK.webm 
+ (Added) versions/v4/_teststructure/Q3_2022_PrepareAForm.webm 
+ (Added) versions/v4/_teststructure/SelectEditMenu.webm 
+ (Added) versions/v4/_teststructure/SelectFinish.webm 
+ (Added) versions/v4/_teststructure/SelectHighlightTool.webm 
+ (Added) versions/v4/_teststructure/SelectSignMegaVerbShortTutorial.webm 
+ (Added) versions/v4/_teststructure/SelectSignTool.webm 
+ (Added) versions/v4/_teststructure/SelectTextTool.webm 
+ (Added) versions/v4/_teststructure/sub/FilesToText__filenames_NEW.md 
+ (Added) versions/v4/_teststructure/sub/FilesToText__filenames_ORG.md 
+ (Added) versions/v4/_teststructure/sub/_teststructure.md 
+ (Added) versions/v4/_teststructure/sub/codebase.md 
+ (Added) versions/v4/v4_1_a.py 
+ (Added) versions/v4/v4_1_a_prompt.md 
+ (Added) versions/v4/v4_1_b.py 
+ (Added) versions/v4/v4_1_b_2.py 
+ (Added) versions/v4/v4_1_b_2_prompt.md 
+ (Added) versions/v4/v4_2_a.py 
+ (Added) versions/v4/v4_2_a_prompt.md 
+ (Added) versions/v4/v4_2_b.py 
+ (Added) versions/v4/v4_2_c.py 
+ (Added) versions/v4/v4_2_d.py 
+ (Added) versions/v4/v4_2_d_2_prompt.md 
+ (Added) versions/v4/v4_2_d_prompt.md 
+ (Added) versions/v4/v4_2_e.py 
+ (Added) versions/v4/v4_2_e_2.py 
+ (Added) versions/v4/v4_3.py 
+ (Added) versions/v5/FilesToText__filenames_NEW.txt 
+ (Added) versions/v5/FilesToText__filenames_ORG.txt 
+ (Added) versions/v5/_teststructure/ddd/GenTech_MultiDoc_de.webm 
+ (Added) versions/v5/_teststructure/ddd/GenTech_MultiDoc_en.webm 
+ (Added) versions/v5/_teststructure/ddd/GenTech_MultiDoc_fr.webm 
+ (Added) versions/v5/_teststructure/ddd/GenTech_MultiDoc_jp.webm 
+ (Added) versions/v5/_teststructure/ddd/GetMobileApp.webm 
+ (Added) versions/v5/_teststructure/ddd/LeaveFeedbackDesktop-DARK.webm 
+ (Added) versions/v5/_teststructure/ddd/NewIcons_LeaveFeedback-DARK.webm 
+ (Added) versions/v5/_teststructure/ddd/NewIcons_LeaveFeedback2-LIGHT.webm 
+ (Added) versions/v5/_teststructure/ddd/NewIcons_NoMatches-DARK.webm 
+ (Added) versions/v5/_teststructure/dir/NewIcons_StartConversation-DARK.webm 
+ (Added) versions/v5/_teststructure/dir/NewIcons_StartConversation-LIGHT.webm 
+ (Added) versions/v5/_teststructure/dir/NoMatchesDesktop-DARK.webm 
+ (Added) versions/v5/_teststructure/dir/Q3_2022_PrepareAForm.webm 
+ (Added) versions/v5/_teststructure/xxx/dir/SelectEditMenu.webm 
+ (Added) versions/v5/_teststructure/xxx/dir/SelectFinish.webm 
+ (Added) versions/v5/_teststructure/xxx/dir/SelectHighlightTool.webm 
+ (Added) versions/v5/_teststructure/xxx/dir/SelectSignMegaVerbShortTutorial.webm 
+ (Added) versions/v5/_teststructure/xxx/dir/SelectSignTool.webm 
+ (Added) versions/v5/_teststructure/xxx/dir/SelectTextTool.webm 
+ (Added) versions/v5/_teststructure/xxx/dir/sub/FilesToText__filenames_NEW.md 
+ (Added) versions/v5/_teststructure/xxx/dir/sub/FilesToText__filenames_ORG.md 
+ (Added) versions/v5/_teststructure/xxx/dir/sub/_teststructure.md 
+ (Added) versions/v5/_teststructure/xxx/dir/sub/codebase.md 
+ (Added) versions/v5/v5_1_a.py 
+ (Added) versions/v5/v5_1_a_prompt.md 
+ (Added) versions/v5/v5_1_b.py 
+ (Added) versions/v5/v5_1_c.py 
+ (Added) versions/v5/v5_1_c_prompt.md 
+ (Added) versions/v5/v5_1_d.py 
+ (Added) versions/v5/v5_1_d_2.py 
+ (Added) versions/v5/v5_1_d_3.py 
+ (Added) versions/v5/v5_1_d_a.py 
+ (Added) versions/v5/v5_1_e.py 
+ (Added) versions/v5/v5_1_e_prompt.md 
+ (Added) versions/v5/v5_1_f.py 
+ (Added) versions/v5/v5_1_f_prompt.md 
+ (Added) versions/v5/v5_2_a.py 
+ (Added) versions/v5/v5_2_b.py 
+ (Added) versions/v5/v5_2_b_2.py 
+ (Added) versions/v5/v5_2_b_3.py 
+ (Added) versions/v5/v5_2_b_4.py 
+ (Added) versions/v5/v5_2_b_5.py 
+ (Added) versions/v5/v5_2_c.py 
+ (Added) versions/v5/v5_2_d.py 
+ (Added) versions/v5/v5_2_e.py 
+ (Added) versions/v5/v5_3_a.py 
+ (Added) versions/v5/v5_3_a_2.py 
+ (Added) versions/v5/v5_3_prompt.md 
+ (Added) versions/v5/v5_4_a_works.py 
+ (Added) versions/v6/BatchFileRenamer.zip 
+ (Added) versions/v6/v6_1/docs/development.md 
+ (Added) versions/v6/v6_1/docs/installation.md 
+ (Added) versions/v6/v6_1/docs/usage.md 
+ (Added) versions/v6/v6_1/src/__init__.py 
+ (Added) versions/v6/v6_1/src/__main__.py 
+ (Added) versions/v6/v6_1/src/core/directory_manager.py 
+ (Added) versions/v6/v6_1/src/core/file_processor.py 
+ (Added) versions/v6/v6_1/src/core/hash_manager.py 
+ (Added) versions/v6/v6_1/src/ui/cli.py 
+ (Added) versions/v6/v6_1/src/ui/editor.py 
+ (Added) versions/v6/v6_1/src/utils/config.py 
+ (Added) versions/v6/v6_1/src/utils/logging.py 
+ (Added) versions/v6/v6_1/src/windows/context_menu.py 
+ (Added) versions/v6/v6_1/test_files/renamed_renamed_renamed_file1.txt 
+ (Added) versions/v6/v6_1/test_files/renamed_renamed_renamed_file2.txt 
+ (Added) versions/v6/v6_1/test_files/subdir/renamed_renamed_renamed_file3.txt 
+ (Added) versions/v6/v6_1/tests/__init__.py 
+ (Added) versions/v6/v6_1/tests/test_directory_manager.py 
+ (Added) versions/v6/v6_1/tests/test_file_processor.py 
+ (Added) versions/v6/v6_1/tests/test_hash_manager.py 
+ (Added) versions/v6/v6_1/v6_1.md 
+ (Added) versions/v7/v7_1_a.py 
+ (Added) versions/v7/v7_1_a_prompt.md 
+ (Added) versions/v7/v7_1_b.py 
+ (Added) versions/v7/v7_1_b_2.py 
``` 
 
| 
| 
 
