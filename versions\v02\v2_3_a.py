# 'Claude 3.5 Sonnet'
# 'https://claude.ai/chat/a2aa89f1-f463-434e-9adf-bb9a18ef8351'

"""
py__RenameWithEditor

A utility for batch renaming files using a text editor as an intermediary.
Preserves file integrity using SHA256 hashes and provides a safe way to preview
and apply batch file renames.
"""

import hashlib
import logging
import os
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FileHasher:
    """Handles file hashing operations."""

    @staticmethod
    def calculate_sha256(file_path: Path) -> str:
        """
        Calculate SHA256 hash of a file.

        Args:
            file_path: Path to the file

        Returns:
            str: Hexadecimal string of the file's SHA256 hash

        Raises:
            IOError: If file cannot be read
        """
        sha256 = hashlib.sha256()
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            raise

class FileScanner:
    """Handles directory scanning and file discovery."""

    def __init__(self, root_dir: Path):
        self.root_dir = Path(root_dir)

    def scan_directory(self, include_subdirs: bool = True) -> List[Tuple[str, Path]]:
        """
        Scan directory and return list of file hashes and paths.

        Args:
            include_subdirs: Whether to scan subdirectories

        Returns:
            List of tuples containing (file_hash, relative_path)
        """
        file_info = []
        try:
            if include_subdirs:
                for path in self.root_dir.rglob('*'):
                    if self._should_process_file(path):
                        file_info.append(self._process_file(path))
            else:
                for path in self.root_dir.iterdir():
                    if self._should_process_file(path):
                        file_info.append(self._process_file(path))
            return [info for info in file_info if info is not None]
        except Exception as e:
            logger.error(f"Error scanning directory {self.root_dir}: {e}")
            raise

    def _should_process_file(self, path: Path) -> bool:
        """Determine if a file should be processed."""
        return path.is_file() and not path.name.startswith('.')

    def _process_file(self, path: Path) -> Optional[Tuple[str, Path]]:
        """Process a single file and return its hash and relative path."""
        try:
            if not os.access(path, os.R_OK):
                logger.warning(f"Cannot access file {path}")
                return None

            file_hash = FileHasher.calculate_sha256(path)
            rel_path = path.relative_to(self.root_dir)
            return (file_hash, rel_path)
        except Exception as e:
            logger.error(f"Error processing file {path}: {e}")
            return None

class FileRenamer:
    """Handles file renaming operations."""

    def __init__(self, work_dir: Path):
        self.work_dir = Path(work_dir)
        self.org_file = self.work_dir / 'filenames_original.txt'
        self.new_file = self.work_dir / 'filenames_new.txt'

    def write_file_lists(self, file_info: List[Tuple[str, Path]]) -> None:
        """Write original and new filename lists."""
        content = '\n'.join(f"{hash}|{path}" for hash, path in file_info)
        try:
            self.org_file.write_text(content)
            self.new_file.write_text(content)
            logger.info(f"Written file lists to {self.org_file} and {self.new_file}")
        except IOError as e:
            logger.error(f"Failed to write file lists: {e}")
            raise

    def read_new_filenames(self) -> Dict[str, Path]:
        """Read and parse the new filenames file."""
        try:
            content = self.new_file.read_text().strip()
            if not content:
                raise ValueError("New filenames file is empty")

            result = {}
            for line in content.split('\n'):
                hash_val, path_str = line.strip().split('|', 1)
                result[hash_val] = Path(path_str)
            return result
        except Exception as e:
            logger.error(f"Failed to read new filenames: {e}")
            raise

    def rename_files(self, file_info: List[Tuple[str, Path]], new_names: Dict[str, Path]) -> None:
        """
        Rename files according to the new names.

        Args:
            file_info: List of (hash, current_path) tuples
            new_names: Dictionary mapping hashes to new paths
        """
        processed_hashes: Set[str] = set()

        for file_hash, current_path in file_info:
            if file_hash not in new_names:
                logger.warning(f"No new name found for {current_path}")
                continue

            if file_hash in processed_hashes:
                logger.warning(f"Duplicate hash found for {current_path}")
                continue

            new_path = new_names[file_hash]
            if current_path == new_path:
                continue

            try:
                full_current = self.work_dir / current_path
                full_new = self.work_dir / new_path

                # Create parent directories if they don't exist
                full_new.parent.mkdir(parents=True, exist_ok=True)

                # Perform the rename
                full_current.rename(full_new)
                logger.info(f"Renamed {current_path} to {new_path}")
                processed_hashes.add(file_hash)

            except Exception as e:
                logger.error(f"Failed to rename {current_path} to {new_path}: {e}")

class RenameWithEditor:
    """Main class coordinating the file renaming process."""

    def __init__(self, directory: Path):
        self.directory = Path(directory)
        self.scanner = FileScanner(self.directory)
        self.renamer = FileRenamer(self.directory)

    def prepare_rename(self, include_subdirs: bool = True) -> None:
        """Scan directory and create filename lists."""
        logger.info(f"Scanning directory: {self.directory}")
        file_info = self.scanner.scan_directory(include_subdirs)
        self.renamer.write_file_lists(file_info)
        logger.info("Prepared files for renaming. Edit filenames_new.txt to specify new names.")

    def apply_rename(self) -> None:
        """Apply the rename operations from the edited file."""
        logger.info("Reading new filenames and applying changes...")
        file_info = self.scanner.scan_directory()
        new_names = self.renamer.read_new_filenames()
        self.renamer.rename_files(file_info, new_names)
        logger.info("Finished renaming files")

def main():
    """Main entry point for the script."""
    import argparse

    parser = argparse.ArgumentParser(description="Batch rename files using a text editor")
    parser.add_argument('directory', type=str, help="Directory containing files to rename")
    parser.add_argument('--prepare', action='store_true', help="Prepare files for renaming")
    parser.add_argument('--apply', action='store_true', help="Apply rename changes")
    parser.add_argument('--no-subdirs', action='store_true', help="Don't include subdirectories")

    args = parser.parse_args()

    try:
        renamer = RenameWithEditor(Path(args.directory))

        if args.prepare:
            renamer.prepare_rename(not args.no_subdirs)
        elif args.apply:
            renamer.apply_rename()
        else:
            parser.print_help()

    except Exception as e:
        logger.error(f"Operation failed: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
