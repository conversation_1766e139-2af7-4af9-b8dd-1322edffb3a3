import os
import sys
import winreg
from typing import Optional

from src.utils.logging import Logger, LogLevel

class WindowsContextMenu:
    """Manages Windows Explorer context menu integration."""

    def __init__(self, logger: Logger):
        self.logger = logger
        self.app_name = "FileRenamer"
        self.command_key = "Software\\Classes\\Directory\\shell\\" + self.app_name

    def install(self, executable_path: str) -> bool:
        """Installs the context menu entry."""
        if not sys.platform.startswith('win'):
            self.logger.log(
                "Context menu integration only supported on Windows",
                LogLevel.ERROR
            )
            return False

        try:
            # Create main menu item
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER, self.command_key) as key:
                winreg.SetValue(key, "", winreg.REG_SZ, "Batch Rename Files")
                winreg.SetValueEx(
                    key,
                    "Icon",
                    0,
                    winreg.REG_SZ,
                    executable_path
                )

            # Create command
            command_path = os.path.join(self.command_key, "command")
            with winreg.Create<PERSON>ey(winreg.HKEY_CURRENT_USER, command_path) as key:
                command = f'"{executable_path}" rename "%V"'
                winreg.SetValue(key, "", winreg.REG_SZ, command)

            self.logger.log(
                "Context menu integration installed successfully",
                LogLevel.CHANGE
            )
            return True

        except WindowsError as error:
            self.logger.log(
                f"Failed to install context menu: {error}",
                LogLevel.ERROR
            )
            return False

    def uninstall(self) -> bool:
        """Removes the context menu entry."""
        if not sys.platform.startswith('win'):
            return False

        try:
            self._delete_registry_key(self.command_key)
            self.logger.log(
                "Context menu integration removed successfully",
                LogLevel.CHANGE
            )
            return True

        except WindowsError as error:
            self.logger.log(
                f"Failed to remove context menu: {error}",
                LogLevel.ERROR
            )
            return False

    def _delete_registry_key(self, key_path: str) -> None:
        """Recursively deletes a registry key and all its subkeys."""
        try:
            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                key_path,
                0,
                winreg.KEY_ALL_ACCESS
            ) as key:
                while True:
                    try:
                        sub_key = winreg.EnumKey(key, 0)
                        self._delete_registry_key(os.path.join(key_path, sub_key))
                    except OSError:
                        break

            winreg.DeleteKey(winreg.HKEY_CURRENT_USER, key_path)
        except WindowsError:
            pass

    def is_installed(self) -> bool:
        """Checks if the context menu entry is installed."""
        try:
            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                self.command_key,
                0,
                winreg.KEY_READ
            ):
                return True
        except WindowsError:
            return False
