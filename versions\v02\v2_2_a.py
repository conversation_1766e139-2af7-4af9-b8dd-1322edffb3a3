# 'ChatGPT o1-mini'
# 'https://chatgpt.com/c/6742dda3-d600-8008-bc70-b303a82d1588'

"""
py__RenameWithEditor

|- Hash Generation: Compute SHA256 hashes for files in a directory and its subdirectories.
|- Hash Recording: Store hashes along with filenames in organized text files.
|- File Renaming: Rename files based on hash matching between original and new hash lists.
|- Directory Visualization: Display a visually appealing directory tree using the Rich library.
|- Command-Line Interface: Easy-to-use CLI for performing various operations.
"""

import argparse
import os
from pathlib import Path

from file_manager import (
    get_file_hashes,
    write_hashes_to_file,
    rename_files,
)
from directory_visualizer import display_directory_tree

def parse_arguments() -> argparse.Namespace:
    """
    Parse command-line arguments.

    Returns:
        argparse.Namespace: Parsed arguments.
    """
    parser = argparse.ArgumentParser(
        description="File Manager: Hashing and Renaming Utility"
    )

    subparsers = parser.add_subparsers(dest="command", required=True, help="Sub-commands")

    # Sub-command: hash
    hash_parser = subparsers.add_parser("hash", help="Generate and write file hashes.")
    hash_parser.add_argument(
        "input_directory",
        type=str,
        help="Directory to process.",
    )
    hash_parser.add_argument(
        "--include-subdirectories",
        action="store_true",
        help="Include subdirectories in processing.",
    )
    hash_parser.add_argument(
        "--original-output",
        type=str,
        default="FilesToText__filenames_ORG.txt",
        help="Output file for original filenames and hashes.",
    )
    hash_parser.add_argument(
        "--new-output",
        type=str,
        default="FilesToText__filenames_NEW.txt",
        help="Output file for new filenames and hashes.",
    )

    # Sub-command: rename
    rename_parser = subparsers.add_parser("rename", help="Rename files based on hash matching.")
    rename_parser.add_argument(
        "input_directory",
        type=str,
        help="Directory containing files to rename.",
    )
    rename_parser.add_argument(
        "original_hash_file",
        type=str,
        help="Path to the original hashes file.",
    )
    rename_parser.add_argument(
        "new_hash_file",
        type=str,
        help="Path to the new hashes file.",
    )

    # Sub-command: visualize
    visualize_parser = subparsers.add_parser("visualize", help="Visualize directory tree.")
    visualize_parser.add_argument(
        "directory",
        type=str,
        help="Directory to visualize.",
    )

    return parser.parse_args()

def main():
    args = parse_arguments()

    if args.command == "hash":
        input_dir = os.path.abspath(args.input_directory)
        original_output = os.path.abspath(args.original_output)
        new_output = os.path.abspath(args.new_output)

        if not Path(input_dir).exists():
            print(f"[red]Error: The directory '{input_dir}' does not exist.[/red]")
            return

        print(f"Generating hashes for directory: {input_dir}")
        file_hashes = get_file_hashes(input_dir, include_subdirectories=args.include_subdirectories)
        write_hashes_to_file(file_hashes, original_output)
        write_hashes_to_file(file_hashes, new_output)
        print("Hash generation completed.")

    elif args.command == "rename":
        input_dir = os.path.abspath(args.input_directory)
        original_hash_file = os.path.abspath(args.original_hash_file)
        new_hash_file = os.path.abspath(args.new_hash_file)

        if not Path(input_dir).exists():
            print(f"[red]Error: The directory '{input_dir}' does not exist.[/red]")
            return
        if not Path(original_hash_file).is_file():
            print(f"[red]Error: The file '{original_hash_file}' does not exist.[/red]")
            return
        if not Path(new_hash_file).is_file():
            print(f"[red]Error: The file '{new_hash_file}' does not exist.[/red]")
            return

        print(f"Renaming files in directory: {input_dir}")
        rename_files(input_dir, original_hash_file, new_hash_file)
        print("File renaming completed.")

    elif args.command == "visualize":
        directory = os.path.abspath(args.directory)
        display_directory_tree(directory)

if __name__ == "__main__":
    main()
