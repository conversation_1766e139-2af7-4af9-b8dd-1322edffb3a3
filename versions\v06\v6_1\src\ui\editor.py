import os
import pathlib
import subprocess
import sys
import tempfile
from typing import List, Optional

from src.utils.logging import Logger, LogLevel

class EditorManager:
    """Manages text editor integration for rename operations."""

    def __init__(self, logger: Logger):
        self.logger = logger

    def edit_rename_plan(self, files: List[pathlib.Path]) -> bool:
        """Creates and opens a rename plan in the default text editor."""
        editor = self._get_default_editor()
        if not editor:
            return False

        with tempfile.NamedTemporaryFile(
            mode='w+',
            suffix='.txt',
            delete=False,
            encoding='utf-8'
        ) as temp_file:
            try:
                self._write_rename_plan(temp_file, files)
                temp_file.flush()
                
                if not self._open_editor(editor, temp_file.name):
                    return False

                return self._read_rename_plan(temp_file.name)
            finally:
                try:
                    os.unlink(temp_file.name)
                except OSError:
                    pass

    def _get_default_editor(self) -> Optional[str]:
        """Returns a simple file handling approach for Replit environment."""
        return "simple"

    def _command_exists(self, cmd: str) -> bool:
        """Checks if a command exists in the system path."""
        return any(
            os.access(os.path.join(path, cmd), os.X_OK)
            for path in os.environ["PATH"].split(os.pathsep)
            if os.path.exists(path)
        )

    def _open_editor(self, editor: str, file_path: str) -> bool:
        """Handles file editing based on editor type."""
        if editor == "simple":
            return True
        return False

    def _write_rename_plan(self, file, files: List[pathlib.Path]) -> None:
        """Writes the initial rename plan to a file."""
        file.write("# File Rename Plan\n")
        file.write("# Format: <current_path> -> <new_path>\n")
        file.write("# Lines starting with # are ignored\n\n")

        for path in sorted(files):
            file.write(f"{path} -> {path}\n")

    def _read_rename_plan(self, file_path: str) -> bool:
        """Creates a simple rename plan for demonstration."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f if line.strip() and not line.startswith('#')]
            
            # In simple mode, we'll just append a prefix to the files
            base_dir = pathlib.Path(file_path).parent
            for line in lines:
                source = pathlib.Path(line.split(' -> ')[0])
                if source.exists():
                    new_name = f"renamed_{source.name}"
                    with open(file_path, 'a', encoding='utf-8') as f:
                        f.write(f"{source} -> {source.parent / new_name}\n")
            
            return True
        except IOError as error:
            self.logger.log(f"Failed to read/write rename plan: {error}", LogLevel.ERROR)
            return False
