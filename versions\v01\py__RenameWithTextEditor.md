# Project Files Documentation for `py__RenameWithTextEditor`

### File Structure

```
├── FilesToText_01 - Copy (3).py
├── FilesToText_01 - Copy.py
├── FilesToText_01.py
├── FilesToText_01__filenames_NEW.txt
├── FilesToText_01__filenames_ORG.txt
├── FilesToText_03_1_writefiles.py
├── main.py
└── old
│   ├── FilesToText_01.py
│   ├── list_directory_tree.py
├── requirements.txt
```
### 1. `FilesToText_01 - Copy (3).py`

#### `FilesToText_01 - Copy (3).py`

```python
import hashlib
import os
import time
import hashlib
import time
import os
import hashlib
import os
import time

def sha256_checksum(file_path):
    """Compute the SHA256 hash of a file."""
    sha256 = hashlib.sha256()
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b''):
            sha256.update(chunk)
    return sha256.hexdigest()

def get_file_hashes(input_directory, relative_path=''):
    """Get the SHA256 hash and filename for each file in a directory and its subdirectories."""
    file_hashes = []
    for entry in os.listdir(input_directory):
        entry_path = os.path.join(input_directory, entry)
        entry_relative_path = os.path.join(relative_path, entry)
        if os.path.isfile(entry_path) and os.access(entry_path, os.R_OK):
            file_hash = sha256_checksum(entry_path)
            file_hashes.append((file_hash, entry_relative_path))
            print(entry_relative_path)
        elif os.path.isdir(entry_path):
            file_hashes.extend(get_file_hashes(entry_path, entry_relative_path))
    return file_hashes

def write_file_hashes(input_directory, org_file, new_file):
    """Write the SHA256 hashes and filenames to two text files."""
    file_hashes = get_file_hashes(input_directory)
    formatted_hashes = [f"{h[0]}|{h[1]}" for h in file_hashes]
    with open(org_file, "w") as f:
        f.write("\n".join(formatted_hashes))
    with open(new_file, "w") as f:
        f.write("\n".join(formatted_hashes))



def rename_files(input_directory, out_file_org, out_file_new):
    with open(out_file_new, "r") as f:
        new_filenames = [line.strip() for line in f.readlines()]

    new_filenames_hash = []
    new_filenames_name = []
    for new in new_filenames:
        split_line = new.split('|')
        new_filenames_hash.append(split_line[0])
        new_filenames_name.append(split_line[1])

    filenames_with_hash = get_file_hashes(input_directory)

    for item in filenames_with_hash:
        matching_indices = []
        for index, new_filename_hash in enumerate(new_filenames_hash):
            if item[0] == new_filename_hash:
                matching_indices.append(index)

        if matching_indices:
            first_matching_index = matching_indices[0]
            print(first_matching_index)
            print('org: %s' % (item[1]))
            print('new: %s' % (new_filenames_name[first_matching_index]))
            print('\n')
            file_path_org = os.path.join(input_directory, item[1])
            file_path_new = os.path.join(input_directory, new_filenames_name[first_matching_index])
            os.rename(file_path_org, file_path_new)
    else:
        print("The number of filenames in the original and new lists do not match.")


initial_directory = os.getcwd()
# input_directory = os.path.join(initial_directory, '.VENV', 'VENV_Utils_Batch')
input_directory = os.path.join(initial_directory, '.VENV')

# Write the current filenames to the original_filenames file
out_textfile_org = os.path.join(initial_directory, 'FilesToText_01__filenames_ORG.txt')
out_textfile_new = os.path.join(initial_directory, 'FilesToText_01__filenames_NEW.txt')
write_file_hashes(input_directory, out_textfile_org, out_textfile_new)

# Rename files based on the new_filenames file
# rename_files(input_directory, out_textfile_org, out_textfile_new)
```
### 2. `FilesToText_01 - Copy.py`

#### `FilesToText_01 - Copy.py`

```python
import hashlib
import os
import time
import win32gui


def sha256_checksum(file_path):
    sha256 = hashlib.sha256()
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b''):
            sha256.update(chunk)
    return sha256.hexdigest()


def get_hashed_file_list(directory):
    # variable for the result
    filenames_with_hash = []
    # get hash and filename for each file in the directory
    for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)
        if os.path.isfile(file_path) and os.access(file_path, os.R_OK):
            file_hash = sha256_checksum(os.path.join(directory, filename))
            filenames_with_hash.append([file_hash, filename])
    # return lists
    return filenames_with_hash


def write_current_filenames(input_directory, out_file_org, out_file_new):
    filenames_with_hash = get_hashed_file_list(input_directory)
    formatted_filenames = []

    # get files/hashes
    for item in filenames_with_hash:
        formatted_filenames.append(f"{item[0]}|{item[1]}")
        print(f"{item[0]}|{item[1]}")
    # write text
    with open(out_file_org, "w") as f:
        for filename in formatted_filenames:
            f.write(f"{filename}\n")
    # write text
    with open(out_file_new, "w") as f:
        for filename in formatted_filenames:
            f.write(f"{filename}\n")


def rename_files(input_directory, out_file_org, out_file_new):
    with open(out_file_new, "r") as f:
        new_filenames = [line.strip() for line in f.readlines()]

    new_filenames_hash = []
    new_filenames_name = []
    for new in new_filenames:
        split_line = new.split('|')
        new_filenames_hash.append(split_line[0])
        new_filenames_name.append(split_line[1])

    filenames_with_hash = get_hashed_file_list(input_directory)

    for item in filenames_with_hash:
        matching_indices = []
        for index, new_filename_hash in enumerate(new_filenames_hash):
            if item[0] == new_filename_hash:
                matching_indices.append(index)

        if matching_indices:
            first_matching_index = matching_indices[0]
            print(first_matching_index)
            print('org: %s' % (item[1]))
            print('new: %s' % (new_filenames_name[first_matching_index]))
            print('\n')
            file_path_org = os.path.join(input_directory, item[1])
            file_path_new = os.path.join(input_directory, new_filenames_name[first_matching_index])
            os.rename(file_path_org, file_path_new)
    else:
        print("The number of filenames in the original and new lists do not match.")


initial_directory = os.getcwd()
input_directory = os.path.join(initial_directory, '.VENV', 'VENV_Utils_Batch')

# Write the current filenames to the original_filenames file
out_textfile_org = os.path.join(initial_directory, 'FilesToText_01__filenames_ORG.txt')
out_textfile_new = os.path.join(initial_directory, 'FilesToText_01__filenames_NEW.txt')
write_current_filenames(input_directory, out_textfile_org, out_textfile_new)

# Rename files based on the new_filenames file
rename_files(input_directory, out_textfile_org, out_textfile_new)
```
### 3. `FilesToText_01.py`

#### `FilesToText_01.py`

```python
import hashlib
import os
import time
import hashlib
import time
import os

def sha256_checksum(file_path):
    """Compute the SHA256 hash of a file."""
    sha256 = hashlib.sha256()
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b''):
            sha256.update(chunk)
    return sha256.hexdigest()

def get_file_hashes(input_directory):
    """Get the SHA256 hash and filename for each file in a directory."""
    file_hashes = []
    for filename in os.listdir(input_directory):
        file_path = os.path.join(input_directory, filename)
        if os.path.isfile(file_path) and os.access(file_path, os.R_OK):
            file_hash = sha256_checksum(file_path)
            file_hashes.append((file_hash, filename))
    return file_hashes

def write_file_hashes(input_directory, org_file, new_file):
    """Write the SHA256 hashes and filenames to two text files."""
    file_hashes = get_file_hashes(input_directory)
    formatted_hashes = [f"{h[0]}|{h[1]}" for h in file_hashes]
    with open(org_file, "w") as f:
        f.write("\n".join(formatted_hashes))
    with open(new_file, "w") as f:
        f.write("\n".join(formatted_hashes))


def rename_files(input_directory, out_file_org, out_file_new):
    with open(out_file_new, "r") as f:
        new_filenames = [line.strip() for line in f.readlines()]

    new_filenames_hash = []
    new_filenames_name = []
    for new in new_filenames:
        split_line = new.split('|')
        new_filenames_hash.append(split_line[0])
        new_filenames_name.append(split_line[1])

    filenames_with_hash = get_file_hashes(input_directory)

    for item in filenames_with_hash:
        matching_indices = []
        for index, new_filename_hash in enumerate(new_filenames_hash):
            if item[0] == new_filename_hash:
                matching_indices.append(index)

        if matching_indices:
            first_matching_index = matching_indices[0]
            print(first_matching_index)
            print('org: %s' % (item[1]))
            print('new: %s' % (new_filenames_name[first_matching_index]))
            print('\n')
            file_path_org = os.path.join(input_directory, item[1])
            file_path_new = os.path.join(input_directory, new_filenames_name[first_matching_index])
            os.rename(file_path_org, file_path_new)
    else:
        print("The number of filenames in the original and new lists do not match.")


initial_directory = os.getcwd()
input_directory = os.path.join(initial_directory, '.VENV', 'VENV_Utils_Batch')

# Write the current filenames to the original_filenames file
out_textfile_org = os.path.join(initial_directory, 'FilesToText_01__filenames_ORG.txt')
out_textfile_new = os.path.join(initial_directory, 'FilesToText_01__filenames_NEW.txt')
write_file_hashes(input_directory, out_textfile_org, out_textfile_new)

# Rename files based on the new_filenames file
# rename_files(input_directory, out_textfile_org, out_textfile_new)
```
### 4. `FilesToText_01__filenames_NEW.txt`

#### `FilesToText_01__filenames_NEW.txt`

```text
9707220740b3b3e70e6d95ba3012bfbbda052fd41103db950714146666ec1965|FilesToText_01 - Copy (2).py
d4388d5983f6c7a5221b4363022237b736ea61277a8ae448a81d02953e5cac09|FilesToText_01 - Copy (3).py
73314d55eaf82726c92815f609e662f6ec8752d8df8ef147245022bb305c4bef|FilesToText_01 - Copy.py
9707220740b3b3e70e6d95ba3012bfbbda052fd41103db950714146666ec1965|FilesToText_01.py
e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855|FilesToText_01__filenames_NEW.txt
e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855|FilesToText_01__filenames_ORG.txt
6659b444e24f5a5ee26c1287e5e87b54eaaa9627765780fe8c0e2cc1dd2b5a35|FilesToText_02.bat
9707220740b3b3e70e6d95ba3012bfbbda052fd41103db950714146666ec1965|FilesToText_02_2.py
d4388d5983f6c7a5221b4363022237b736ea61277a8ae448a81d02953e5cac09|FilesToText_03.py
c482837d8c4050cd6003c2cdb9c8164579c13e49f9dbbe2c4e70536d05cd5c43|Install or Remove Package.bat
37df2ec64ced3d091a5244bdb88272f63bcdb98005e9e3bed08b7cbcf9afb893|RenameFiles_With_TextEditor_Initialize.bat
5f8ad63ad980ffa0588d694cff7dc8bae935393f6890e5cad894c0dc4ce2d685|requirements.txt
```
### 5. `FilesToText_01__filenames_ORG.txt`

#### `FilesToText_01__filenames_ORG.txt`

```text
9707220740b3b3e70e6d95ba3012bfbbda052fd41103db950714146666ec1965|FilesToText_01 - Copy (2).py
d4388d5983f6c7a5221b4363022237b736ea61277a8ae448a81d02953e5cac09|FilesToText_01 - Copy (3).py
73314d55eaf82726c92815f609e662f6ec8752d8df8ef147245022bb305c4bef|FilesToText_01 - Copy.py
9707220740b3b3e70e6d95ba3012bfbbda052fd41103db950714146666ec1965|FilesToText_01.py
e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855|FilesToText_01__filenames_NEW.txt
e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855|FilesToText_01__filenames_ORG.txt
6659b444e24f5a5ee26c1287e5e87b54eaaa9627765780fe8c0e2cc1dd2b5a35|FilesToText_02.bat
9707220740b3b3e70e6d95ba3012bfbbda052fd41103db950714146666ec1965|FilesToText_02_2.py
d4388d5983f6c7a5221b4363022237b736ea61277a8ae448a81d02953e5cac09|FilesToText_03.py
c482837d8c4050cd6003c2cdb9c8164579c13e49f9dbbe2c4e70536d05cd5c43|Install or Remove Package.bat
37df2ec64ced3d091a5244bdb88272f63bcdb98005e9e3bed08b7cbcf9afb893|RenameFiles_With_TextEditor_Initialize.bat
5f8ad63ad980ffa0588d694cff7dc8bae935393f6890e5cad894c0dc4ce2d685|requirements.txt
```
### 6. `FilesToText_03_1_writefiles.py`

#### `FilesToText_03_1_writefiles.py`

```python
import hashlib
import os
import time
import hashlib
import time
import os
import hashlib
import os
import time

def sha256_checksum(file_path):
    """Compute the SHA256 hash of a file."""
    sha256 = hashlib.sha256()
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b''):
            sha256.update(chunk)
    return sha256.hexdigest()

def get_file_hashes(input_directory, relative_path=''):
    """Get the SHA256 hash and filename for each file in a directory and its subdirectories."""
    file_hashes = []
    for entry in os.listdir(input_directory):
        entry_path = os.path.join(input_directory, entry)
        entry_relative_path = os.path.join(relative_path, entry)
        if os.path.isfile(entry_path) and os.access(entry_path, os.R_OK):
            file_hash = sha256_checksum(entry_path)
            file_hashes.append((file_hash, entry_relative_path))
            print(entry_relative_path)
        elif os.path.isdir(entry_path):
            file_hashes.extend(get_file_hashes(entry_path, entry_relative_path))
    return file_hashes

def write_file_hashes(input_directory, org_file, new_file):
    """Write the SHA256 hashes and filenames to two text files."""
    file_hashes = get_file_hashes(input_directory)
    formatted_hashes = [f"{h[0]}|{h[1]}" for h in file_hashes]
    with open(org_file, "w") as f:
        f.write("\n".join(formatted_hashes))
    with open(new_file, "w") as f:
        f.write("\n".join(formatted_hashes))



def rename_files(input_directory, out_file_org, out_file_new):
    with open(out_file_new, "r") as f:
        new_filenames = [line.strip() for line in f.readlines()]

    new_filenames_hash = []
    new_filenames_name = []
    for new in new_filenames:
        split_line = new.split('|')
        new_filenames_hash.append(split_line[0])
        new_filenames_name.append(split_line[1])

    filenames_with_hash = get_file_hashes(input_directory)

    for item in filenames_with_hash:
        matching_indices = []
        for index, new_filename_hash in enumerate(new_filenames_hash):
            if item[0] == new_filename_hash:
                matching_indices.append(index)

        if matching_indices:
            first_matching_index = matching_indices[0]
            print(first_matching_index)
            print('org: %s' % (item[1]))
            print('new: %s' % (new_filenames_name[first_matching_index]))
            print('\n')
            file_path_org = os.path.join(input_directory, item[1])
            file_path_new = os.path.join(input_directory, new_filenames_name[first_matching_index])
            os.rename(file_path_org, file_path_new)
    else:
        print("The number of filenames in the original and new lists do not match.")


initial_directory = os.getcwd()
# input_directory = os.path.join(initial_directory, '.VENV', 'VENV_Utils_Batch')
input_directory = os.path.join(initial_directory, 'EMPTY_TEST_DIR')

# Write the current filenames to the original_filenames file
out_textfile_org = os.path.join(initial_directory, 'FilesToText_01__filenames_ORG.txt')
out_textfile_new = os.path.join(initial_directory, 'FilesToText_01__filenames_NEW.txt')
write_file_hashes(input_directory, out_textfile_org, out_textfile_new)

# Rename files based on the new_filenames file
# rename_files(input_directory, out_textfile_org, out_textfile_new)
```
### 7. `main.py`

#### `main.py`

```python
if __name__ == "__main__":
    print("Hello, World!")
```
### 8. `old\FilesToText_01.py`

#### `old\FilesToText_01.py`

```python
import hashlib
import os
import time
import hashlib
import time
import os
import hashlib
import os
import time

def sha256_checksum(file_path):
    """Compute the SHA256 hash of a file."""
    sha256 = hashlib.sha256()
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b''):
            sha256.update(chunk)
    return sha256.hexdigest()

def get_file_hashes(input_directory, relative_path='', include_paths=True, search_subfolders=True):
    """Get the SHA256 hash, relative path (optional), and filename for each file in a directory and its subdirectories."""
    file_hashes = []
    print(f"Entering directory: {input_directory}")  # Debug print
    for entry in os.listdir(input_directory):
        entry_path = os.path.join(input_directory, entry)
        if os.path.isfile(entry_path) and os.access(entry_path, os.R_OK):
            print(f"Processing file: {entry_path}")  # Debug print
            file_hash = sha256_checksum(entry_path)
            if include_paths:
                file_hashes.append((file_hash, relative_path, entry))
                print(os.path.join(relative_path, entry))
            else:
                file_hashes.append((file_hash, entry))
                print(entry)
        elif os.path.isdir(entry_path) and search_subfolders and not os.path.islink(entry_path):
            new_relative_path = os.path.join(relative_path, entry)
            file_hashes.extend(get_file_hashes(entry_path, new_relative_path, include_paths, search_subfolders))
    return file_hashes



def write_file_hashes(input_directory, org_file, new_file, include_paths=True, search_subfolders=True):
    """Write the SHA256 hashes, relative paths (optional), and filenames to two text files."""
    file_hashes = get_file_hashes(input_directory, include_paths=include_paths, search_subfolders=search_subfolders)
    if include_paths:
        formatted_hashes = [f"{h[0]}|{h[1]}|{h[2]}" for h in file_hashes]
    else:
        formatted_hashes = [f"{h[0]}|{h[1]}" for h in file_hashes]
    with open(org_file, "w") as f:
        f.write("\n".join(formatted_hashes))
    with open(new_file, "w") as f:
        f.write("\n".join(formatted_hashes))






def rename_files(input_directory, out_file_org, out_file_new):
    with open(out_file_new, "r") as f:
        new_filenames = [line.strip() for line in f.readlines()]

    new_filenames_hash = []
    new_filenames_name = []
    for new in new_filenames:
        split_line = new.split('|')
        new_filenames_hash.append(split_line[0])
        new_filenames_name.append(split_line[1])

    filenames_with_hash = get_file_hashes(input_directory)

    for item in filenames_with_hash:
        matching_indices = []
        for index, new_filename_hash in enumerate(new_filenames_hash):
            if item[0] == new_filename_hash:
                matching_indices.append(index)

        if matching_indices:
            first_matching_index = matching_indices[0]
            print(first_matching_index)
            print('org: %s' % (item[1]))
            print('new: %s' % (new_filenames_name[first_matching_index]))
            print('\n')
            file_path_org = os.path.join(input_directory, item[1])
            file_path_new = os.path.join(input_directory, new_filenames_name[first_matching_index])
            os.rename(file_path_org, file_path_new)
    else:
        print("The number of filenames in the original and new lists do not match.")


initial_directory = os.getcwd()
# input_directory = os.path.join(initial_directory, '.VENV', 'VENV_Utils_Batch')
input_directory = os.path.join(initial_directory, '.VENV')

# Write the current filenames to the original_filenames file
out_textfile_org = os.path.join(initial_directory, 'FilesToText_01__filenames_ORG.txt')
out_textfile_new = os.path.join(initial_directory, 'FilesToText_01__filenames_NEW.txt')
write_file_hashes(input_directory, out_textfile_org, out_textfile_new, True, False)

# Rename files based on the new_filenames file
# rename_files(input_directory, out_textfile_org, out_textfile_new)
```
### 9. `old\list_directory_tree.py`

#### `old\list_directory_tree.py`

```python
"""
list_directory_tree.py

Displays a tree of files/directories using the Tree renderable from the Rich library.
"""

import os
import pathlib
import sys

from rich import print
from rich.filesize import decimal
from rich.markup import escape
from rich.text import Text
from rich.tree import Tree


def create_file_text(path: pathlib.Path) -> Text:
    """Creates a formatted Text object for a file."""
    text_filename = Text(path.name, "green")
    text_filename.highlight_regex(r"\..*$", "bold red")
    text_filename.stylize(f"link file://{path}")
    file_size = path.stat().st_size
    text_filename.append(f" ({decimal(file_size)})", "blue")
    icon = "🐍 " if path.suffix == ".py" else "📄 "
    return Text(icon) + text_filename


def walk_directory(directory: pathlib.Path, tree: Tree) -> None:
    """Recursively builds a Tree with directory contents."""
    paths = sorted(
        pathlib.Path(directory).iterdir(),
        key=lambda path: (path.is_file(), path.name.lower()),
    )

    for path in paths:
        if path.name.startswith("."):
            continue

        if path.is_dir():
            style = "dim" if path.name.startswith("__") else ""
            branch = tree.add(
                f"[bold magenta]:open_file_folder: [link file://{path}]{escape(path.name)}",
                style=style,
                guide_style=style,
            )
            walk_directory(path, branch)
        else:
            tree.add(create_file_text(path))


def main(directory: str) -> None:
    """Main function to display a directory tree."""
    tree = Tree(
        f":open_file_folder: [link file://{directory}]{directory}",
        guide_style="bold bright_blue",
    )
    walk_directory(pathlib.Path(directory), tree)
    print(tree)


if __name__ == "__main__":
    try:
        directory = os.path.abspath(sys.argv[1])
    except IndexError:
        print("[b]Usage:[/] python list_directory_tree.py <DIRECTORY>")
    else:
        main(directory)

```
### 10. `requirements.txt`

#### `requirements.txt`

```text
certifi==2022.12.7
charset-normalizer==3.1.0
columnize==0.3.11
idna==3.4
isort==5.12.0
markdown-it-py==2.2.0
mdurl==0.1.2
pyglet==2.0.5
Pygments==2.15.0
pywin32==306
requests==2.28.2
rich==13.3.4
urllib3==1.26.15

```
