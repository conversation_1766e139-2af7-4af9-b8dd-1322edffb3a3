<!-- 'https://chatgpt.com/c/6742eec4-e1e0-8008-838c-c83a6b6c95b2' -->

it was a bad idea to output markdown-syntax directly to the terminal. a better idea would be to simplify the terminal output to the user, and instead make changes to the generated files (`FilesToText__filenames_OLD.txt` and `FilesToText__filenames_NEW.txt`). these could be changed to e.g. .md, then we could represent them like this (example):
```
[foldable-hash-string] [ ] └── gen_subdir
[foldable-hash-string] [-] │   ├── GenTech_MultiDoc_de.webm
[foldable-hash-string] [-] │   ├── GenTech_MultiDoc_en.webm
[foldable-hash-string] [-] │   ├── GenTech_MultiDoc_fr.webm
[foldable-hash-string] [-] │   ├── GenTech_MultiDoc_jp.webm
[foldable-hash-string] [-] │   ├── GetMobileApp.webm
[foldable-hash-string] [-] ├── Q3_2022_PrepareAForm.webm
[foldable-hash-string] [-] ├── SelectEditMenu.webm
[foldable-hash-string] [-] ├── SelectFinish.webm
[foldable-hash-string] [-] ├── SelectHighlightTool.webm
[foldable-hash-string] [-] ├── SelectSignMegaVerbShortTutorial.webm
[foldable-hash-string] [-] ├── SelectSignTool.webm
[foldable-hash-string] [-] ├── SelectTextTool.webm
[foldable-hash-string] [ ] └── subdir
[foldable-hash-string] [-] │   ├── LeaveFeedbackDesktop-DARK.webm
[foldable-hash-string] [-] │   ├── NewIcons_LeaveFeedback-DARK.webm
[foldable-hash-string] [-] │   ├── NewIcons_LeaveFeedback2-LIGHT.webm
[foldable-hash-string] [-] │   ├── NewIcons_NoMatches-DARK.webm
[foldable-hash-string] [-] │   ├── NewIcons_StartConversation-DARK.webm
[foldable-hash-string] [-] │   ├── NewIcons_StartConversation-LIGHT.webm
[foldable-hash-string] [-] │   ├── NoMatchesDesktop-DARK.webm
```
---

keep in mind the key points:
- Ensure that the overall architecture of script is concistent and well-structured.
- Ensure the code include only brief, high-value comments that clarify the purpose of sections or explain complex logic, avoiding excessive commentary.


